# coding=utf-8
import json

from django.conf import settings
from django.db import models
from django.utils import timezone

from conversaciones.medios import MedioDeConversacion
from conversaciones.models import Conversacion
from core.support import make_aware_when_is_naive
from occ.managers import MensajesChatManager
from occ.models.origen_de_mensajes import OrigenDesconocido
from occ.querysets.chats import ChatDeVentasQuerySet
from occ.soporte import TranscriptInterpreter
from occ.soporte_chat import VariablesDeContacto


class ChatDeVentas(models.Model):
    vendedor = models.ForeignKey('vendedores.Vendedor', null=True, related_name='chats_de_ventas')
    fecha = models.DateTimeField(auto_now_add=True, db_index=True)
    token = models.CharField(max_length=100, unique=True)
    variables = models.TextField(max_length=256, default='{}')
    telefono = models.CharField(max_length=64, blank=True, default='')
    celular = models.CharField(max_length=64, blank=True, default='')
    email = models.CharField(max_length=64, blank=True, default='')
    activo = models.BooleanField(default=True)

    objects = ChatDeVentasQuerySet.as_manager()

    def obtener_token(self):
        return self.token

    def marcas(self):
        variables_de_contacto = self.variables_de_contacto()
        return variables_de_contacto.marcas()

    def cliente_tiene_nombre_establecido(self):
        return self.variables_de_contacto().nombre != 'Posible Comprador'

    def nombre_del_cliente(self):
        if self.cliente_tiene_nombre_establecido():
            return self.variables_de_contacto().nombre()

    def esta_vigente(self):
        return self.activo

    def fue_convertido(self):
        return hasattr(self, 'chat_de_ventas_convertido')

    def fue_leido(self):
        return not self.mensajes.filter(fue_leido=False).exists()

    def finalizar(self):
        self.activo = False
        self.full_clean()
        self.save()

    def variables_de_contacto(self):
        variables_json = json.loads(self.variables)
        variables = VariablesDeContacto.nuevo_para(variables_json)
        return variables

    def actualizar_variables_de_contacto(self, variables_de_contacto):
        self.set_variables_de_contacto(variables_de_contacto)
        self.full_clean()
        self.save()

    def set_variables_de_contacto(self, variables_de_contacto):
        self.telefono = variables_de_contacto.telefono()
        self.celular = variables_de_contacto.celular()
        self.email = variables_de_contacto.email()
        self.variables = json.dumps(variables_de_contacto.as_json())

    def agregar_envio_con(self, texto):
        mensaje = MensajeDeChat.nuevo_envio(texto, self)
        return mensaje

    def agregar_respuesta_con(self, texto):
        mensaje = MensajeDeChat.nueva_respuesta(texto, self)
        return mensaje

    def agregar_respuesta_y_variables_de_contacto(self, texto, variables_de_contacto):
        mensaje = self.agregar_respuesta_con(texto)
        self.actualizar_variables_de_contacto(variables_de_contacto)
        return mensaje

    def agregar_mensaje(self, texto, emisor, fecha):
        mensaje = MensajeDeChat.nuevo_mensaje(self, texto, emisor, fecha)
        return mensaje

    def agregar_mensajes_desde(self, transcript, nombre_visitante):
        interpreter = TranscriptInterpreter()
        interpreter.agregar_mensajes_desde(transcript, nombre_visitante, self)

    def vista_previa_de_mensajes(self):
        return reversed(list(self.lista_de_mensajes().reverse()[:3]))

    def lista_de_mensajes(self):
        return self.mensajes.filter(eliminado=False)

    def tiene_mensajes(self):
        return self.lista_de_mensajes().exists()

    def mensajes_del_cliente(self):
        return self.mensajes.filter(emisor=MensajeDeChat.CLIENTE)

    def obtener_fecha_del_ultimo_mensaje(self):
        return self.mensajes.last().fecha

    def marcar_mensajes_como_leidos(self):
        self.mensajes.filter(fue_leido=False).update(fue_leido=True)

    @classmethod
    def nuevo_vacio(cls, vendedor, token):
        chat = cls.objects.create(vendedor=vendedor, token=token)
        return chat

    @classmethod
    def nuevo_para_pedido(cls, vendedor, token, pedido):
        chat = cls.nuevo_vacio(vendedor, token)
        variables_de_contacto = pedido.variables()
        chat.set_variables_de_contacto(variables_de_contacto)
        chat.full_clean()
        chat.save()
        return chat

    @classmethod
    def nuevo_con_envio_con(cls, texto, vendedor, token):
        chat = cls.nuevo_vacio(vendedor, token)
        chat.agregar_envio_con(texto)
        return chat


class ChatDeVentasConvertido(models.Model):
    prospecto = models.OneToOneField('prospectos.Prospecto', related_name='chat_de_ventas_convertido')
    chat = models.OneToOneField('ChatDeVentas', related_name='chat_de_ventas_convertido')
    conversacion_de_chat = models.OneToOneField('conversaciones.Conversacion', related_name='chat_de_ventas_convertido')

    @classmethod
    def nuevo(cls, prospecto, chat):
        if not chat.tiene_mensajes():
            raise ValueError('Imposible convertir un chat sin mensajes')
        conversacion_de_chat = cls._crear_conversacion_de_chat(prospecto, chat)
        chat_de_ventas_convertido = cls.objects.create(prospecto=prospecto, chat=chat,
                                                       conversacion_de_chat=conversacion_de_chat)
        return chat_de_ventas_convertido

    @classmethod
    def _crear_conversacion_de_chat(cls, prospecto, chat):
        ultimo_mensaje = chat.lista_de_mensajes().last()
        fecha_ultima_respuesta = cls._fecha_ultima_respuesta_de(chat)
        conversacion_de_chat = Conversacion.nuevo(tipo=Conversacion.TIPO_CHAT, prospecto=prospecto, eliminada=False,
                                                  fecha_ultima_respuesta=fecha_ultima_respuesta, fue_leida=True,
                                                  fecha_ultimo_mensaje=ultimo_mensaje.fecha)
        return conversacion_de_chat

    @classmethod
    def _fecha_ultima_respuesta_de(cls, chat):
        mensajes_del_cliente = chat.mensajes_del_cliente()
        if mensajes_del_cliente.exists():
            fecha_ultima_respuesta = mensajes_del_cliente.last().fecha
        else:
            fecha_ultima_respuesta = None
        return fecha_ultima_respuesta

    def marcar_como_leida(self):
        self.conversacion_de_chat.fue_leida = True
        self.conversacion_de_chat.full_clean()
        self.conversacion_de_chat.save()

    def es_de_whatsapp(self):
        return self.conversacion_de_chat.tipo == self.conversacion_de_chat.TIPO_WHATSAPP

    def tiene_telefono_para_whatsapp(self):
        return self.prospecto.tiene_telefono_para_whatsapp()

    def imagen_de_perfil(self):
        return self.prospecto.imagen_de_perfil()

    def nombre(self):
        return self.prospecto.nombre

    def fue_leida_o_es_vieja(self):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        return self.conversacion_de_chat.fue_leida or self.conversacion_de_chat.fecha_ultimo_mensaje < fecha_limite

    def mensajes(self, cantidad=None):
        medio = MedioDeConversacion.nuevo_para(self.conversacion_de_chat.tipo)
        mensajes = medio.mensajes_para(self.conversacion_de_chat, cantidad)
        return mensajes


class MensajeDeChat(models.Model):
    VENDEDOR = 'V'
    CLIENTE = 'C'
    OPCIONES_EMISOR = ((VENDEDOR, 'Vendedor'), (CLIENTE, 'Cliente'))

    chat = models.ForeignKey('ChatDeVentas', related_name='mensajes')
    texto = models.TextField(blank=True, default='')
    emisor = models.CharField(max_length=1, choices=OPCIONES_EMISOR, default=VENDEDOR)
    fecha = models.DateTimeField()
    eliminado = models.BooleanField(default=False)
    fue_leido = models.BooleanField(default=False)
    _origen = models.ForeignKey('occ.OrigenDeMensaje', related_name='_mensajes_chat')

    objects = MensajesChatManager()

    def origen(self):
        return self._origen

    def cambiar_origen(self, origen):
        self._origen = origen
        self.full_clean()
        self.save()

    def obtener_texto(self):
        return self.texto

    def obtener_fecha(self):
        return self.fecha

    def tipo_de_chat(self):
        return Conversacion.TIPO_CHAT

    def proveniente_de_cliente(self):
        return self.emisor == self.CLIENTE

    def nombre_de_emisor(self):
        if not(self.proveniente_de_cliente()):
            return self.chat.vendedor.full_name()
        else:
            return self.chat.variables_de_contacto().nombre()

    @classmethod
    def nuevo_envio(cls, texto, chat):
        mensaje = cls.nuevo_mensaje(chat, texto, cls.VENDEDOR, fue_leido=True)
        return mensaje

    @classmethod
    def nueva_respuesta(cls, texto, chat):
        mensaje = cls.nuevo_mensaje(chat, texto, cls.CLIENTE, fue_leido=False)
        return mensaje

    @classmethod
    def nuevo_mensaje(cls, chat, texto, emisor, fecha=None, fue_leido=False):
        if not fecha:
            fecha = timezone.now()

        fue_leido = fue_leido or emisor == cls.VENDEDOR
        fecha = make_aware_when_is_naive(fecha)
        mensaje = cls.objects.create(chat=chat, texto=texto, fecha=fecha, emisor=emisor,
                                     fue_leido=fue_leido, _origen=OrigenDesconocido.nuevo())
        return mensaje

    def __str__(self):
        mensaje_truncado = (self.texto[:15] + '...') if len(self.texto) > 15 else self.texto
        return 'Mensaje de chat - "%s"' % mensaje_truncado

    class Meta:
        verbose_name = 'Mensaje de chat'
        verbose_name_plural = 'Mensajes de chat'
        ordering = ('fecha', )


