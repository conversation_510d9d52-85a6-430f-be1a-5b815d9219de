import json
import logging
import random

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from rest_framework import status

from lib.api_client import ApiClient, ClientErrorResponse, UnexpectedStatus

logger = logging.getLogger(__name__)


class ClienteEAvisos(ApiClient):
    _HEADERS = {'Content-type': 'application/json', 'Accept': 'application/json, text/javascript'}

    def __init__(self, url, access_token):
        url = url + '?access_token=%s' % access_token
        super(ClienteEAvisos, self).__init__(url, request_type=self.POST)

    def name(self):
        return 'EAvisos'

    def headers(self):
        return self._HEADERS

    def _post(self, url, headers, data, **kwargs):
        return requests.post(url=url, json=data, headers=headers, **kwargs)

    def _send_request(self, headers, request_data, **kwargs):
        response = super(ClienteEAvisos, self)._send_request(headers, request_data, **kwargs)
        self._validate_response_status(request_data, response)
        response_dict = self._response_dict_from(response, request_data)
        return response_dict

    def _validate_response_status(self, request_data, response):
        is_success = response.status_code in [status.HTTP_201_CREATED, status.HTTP_200_OK]
        if not is_success:
            raise UnexpectedStatus(
                message='The response status should be 200(OK) / 201 (Created) but is %s' %
                        response.status_code,
                request=request_data, response=response)

    def _response_dict_from(self, response, request):
        # Se espera un response de la pinta {"id_tran": 4}

        response_dict = self._response_dict(response)
        id_tran = response_dict.get('id_tran')
        if not id_tran:
            raise ClientErrorResponse(
                message="Respuesta inesperada", data=response_dict, request=request, response=response)
        return response_dict

    def _response_dict(self, response):
        return json.loads(response.content)

    @classmethod
    def new_with(cls, url, access_token):
        return cls(url, access_token)


class ServicioDeEAvisos(object):

    def __init__(self, cliente=None):
        self._cliente = cliente or self._cliente_default()

    def enviar_respuesta(self, respuesta_de_mensaje):
        response_dict = self._enviar_respuesta(
            id_de_transaccion=respuesta_de_mensaje.mensaje_original().id_externo(),
            contenido=respuesta_de_mensaje.contenido())
        respuesta_de_mensaje.realizado_con(id_externo=response_dict.get('id_tran'))

    def marcar_respuesta_como_fallida(self, respuesta_de_mensaje):
        respuesta_de_mensaje.fallido()

    def _enviar_respuesta(self, id_de_transaccion, contenido):
        if contenido:
            request_data = self._request(id_de_transaccion, contenido)
            response = self._enviar_request(request_data)
            return response
        else:
            raise ValidationError('El contenido no puede ser vacio')

    def _request(self, id_de_transaccion, contenido):
        return {"id_tran": id_de_transaccion, "respuesta": contenido}

    def _enviar_request(self, request_data):
        return self._cliente.call(request_data)

    def _cliente_default(self):
        return ClienteEAvisos(
            url=settings.EAVISOS_PROVIDER_URL, access_token=settings.EAVISOS_PROVIDER_ACCESS_TOKEN)


class ServicioDeEAvisosMock(object):
    def enviar_respuesta(self, respuesta_de_mensaje):
        id_tran = random.randint(1, **********)
        respuesta_de_mensaje.realizado_con(id_externo=id_tran)
