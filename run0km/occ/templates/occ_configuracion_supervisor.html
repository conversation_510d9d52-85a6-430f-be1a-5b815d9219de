{% extends "occ_configuracion_layout.html" %}

{% block css %}
    {{ block.super }}
    <link href="{{ STATIC_URL }}css/occ-configuracion-popup.css" type="text/css" rel="stylesheet">
{% endblock %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript" src="{{ STATIC_URL }}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/occ-configuracion.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/configuracion-mensaje.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/configuracion_de_cuenta_de_email.js"></script>
    <script type="text/javascript">
        var configuracion_cuenta_url = "{% url 'administrar-cuentas-de-email' %}";

        $(document).ready(function () {
            var configuration = OCCConfiguration();
            {% if tiene_permiso_para_modificar_servicio_de_llamados %}
            configuration.addButton(
                'llamadas', 'spin_llamadas',
                {{staff_a_cargo_puede_utilizar_servicio_de_llamados|yesno:"true,false"}}, "{% url 'occ-servicio-de-llamadas' %}");
            {% endif %}
            {% if puede_crear_propuestas %}
            configuration.addButton(
                'propuestas', 'spin_propuestas',
                {{vendedores_pueden_crear_propuestas|yesno:"true,false"}}, "{% url 'occ-propuestas' %}");
            {% endif %}
            configuration.addButton(
                'whatsApp', 'spin_whatsapp', {{whatsapp_habilitado|yesno:"true,false"}}, "{% url 'occ-whatsapp' %}");
            configuration.addButton(
                'smsMasivo', 'spin_sms', {{sms_habilitado|yesno:"true,false"}}, "{% url 'occ-sms' %}");
            configuration.addButton(
                'redesSociales', 'spin_redes', {{redes_habilitado|yesno:"true,false"}}, "{% url 'occ-redes' %}");
            configuration.addButtonWithDependents(
                'notificaciones', 'spin_notificaciones',
                {{notificaciones_habilitadas|yesno:"true,false"}}, "{% url 'occ-notificaciones' %}",
                [$("#link-datos-de-contacto")]
            );
        });
        {% if abrir_configuracion_de_cuenta %}
            cambiarConfiguracionDeCuentasDeMail(
                configuracion_cuenta_url, 'spin_propuestas',
                {{ configuracion_de_cuenta_resultado|safe }}, 1);
        {% endif %}
    </script>
{% endblock %}

{% block solapas %}
    {% if puede_crear_propuestas %}
     <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas">Propuestas</a>
    {% endif %}
    <a href="{% url 'occ-campanias' %}">Campañas</a>
    <a href="{% url 'occ-configuracion' %}" class="activo">Configuración</a>
{% endblock %}

{% block configuracion_servicios  %}
    <div id="overlay" style="display:none" class="overlay-padding-fix"></div>

    {% if tiene_permiso_para_modificar_servicio_de_llamados %}
    <li id="llamadas" class="llamadas">
        <h1>Permite a tus vendedores hacer llamadas desde la aplicación!</h1>
        <button><label><span></span> Llamados por IP</label></button>
        <input type="hidden" value="0"/>
        <div id="spin_llamadas" class='spin-holder'></div>
    </li>
    {% endif %}

    <li id="propuestas" class="propuestas">
        <h1>Habilita la creación de propuestas para tus vendedores.</h1>
            {% if puede_crear_propuestas %}
                <button><label><span>Actovar</span></label></button>
            {% else %}
                <button disabled><label><span>Activar</span></label></button>
            {% endif %}
            <input type="hidden" value="0"/>
            <a class="link-popup" id="link-popup-configurar-cuenta"
               href="javascript:cambiarConfiguracionDeCuentasDeMail(configuracion_cuenta_url, 'spin_propuestas', null)">
                Administrar cuentas de email
            </a>
        <div id="spin_propuestas" class='spin-holder'></div>
    </li>

    <li id="whatsApp" class="whatsApp">
        <h1>Ahora podés chatear por WhatsApp desde acá.<br/>Activalo ahora!</h1>
        {% if whatsapp_de_concesionaria_habilitado %}
            <button><label><span></span> Chat para tus vendedores</label></button>
        {% else %}
            <button disabled><label><span></span> Chat para tus vendedores</label></button>
        {% endif %}
        <input type="hidden" value="0"/>
        <div id="spin_whatsapp" class='spin-holder'></div>
    </li>

    <li id="notificaciones" class="notificaciones">
        <h1>Recibí notificaciones del sistema.<br/>Activalo ahora!</h1>
        <button id="boton-habilitar-notificaciones"><label><span></span> Notificaciones</label></button>
        <input type="hidden" value="0"/>
        <a id="link-datos-de-contacto" class="link-popup" href="{% url 'datos-de-contacto' %}" style="display: none;">
                Configurar mis datos
        </a>
        <div id="spin_notificaciones" class='spin-holder'></div>
    </li>

    <li id="smsMasivo" class="smsMasivo">
        <h1>SMS Masivos para comunicarse con los incontactables.<br/>Integralo con el Delivery ahora!</h1>
        <button><label><span></span> SMS Masivo</label></button>
        <input type="hidden" value="0"/>
        <div id="spin_sms" class='spin-holder'></div>
    </li>

    {#    <li id="encuesta" class="encuesta">#}
    {#        <h1>Encuesta de satisfacción mensual!<br/>Enterate como atienden tus vendedores a sus clientes.</h1>#}
    {#        <button><label><span></span> Encuesta</label></button>#}
    {#        <input type="hidden" value="0"/>#}
    {#    </li>#}

    <li id="redesSociales" class="redesSociales">
        <h1>Mejora el nivel de contactación entre tus vendedores y los prospectos! Buscar más contactos en las Redes
            Sociales!</h1>
        <button><label><span></span> búsqueda en Redes Sociales</label></button>
        <input type="hidden" value="0"/>
        <div id="spin_redes" class='spin-holder'></div>
    </li>

{% endblock %}