{% extends "occ_layout.html" %}
{% load static from staticfiles %}

{% block js %}
    {{ block.super }}
    <link href="{% static 'lib/DataTables/media/css/jquery.dataTables.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'lib/DataTables/extensions/TableTools/css/dataTables.tableTools.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/runkm-filtro-prospectos.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/filtros_administracion.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />

    <script type="text/javascript" src="{% static 'lib/DataTables/media/js/jquery.dataTables.js' %}?v={{ version }}"></script>
    <script type="text/javascript"
            src="{% static 'lib/DataTables/extensions/Scroller/js/dataTables.scroller.js' %}?v={{ version }}"></script>
    <script type="text/javascript"
            src="{%  static 'lib/DataTables/extensions/TableTools/js/dataTables.tableTools.js' %}?v={{ version }}"></script>

    <script type="text/javascript" src="{% static 'js/table.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/system_unavailable.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/filtro_prospectos.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/nueva_campania.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/validar_caracteres.js' %}?v={{ version }}"></script>

    <script type="text/javascript">
        var cantidad_filtrada_url = "{% url 'cantidad-filtrada' %}";
        var visualizar_filtrados_url = "{% url 'visualizar_filtrados' %}";
        $(InicializarFiltro);
        $(inicializarSelectionTable);
        $(InicializarFiltrosDeFechas);
    </script>
{% endblock %}

{% block occ_content %}

    <div id="visualizar_filtrados" title="Atención" style="display:none;">
        <table id="table_id" class="display" width="100%" cellspacing="0">
            <thead>
            <tr>
                <th></th>
                <th>Nombre</th>
                <th>Fecha</th>
                <th>Mensaje</th>
                <th>Campaña</th>
                <th>Provincia</th>
                <th>Vendedor asignado</th>
            </tr>
            </thead>
            <tfoot>
            <tr>
                <th></th>
                <th>Nombre</th>
                <th>Fecha</th>
                <th>Mensaje</th>
                <th>Campaña</th>
                <th>Provincia</th>
                <th>Vendedor asignado</th>
            </tr>
            </tfoot>
        </table>
        <p id="cantidad_seleccionados"></p>
    </div>

    <nav class="solapas">
        {% if user.vendedor.configuracion_de_servicios.puede_crear_propuestas %}
        <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas">Propuestas</a>
        {% endif %}
        {% if user.vendedor.configuracion_de_servicios.puede_modificar_servicio_de_chat %}
            <a href="{% url 'occ-mis-chats' %}">Mis Chats</a>
        {% endif %}
        {% if user.vendedor.configuracion_de_servicios.tiene_sms_habilitado or user.vendedor.tiene_al_menos_una_campania %}
        <a href="{% url 'occ-campanias' %}" class="activo">Campañas</a>
        {% endif %}
        <a href="{% url 'occ-configuracion' %}">Configuración</a>
    </nav>

    <div id="listaCampanas">
        <div id="botonesTop">
            <a href="{% url 'occ-campanias' %}" class="verResumenCampanas">Ver Resumen de Campañas</a>
        </div>
        <div class="contenedor-lista">
            <div class="titulo-lista">
                <h3>Crear Nueva Campaña - Crédito disponible:
                    <p style="display: inline; font-size: inherit; color: {% if user.vendedor.credito.cantidad > 0 %} white {% else %} red {% endif %};">
                        {{ user.vendedor.credito.cantidad }}
                    </p>
                </h3>
            </div>
            <div class="lista">

                {% include "mensajes.html" %}

                <form id="form_nueva_campania" class="form_filtros_administracion" method="POST" action="{% url 'occ-campania-nueva' %}">
                    {% csrf_token %}

                    {# Datos generales de campaña #}
                    <div id="datos_generales">
                        <div>
                            <label>Medio:</label>{{ form.medio }}
                            {{ form.medio.errors }}
                        </div>
                        <div>
                            <label>Mensaje:</label>{{ form.mensaje }}
                            {{ form.mensaje.errors }}
                        </div>
                        <div id="complete-message" class="validate-message">
                            <img id="imgEstado" src="{% static 'img/sms-successful.png' %}" name="imgEstado" width="16" height="16" />
                            <div style="display: inline;margin-left: 2px;"> Hola {prospecto}, <div id="complete-text" style="display: inline">{{ mensaje }}</div></div>
                        </div>
                        <script>
                            var static_url = "{{ STATIC_URL }}img/";
                            var element = $("#id_mensaje");
                            var validador = new CharacterValidator(element, $("#complete-text"), $("#imgEstado"), static_url);
                            element.keyup(function() {
                                validador.validate();
                            } );
                        </script>
                    </div>

                    {# Seleccion de prospectos #}
                    <div class="contenedor-filtros">
                        <h2>Seleccion de Prospectos</h2>
                        <input type="hidden" name="seleccion_de_prospectos" id="seleccion_de_prospectos" value="{}"/>
                        <div id="filtros">
	                        <div class="row">
		                        <div class="col-6">
                                    <div class="fila-filtro">
                                        <label for="tipo_origen">Tipo de origen:</label>
                                        <select name="tipo_origen" id="tipo_origen">
                                            <option value="">Todos</option>
                                            {% for tipo_de_origen in tipos_de_origen %}
                                                <option value="{{ tipo_de_origen.codigo }}">{{ tipo_de_origen.nombre }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="fila-filtro">
                                        <label for="filter_llamado">Por llamado:</label>
                                        <select id="filter_llamado" name="filetr_llamado">
                                            <option value="">Todos los prospectos</option>
                                            <option value="vencidos">Llamados Vencidos</option>
                                            <option value="futuros">Llamados hoy en adelante</option>
                                        </select>
                                    </div>
                                    <div class="fila-filtro-compuesta">
                                        <label for="fecha_desde">Desde:</label>
                                        <input type="text" name="fecha_desde" id="fecha_desde" autocomplete="off"/>

                                        <label for="fecha_hasta">Hasta:</label>
                                        <input type="text" name="fecha_hasta" id="fecha_hasta" autocomplete="off"/>
                                    </div>
                                    <div class="fila-filtro">
                                        <label for="filter_estado">Por estado:</label>
                                        <select id="filter_estado" name="filter_estado">
                                            <option value="N">Nuevo</option>
                                            <option value="activo">Activos (Nuevos + En Proceso)</option>
                                            <option value="no_vendido" selected="selected">Todos (menos Vendidos)</option>
                                            <option value="P">En proceso</option>
                                            <option value="F">Finalizado</option>
                                            <option value="V">Vendido</option>
                                        </select>
                                    </div>
                                    <div class="fila-filtro" id="holder_motivo_de_finalizacion" style="display: none;">
                                        <label for="motivo_de_finalizacion" >Motivo:</label>
                                        <select name="motivo_de_finalizacion" id="motivo_de_finalizacion">
                                                <option value="">Todos</option>
                                            {% for motivo in motivos_de_finalizacion %}
                                                <option value="{{ motivo.id }}">{{ motivo.descripcion }}</option>
                                            {% endfor %}
                                                <option value="0">Otro</option>
                                        </select>
                                   </div>
                                    <div class="fila-filtro" id="holder_inactividad" style="display: none;">
                                        <label for="inactividad">Días sin actividad:</label>
                                        <select id="inactividad" name="inactividad">
                                            <option value="">Indistinto</option>
                                            <option value="3">3 días</option>
                                            <option value="5">5 días</option>
                                            <option value="mas">10 o más</option>
                                        </select>
                                    </div>
                                    <div class="fila-filtro" id="holder-dias-en-reposo" style="display: none;">
                                        <label for="dias-en-reposo">Días en reposo:</label>
                                        <select id="dias-en-reposo" name="dias-en-reposo">
                                            <option value="">Indistinto</option>
                                            <option value="menor-a-5">Menor a 5 días</option>
                                            <option value="entre-5-y-10">Entre 5 y 10 días</option>
                                            <option value="mas-de-10">Mayor a 10 días</option>
                                        </select>
                                    </div>
                                    {% if user.vendedor.es_supervisor %}
                                        <div class="fila-filtro">
                                            <label for="asignados">Asignados:</label>
                                            <select name="asignados" id="asignados">
                                                <option value="">Sin Asignar</option>
                                                <option {% if user.vendedor.es_supervisor %}selected="selected" {% endif %}value="si">Asignados</option>
                                            </select>
                                        </div>
                                        <div id='holder-vendedores' style="display:none" class="fila-filtro">
                                            <label for="vendedores">Vendedor Asignado:</label>
                                            <select name="vendedores" id="vendedores" multiple="multiple"
                                                    size=5 disabled="disabled">
                                                <option value="{{ user.vendedor.id }}"
                                                        selected="selected">{{ user.get_full_name }}</option>
                                                {% for vendedor in user.vendedor.vendedores.all %}
                                                    <option value="{{ vendedor.id }}"
                                                            selected="selected">{{ vendedor.user.get_full_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    {% else %}
                                        <input type="hidden" name="asignados" id="asignados" value="si">
                                        <input type="hidden" name="vendedores" id="vendedores" value="{{ user.vendedor.id }}">
                                    {% endif %}
                                    <div class="fila-filtro">
                                        <label for="marcas">{{ rubro.etiqueta_marca.title }}:</label>
                                        <select name="marcas" id="marcas" multiple="multiple" size="5">
                                            {% for marca in marcas %}
                                                <option value="{{ marca.id }}" selected="selected">
                                                    {{ marca.nombre }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="fila-filtro">
                                        <label for="modelos">{{ rubro.etiqueta_modelo.title }}:</label>
                                        <select name="modelos" id="modelos" multiple="multiple" size="5">
                                            <option value="sin-dato" selected="selected">
                                                Sin {{ rubro.etiqueta_modelo.lower }}</option>
                                            {% for modelo in modelos %}
                                                <option value={{ modelo.id }} selected="selected">
                                                    {{ modelo.nombre }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="fila-filtro">
                                        <label for="campania">Campaña:</label>
                                        <select name="campania" id="campania">
                                            <option value="">Todas</option>
                                            {% for campania in campanias %}
                                                <option value="{{ campania.id }}"
                                                        data-origen="{{ campania.categoria.tipo_de_origen.codigo }}">
                                                    {{ campania.nombre }} ({{ campania.nombre_origen }})
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    {% if user.vendedor.es_supervisor %}
                                        <div class="fila-filtro">
                                            <label for="exportados">Exportados a Excel:</label>
                                            <select name="exportados" id="exportados">
                                                <option value="no">No</option>
                                                <option value="si" id='exportados_si'>Si</option>
                                                <option value="" selected="selected" id='exportados_in'>Indistinto</option>
                                            </select>
                                        </div>
                                    {% endif %}
                                    <div class="fila-filtro">
                                        <label for="dias_sin_sms">Días sin contacto por sms:</label>
                                        <select name="dias_sin_sms" id="dias_sin_sms">
                                            <option value="" selected="selected">Indistinto</option>
                                            <option value="7" id='dias_sin_sms_7'>7 días</option>
                                            <option value="30" id='dias_sin_sms_30'>30 días</option>
                                            <option value="90" id='dias_sin_sms_90'>90 días</option>
                                        </select>
                                    </div>

                                    <div class="fila-filtro">
                                        <label for="prefijos">Prefijo:</label>
                                        <select name="prefijos" id="prefijos" multiple="multiple" size="5">
                                            {% for prefijo in prefijos %}
                                                {% if prefijo %}
                                                    <option value="{{ prefijo }}" selected="selected">{{ prefijo }}</option>
                                                {% else %}
                                                    <option value="sin-dato" selected="selected">Sin prefijo</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="fila-filtro">
                                        <label for="provincias">Provincia:</label>
                                        <select name="provincias" id="provincias" multiple="multiple" size="5">
                                            {% for provincia in provincias %}
                                                {% if provincia %}
                                                    <option value="{{ provincia }}" selected="selected">{{ provincia }}</option>
                                                {% else %}
                                                    <option value="sin-dato" selected="selected">Sin provincia</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="fila-filtro">
                                        <label for="etiquetas">Etiquetas:</label>
                                        <select name="etiquetas" id="etiquetas" multiple="multiple" size="5">
                                            <option value='' selected="selected">Sin etiqueta</option>
                                            {% for etiqueta in etiquetas %}
                                                {% if etiqueta %}
                                                    <option value="{{ etiqueta }}" selected="selected">{{ etiqueta }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- Fin filtros-->
                        <div id="contenedor-cantidad">
                            <div class="center">
                                {{ form.cantidad.errors }}
                                <lu id='error_cantidad' style="display:none" class='errorlist'>
                                    <li>Ingrese una cantidad válida.</li>
                                </lu>
                                <b>
                                    <label for="cantidad">Cantidad:</label>
                                    <a id="quitar_filtro_de_seleccion_button" href="javascript:quitarSeleccionDeFiltrados()"
                                       title="Quitar Selección" style="display:none">
                                        <img src="{% static 'img/remove-selection.png' %}" alt="Quitar Selección"/>
                                    </a>
                                    {{ form.cantidad }} de <span id="total_filtrado">{{ total_filtrados }}</span>

                                    <div id="spin_cantidad" class='spin-holder'></div>
                                    <a id="filtro_de_seleccion_button" href="javascript:seleccionDeFiltrados()"
                                       title="Selección de prospectos">Visualizar</a>
                                </b>
                            </div>
                        </div>
                        <!-- -->
                        <div style="text-align:center;">
                            <div id="spin_crear" class='spin-holder'></div>
                            {% if user.is_vendedor %}
                                {% if user.vendedor.credito.cantidad > 0 %}
                                    <input id='crear_campania' class="boton-default transparente" type="button"
                                           value="Crear" onclick="Submit()"/>
                                {% else %}
                                    <input id='crear_campania' class="boton-default transparente" type="button"
                                           value="Crear"
                                           onclick="$.growl.notice({title: 'Advertencia', message: 'Usted no posee crédito suficiente para crear una campaña'});"
                                           title="Usted no posee crédito suficiente para crear una campaña"/>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div> <!-- Fin contenedor-filtros -->
                </form>


            </div>
        </div>
    </div>

{% endblock %}
