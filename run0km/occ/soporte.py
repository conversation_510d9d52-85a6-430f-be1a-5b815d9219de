# coding=utf-8
import re
from datetime import timedelta

from django.conf import settings
from django.utils.html import escape
from django.utils.timezone import datetime, now, localtime, make_aware


class MockChatProviderService(object):
    def __init__(self, assign_operator_url, send_message_url):
        pass

    def assign_operator(self, operator, token, messages_url, marca):
        response = self.__class__.bind_response_example()
        return response

    def send_message(self, token, message):
        pass

    @classmethod
    def respuesta_bind_operador_para(cls, response_json):
        from occ.soporte_chat import RespuestaBindOperador
        response = RespuestaBindOperador.nuevo_para(response_json)
        return response

    @classmethod
    def bind_false_response(cls):
        response_json = cls._json_binded_false_example()
        response = cls.respuesta_bind_operador_para(response_json)
        return response

    @classmethod
    def bind_response_example(cls):
        response_json = cls._json_bind_response_example()
        response = cls.respuesta_bind_operador_para(response_json)
        return response

    @classmethod
    def _json_binded_false_example(cls):
        return {"IsBinded": False,"Reason":"La sesión ya ha sido asignada a un operador.",
                "Transcript": None,"Variables": None}

    @classmethod
    def _json_bind_response_example(cls):
        # Ejemplo bind fallido
        # {"IsBinded": false, "Reason": "Sesión no encontrada.", "Transcript": null, "Variables": null}
        return {
            "IsBinded": True,
            "Reason": "Asignación exitosa.",
            "Transcript": [
                "10:57:50", "B: (+5s) Hola, te puedo ayudar en algo?",
                "B: (+30s) Te interesa alguna marca en particular?",
                "B: (+30s) Tenemos las siguientes marcas para ofrecerte: Volkswagen, Fiat, Ford, Renault, Chevrolet, "
                "Citroen, Peugeot, Toyota. Querés que te mande más información? de cual?",
                "U: (+228s) me interesa saber como es el plan ,tengo mi auto usado un Chevrolet vectra modelo 97",
                "B: (+14s) Querés que te mande más info de Chevrolet  por whatsapp?",
                "B: (+31s)  te transfiero con Jessica Valenzuela, especialista en Chevrolet. Saludos!"],
            "Variables": {
                "Telefono": "",
                "Celular": "",
                "Email": "",
                "Nombre": "",
                "Modelo": "",
                "Marca": "Chevrolet",
                "Provincia": "",
                "OperatorUsername": "jvalenzuela",
                "OperatorDisplayName": "Jessica Valenzuela",
                "Country": "Argentina",
                "Location": "Buenos Aires",
                "Browser": "Chrome Compatibility Mode 30.0 on mobile platform Android version 4.4.2",
                "IP": "*************",
                "AtUrl": "http:\/\/precios-claros.org\/?source=community-f"
            }
        }


class Cinta(object):
    def __init__(self, indice_inicial, tamanio_ventana, lista):
        super(Cinta, self).__init__()
        self._indice_inicial = indice_inicial
        self._tamanio_ventana = tamanio_ventana
        self._lista = lista

    def siguiente_ventana(self):
        tamanio = len(self._lista)
        cantidad = min(self._tamanio_ventana, tamanio)
        indice_final = (self._indice_inicial + cantidad) % tamanio

        if indice_final == self._indice_inicial:
            ventana = list(self._lista)
        elif indice_final < self._indice_inicial:
            ventana = self._lista[0:indice_final] + self._lista[self._indice_inicial:tamanio+1]
        else:  #indice_final > self._indice_inicial:
            ventana = self._lista[self._indice_inicial:indice_final]
        return ventana

    @classmethod
    def nueva(cls, indice_inicial, tamanio_ventana, lista):
        return cls(indice_inicial, tamanio_ventana, lista)

    @classmethod
    def nueva_para_iteraciones(cls, cantidad_de_iteraciones, tamanio_ventana, lista):
        tamanio = len(lista)
        tamanio_ventana_acotado = min(tamanio, tamanio_ventana)
        indice_inicial = (cantidad_de_iteraciones * tamanio_ventana_acotado) % tamanio
        cinta = cls.nueva(indice_inicial, tamanio_ventana, lista)
        return cinta


class TranscriptInterpreterError(Exception):
    @classmethod
    def formato_inesperado(cls):
        return cls('Formato inesperado')


class TranscriptInterpreter(object):
    MENSAJE_RE = r'(?P<emisor>^[U|B]): \(\+(?P<hora>.*)s\) (?P<texto>.*)'
    BOT_CLAVE = 'B'
    VISITANTE_CLAVE = 'U'

    def __init__(self):
        super(TranscriptInterpreter, self).__init__()
        self._nombre_bot = settings.CHAT_NOMBRE_BOT

    def agregar_mensajes_desde(self, transcript, nombre_visitante, chat):
        mensajes = self.interpretar_mensajes_desde(transcript, self._nombre_bot, nombre_visitante)
        for mensaje in mensajes:
            chat.agregar_mensaje(texto=mensaje['texto'],
                                 emisor=mensaje['emisor'],
                                 fecha=mensaje['hora'])

    def interpretar_mensaje_desde(self, linea, datetime_base, nombre_bot, nombre_usuario):
        # u'B: (+5s) Hola, te puedo ayudar en algo?'
        # u'U: (+21s) Hola, queria saber un poco mas sobre este plan..',

        patron = re.compile(self.MENSAJE_RE)
        matcher = patron.search(linea)
        if matcher:
            mensaje = matcher.groupdict()
            emisor, nombre_emisor = self._obtener_emisor(mensaje, nombre_bot, nombre_usuario)
            mensaje['emisor'] = emisor
            mensaje['hora'] = self._hora_de_mensaje(mensaje, datetime_base)
            mensaje['texto'] = self._formatear_mensaje(mensaje, nombre_emisor)
        else:
            raise TranscriptInterpreterError.formato_inesperado()

        return mensaje

    def interpretar_mensajes_desde(self, transcript, nombre_bot, nombre_usuario):
        iter_trasncript = iter(transcript)
        hora_base = next(iter_trasncript)
        date_time = self._obtener_hora_base(hora_base)
        mensajes = []
        for linea in iter_trasncript:
            try:
                mensaje = self.interpretar_mensaje_desde(linea, date_time, nombre_bot, nombre_usuario)
                mensajes.append(mensaje)
                date_time = mensaje['hora']
            except TranscriptInterpreterError:
                pass
        return mensajes

    def _obtener_hora_base(self, hora_base):
        hoy = localtime(now())
        try:
            date_time = datetime.strptime(hora_base or '', "%H:%M:%S").time()
        except ValueError:
            date_time = hoy.time()
        date_time = make_aware(datetime.combine(hoy, date_time))
        return date_time

    def _formatear_mensaje(self, mensaje, nombre_emisor):
        hora = self._imprimir(mensaje['hora'])
        return '[%s] %s: %s' % (hora, nombre_emisor, mensaje['texto'])

    def _hora_de_mensaje(self, mensaje, datetime_base):
        segundo_string = mensaje['hora']
        try:
            segundos_int = int(segundo_string)
        except ValueError:
            segundos_int = 0

        segundos = timedelta(seconds=segundos_int)
        return datetime_base + segundos

    def _imprimir(self, date_time):
        return date_time.strftime('%H:%M:%S')

    def _obtener_emisor(self, mensaje, nombre_bot, nombre_usuario):
        emisor = mensaje['emisor']
        from occ.models import MensajeDeChat
        if emisor == self.BOT_CLAVE:
            return MensajeDeChat.VENDEDOR, nombre_bot
        if emisor == self.VISITANTE_CLAVE:
            return MensajeDeChat.CLIENTE, nombre_usuario
        return ''


class MensajeRenderer(object):

    def __init__(self, mensaje):
        super(MensajeRenderer, self).__init__()
        self._mensaje = mensaje

    def render(self):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def para(cls, mensaje):
        for renderer in cls.__subclasses__():
            if renderer.puede_dibujar(mensaje):
                return renderer(mensaje)

    @classmethod
    def puede_dibujar(cls, mensaje):
        return NotImplementedError("Subclass Responsibility")


class MensajeComoTextoRenderer(MensajeRenderer):
    def render(self):
        return escape(self._mensaje.obtener_texto())

    @classmethod
    def puede_dibujar(cls, mensaje):
        return not mensaje.origen().proveniente_de_propuestas()


class MensajeAPropuestaRenderer(MensajeRenderer):
    def render(self):
        propuesta = self._mensaje.origen().propuesta()
        return self.__class__.render_mensaje(propuesta)

    @classmethod
    def render_mensaje(cls, propuesta):
        url = settings.PROPUESTAS_URL_DETALLE % propuesta.identificador_externo()
        return 'Propuesta: <a href="%s" title="Detalle de la propuesta">%s</a>' % (url, escape(propuesta.titulo()))

    @classmethod
    def puede_dibujar(cls, mensaje):
        return mensaje.origen().proveniente_de_propuestas()


class OrigenDesconocido(object):

    @classmethod
    def nuevo(cls):
        return cls()

    def proveniente_de_propuestas(self):
        return False
