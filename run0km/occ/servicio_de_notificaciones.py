# coding=utf-8
from django.conf import settings

from core.models import Sistema
from mobileapi.communication import CommunicationChannel


class ErrorDelServicioDeNotificaciones(Exception):
    pass


ACTIVITY_VERBS = {
    # IMPORTANT MAX 20 CHARS PER VERB (DICT VALUE)
    'INICIAR_COMPULSA': 'iniciar-compulsa',
    'CERRAR_COMPULSA': 'cerrar-compulsa',
    'INICIO_CHAT': 'inicio-chat',
    'SESION_EXPIRADA_DE_CHAT': 'chat-expirado',
    'MENSAJE_CHAT': 'mensaje-chat',
    'FINALIZAR_CHAT': 'finalizar-chat',
    'PROSPECTO_ASIGNADO': 'prospecto-asignado',
    'ENTREGA_DE_PROSPECTO_PETICIONADO': 'entrega-de-prospecto-peticionado',
    'ENTREGA_FALLIDA_DE_PROSPECTO_PETICIONADO': 'entrega-fallida-de-prospecto-peticionado'
}


class ServicioDeNotificaciones(object):
    CHANNEL_NAME_TEMPLATE = 'vendedor-%s'

    def __init__(self):
        self._sistema = Sistema.instance()

    def enviar_participacion_a_compulsa(self, compulsa, a_vendedores):
        variables = compulsa.variables()
        data_adicional = {
            'id': 1,
            'client':
                {
                    'name': variables.nombre(),
                    'location': variables.location(),
                    'campaign': compulsa.campania.nombre,
                    'channel': variables.canal(),
                },
            'timeout': self._sistema.timeout_compulsa
        }
        self._enviar_como_sistema(
            a_vendedores,
            ACTIVITY_VERBS['INICIAR_COMPULSA'],
            compulsa,
            data_adicional=data_adicional
        )

    def enviar_cierre_de_compulsa(self, finalizacion_exitosa):
        a_vendedores = finalizacion_exitosa.perdedores()
        compulsa = finalizacion_exitosa.compulsa
        self._enviar_como_sistema(a_vendedores, ACTIVITY_VERBS['CERRAR_COMPULSA'], compulsa)

    def enviar_inicio_de_chat(self, a_ganador, chat):
        data = {'id': chat.id}
        self._enviar_como_sistema([a_ganador], ACTIVITY_VERBS['INICIO_CHAT'], chat, data_adicional=data)

    def enviar_sesion_expirada_de_chat(self, vendedor):
        self._enviar_como_sistema([vendedor], ACTIVITY_VERBS['SESION_EXPIRADA_DE_CHAT'], vendedor, data_adicional={})

    def enviar_finalizacion_de_chat(self, a_vendedor, chat):
        self._enviar_como_sistema([a_vendedor], ACTIVITY_VERBS['FINALIZAR_CHAT'], chat)

    def enviar_mensaje_de_chat(self, a_vendedor, texto, fecha, parametros, chat):
        self._enviar_como_sistema([a_vendedor], ACTIVITY_VERBS['MENSAJE_CHAT'], chat,
                                  data_adicional={'mensaje': {'texto': texto, 'fecha': fecha},
                                                  'parametros': parametros})

    def enviar_prospectos_asignado(self, a_vendedor):
        self._enviar_como_sistema([a_vendedor], ACTIVITY_VERBS['PROSPECTO_ASIGNADO'], a_vendedor, data_adicional={})

    def enviar_entrega_de_prospecto_peticionado_por_vendedor(self, a_vendedor):
        self._enviar_como_sistema([a_vendedor], ACTIVITY_VERBS['ENTREGA_DE_PROSPECTO_PETICIONADO'], a_vendedor, data_adicional={})

    def enviar_entrega_fallida_de_prospecto_peticionado_por_vendedor(self, a_vendedor, detalle):
        self._enviar_como_sistema([a_vendedor], ACTIVITY_VERBS['ENTREGA_FALLIDA_DE_PROSPECTO_PETICIONADO'], a_vendedor, data_adicional={'detalle':detalle})

    def _enviar_como_sistema(self, a_vendedores, verbo, objeto, data_adicional=None):
        return self._enviar(a_vendedores, self._sistema, verbo, objeto, data_adicional)

    def _enviar(self, a_vendedores, actor, verbo, objeto, data_adicional=None):
        # parametro `actor` es aquel que emite la notificacion. no sirve para Pusher

        data_adicional = data_adicional or {}
        data_adicional['id'] = objeto.id
        canal = CommunicationChannel.new_with(settings.PUSHER_APP_ID,
                                              settings.PUSHER_KEY,
                                              settings.PUSHER_SECRET)
        canales = [self.nombre_de_canal_para(vendedor) for vendedor in a_vendedores]
        self._enviar_en_batch(canal, canales, data_adicional, verbo)

    def _enviar_en_batch(self, canal, canales, data_adicional, verbo):
        "PARCHE porque pusher lanza ComunicationChannelException: Batch too large (12 > 10)"
        limite = 10
        for indice in range(0, len(canales), limite):
            canales_a_enviar = canales[indice:indice+limite]
            canal.send_message_batch(channels=canales_a_enviar, event_name=verbo, data=data_adicional)

    def nombre_de_canal_para(self, vendedor):
        return self.CHANNEL_NAME_TEMPLATE % vendedor.pk
