from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
from occ.models import OrigenPropuesta
from whatsapp.service import WhatsappService


class ServicioDeWhatsappParaProspecto(WhatsappService):

    def _assert_message(self, message):
        if not hasattr(message, 'prospecto'):
            raise ValueError('El mensaje debe saber responder #prospecto')

    def _create_message(self, message, token):
        return MensajesWhatsapp.nuevo_mensaje(message.prospecto(), message.text, message.phone)


class ServicioDeWhatsappParaPropuesta(ServicioDeWhatsappParaProspecto):

    def _assert_message(self, message):
        super()._assert_message(message)
        if not hasattr(message, 'propuesta'):
            raise ValueError('El mensaje debe saber responder #propuesta')

    def _create_message(self, message, token):
        envio_de_mensaje = super()._create_message(message, token)
        OrigenPropuesta.nuevo_mensaje(envio_de_mensaje, message.propuesta())
        return envio_de_mensaje


