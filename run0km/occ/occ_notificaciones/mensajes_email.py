from email.mime.text import MIMEText


class MensajeDeEmail(object):
    def __init__(self, contenido, destinatario):
        super(MensajeDeEmail, self).__init__()
        self._contenido = contenido
        self._destinatario = destinatario

    def cuerpo(self):
        return self._contenido.cuerpo()

    def as_mime(self):
        message = MIMEText(self._contenido.cuerpo(), 'html', 'utf-8')
        message['to'] = self._destinatario
        message['from'] = self._contenido.remitente()
        message['subject'] = self._contenido.asunto()
        return message
