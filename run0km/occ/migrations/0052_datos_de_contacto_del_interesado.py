

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('occ', '0051_llamadarealizadadeanura__audio_file'),
    ]

    operations = [

        migrations.CreateModel(
            name='DatosDeContactoDelInteresado',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_phone', models.CharField(blank=True, default=b'', max_length=64, null=True)),
                ('_email', models.EmailField(blank=True, default=b'', max_length=80, null=True)),
                ('_name', models.CharField(blank=True, default=b'', max_length=150, null=True)),
                ('_whatsapp', models.CharField(blank=True, default=b'', max_length=64, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),

        migrations.AddField(
            model_name='conversaciondeeavisos',
            name='_datos_de_contacto_del_interesado',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE,
                                    to='occ.DatosDeContactoDelInteresado'),
        ),
    ]
