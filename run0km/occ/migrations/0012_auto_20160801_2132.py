# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-08-02 00:32


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0029_auto_20160728_0934'),
        ('occ', '0011_compulsa__pedido'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatDeVentas',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('token', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='MensajeDeChat',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('texto', models.Char<PERSON>ield(blank=True, default=b'', max_length=255)),
                ('emisor', models.Char<PERSON>ield(choices=[(b'V', b'Vendedor'), (b'C', b'Cliente')], default=b'V', max_length=1)),
                ('fecha', models.DateTimeField()),
                ('eliminado', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Mensaje de chat',
                'verbose_name_plural': 'Mensajes de chat',
            },
        ),
        migrations.AddField(
            model_name='chatdeventas',
            name='mensajes',
            field=models.ManyToManyField(related_name='chat', to='occ.MensajeDeChat'),
        ),
        migrations.AddField(
            model_name='chatdeventas',
            name='vendedor',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='vendedores.Vendedor'),
        ),
    ]
