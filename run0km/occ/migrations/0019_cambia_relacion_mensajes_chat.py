# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-10-21 20:02


from django.db import migrations, models
import django.db.models.deletion
from django.db.models import F


def asociar_chat(apps, schema_editor):
    # MensajeDeChat: chat = models.ForeignKey('ChatDeVentas', related_name='mensajes')
    mensaje_de_chat_class = apps.get_model("occ", "MensajeDeChat")
    mensaje_de_chat_class.objects.update(chat=F('_chat'))


def asociar_chat_a_variable_temporal(apps, schema_editor):
    # ChatDeVentas: mensajes = models.ManyToManyField('MensajeDeChat', related_name='chat')
    # mensaje_de_chat_class = apps.get_model("occ", "MensajeDeChat")
    # mensaje_de_chat_class.objects.update(_chat=F('chat'))
    chat_de_ventas_class = apps.get_model("occ", "ChatDeVentas")
    for chat in chat_de_ventas_class.objects.all():
        chat.mensajes.update(_chat=chat)


def undo_asociar_chat(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('occ', '0018_chatdeventasconvertido'),
    ]

    operations = [
        migrations.AddField(
            model_name='mensajedechat',
            name='_chat',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='_mensajes', to='occ.ChatDeVentas'),
            preserve_default=False,
        ),
        migrations.RunPython(asociar_chat_a_variable_temporal, undo_asociar_chat),
        migrations.RemoveField(
            model_name='chatdeventas',
            name='mensajes',
        ),
        migrations.AddField(
            model_name='mensajedechat',
            name='chat',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='mensajes',
                                    to='occ.ChatDeVentas'),
            preserve_default=False,
        ),
        migrations.RunPython(asociar_chat, undo_asociar_chat),
    ]
