import mock
from django.utils.timezone import now, timedelta

from occ.asignacion_de_credito import AsignadorDeCreditoDeSMS
from occ.campania_envios_sms import EmisorDeCampaniasDeSMS
from occ.models import CreditoDeSMS
from lib.smscover.tests import SMSServiceAnswerMock
from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory


class CreditoDeSMSTest(BaseFixturedTest):
    def setUp(self):
        super(CreditoDeSMSTest, self).setUp()
        f = self.fixture
        hace_24 = now() + timedelta(days=-1, seconds=-1)
        cuatro_meses = timedelta(days=30 * 4)
        hace_cuatro_meses = now() - cuatro_meses
        self.p1 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=None,
                                    fecha_creacion=hace_cuatro_meses)
        self.p2 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                    fecha_creacion=hace_24)
        self.p3 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'], vendedor=f['sup_2'],
                                    fecha_creacion=hace_cuatro_meses)
        self.p4 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'], vendedor=f['sup_2'],
                                    fecha_creacion=hace_24)
        self.campania = self.fixture['cv_1']
        self.vendedor = self.fixture['vend_1']

        self.servicio_de_mensaje = EmisorDeCampaniasDeSMS()
        self.credito_base_anterior_a_envio = self.vendedor.credito.credito_base
        self.credito_extra_anterior_a_envio = self.vendedor.credito.credito_extra

    def _template_de_mensaje(self):
        template_de_mensaje = self.campania.mensaje
        return template_de_mensaje

    def _generar_pedido_de_envios(self):
        pedido_de_envios = self.servicio_de_mensaje._generar_pedidos_de_envios_para(self.campania,
                                                                                    self._template_de_mensaje())
        return pedido_de_envios

    def _configurar_credito(self, credito_base, credito_extra):
        credito = self.vendedor.credito
        credito.credito_base = credito_base
        credito.credito_extra = credito_extra
        credito.save()

        self.credito_base_anterior_a_envio = credito.credito_base
        self.credito_extra_anterior_a_envio = credito.credito_extra

    def _enviar_campania(self):
        return self.servicio_de_mensaje.enviar_campania(self.campania, self._template_de_mensaje(), self.vendedor)

    def assert_credito(self, vendedor, credito_base, credito_extra):
        credito = CreditoDeSMS.objects.get(vendedor=vendedor)  # hace el refresh

        self.assertEqual(credito.credito_base, credito_base)
        self.assertEqual(credito.credito_extra, credito_extra)

    def test_setea_credito_vendedor_correctamente(self):
        """
           Credito es actualizado en base a la cantidad de prospectos del vendedor y el credito extra es mantenido
        """
        tres_meses = timedelta(days=30 * 3)
        hace_tres_meses = now() - tres_meses
        vendedor = self.fixture['vend_1']
        prospectos = vendedor.prospectos.filter(asignacion__fecha_de_asignacion_a_vendedor__gte=hace_tres_meses)
        cant_prospectos_ult_tres_meses = prospectos.count()
        credito = vendedor.credito
        extra = 10
        credito.credito_extra = extra
        credito.save()

        AsignadorDeCreditoDeSMS().asignar_credito_mensual()
        self.assert_credito(vendedor, cant_prospectos_ult_tres_meses, extra)

    def test_setea_credito_supervisor_correctamente(self):
        """
           Credito es actualizado en base a la cantidad de prospectos del supervisor y el credito extra es mantenido
        """
        tres_meses = timedelta(days=30 * 3)
        hace_tres_meses = now() - tres_meses
        supervisor = self.fixture['sup_1']
        cant_prospectos_ult_tres_meses = supervisor.prospectos.filter(fecha_creacion__gte=hace_tres_meses).count()
        extra = 67
        credito = supervisor.credito
        credito.credito_extra = extra
        credito.save()

        AsignadorDeCreditoDeSMS().asignar_credito_mensual()
        self.assert_credito(supervisor, cant_prospectos_ult_tres_meses, extra)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_cantidad_de_mensajes_a_enviar_igual_a_credito_extra_con_credito_base_0_se_envia_y_se_actualiza_el_credito(
            self, mock_call):
        pedido_de_envios = self._generar_pedido_de_envios()
        self._configurar_credito(0, pedido_de_envios.cantidad_de_mensajes_a_enviar())

        resultado = self._enviar_campania()
        self.assertTrue(resultado.fue_exitoso())
        self.assert_credito(self.vendedor, 0, 0)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_cantidad_de_mensajes_a_enviar_igual_a_credito_base_y_extra_se_envia_y_se_actualiza_el_credito_a_cero(
            self, mock_call):
        pedido_de_envios = self._generar_pedido_de_envios()
        self._configurar_credito(1, pedido_de_envios.cantidad_de_mensajes_a_enviar()-1)

        resultado = self._enviar_campania()
        self.assertTrue(resultado.fue_exitoso())
        self.assert_credito(self.vendedor, 0, 0)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_cantidad_de_mensajes_a_enviar_igual_credito_base_y_con_credito_extra_se_envia_y_actualiza_el_credito(
            self, mock_call):
        pedido_de_envios = self._generar_pedido_de_envios()
        cantidad_envios = pedido_de_envios.cantidad_de_mensajes_a_enviar()
        self._configurar_credito(cantidad_envios, cantidad_envios)

        resultado = self._enviar_campania()
        self.assertTrue(resultado.fue_exitoso())
        self.assert_credito(self.vendedor, 0, cantidad_envios)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_cantidad_de_mensajes_a_enviar_mayor_a_credito_base_y_extra_no_se_envia_y_no_se_actualiza_el_credito(
            self, mock_call):
        pedido_de_envios = self._generar_pedido_de_envios()
        self._configurar_credito(1, pedido_de_envios.cantidad_de_mensajes_a_enviar()-2)

        resultado = self._enviar_campania()
        self.assertFalse(resultado.fue_exitoso())
        self.assertTrue(resultado.tiene_errores_de_validacion())
        self.assertIn('Credito insuficiente', resultado.errores_de_validacion())
        self.assert_credito(self.vendedor, 1, pedido_de_envios.cantidad_de_mensajes_a_enviar()-2)
