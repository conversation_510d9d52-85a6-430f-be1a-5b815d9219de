# coding=utf-8
import mock
from django.test import override_settings
from django.utils import timezone

from occ.models import EnvioDeMensaje, LogDeErrorActualizacionDeEstadoSMS, EnvioDeCampaniaDeSMS
from occ.sms_estrategias_de_envio import DeliverySMS
from lib.smscover.tests import SMSServiceAnswerMock, SMSQueryStateLotMock, SMSOutputQueryLotListMock, \
    SMSAnswerListMock, SMSCommunicationFailMock
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model


@override_settings(SMSCOVER_SMS_SENDER_CLASS='lib.smscover.SMSSender')
class ActualizacionDeEnvioDeCampaniaDeSMSTest(BaseFixturedTest):
    def setUp(self):
        super(ActualizacionDeEnvioDeCampaniaDeSMSTest, self).setUp()
        self.campania = self.fixture['cv_1']
        DeliverySMS.reset_instance()
        self.servicio_de_mensajes = DeliverySMS.default()
        self.cantidad_de_errores_inicial = LogDeErrorActualizacionDeEstadoSMS.objects.count()

    def _inicializar_fixture_envios(self):
        EnvioDeCampaniaDeSMS.nuevo(self.campania, self.campania.prospectos.all()[0], '1', '1510101010', 'hello 1510101010')
        EnvioDeCampaniaDeSMS.nuevo(self.campania, self.campania.prospectos.all()[1], '2', '1520202020', 'hello 1520202020')

    def _get_envio(self, id_mensaje, envios):
        for envio in list(envios):
            if envio.id_mensaje == id_mensaje:
                return envio
        raise ValueError('Envio no encontrado')

    def _client_lot_para(self, lot_request):
        return '0%s' % lot_request

    def _assert_envio(self, envio, telefono, mensaje, estado=EnvioDeMensaje.EN_PROCESO, fecha_envio=None):
        envio_reloaded = reload_model(envio)
        self.assertEqual(envio_reloaded.telefono, telefono)
        self.assertEqual(envio_reloaded.texto(), mensaje)
        self.assertEqual(envio_reloaded.estado, estado)
        if envio_reloaded.fue_exitoso():
            self._assert_datetime(envio_reloaded.fecha, fecha_envio)

    def _assert_datetime(self, fecha, fecha_esperada_string, formato='%Y/%m/%d'):
        self.assertEqual(timezone.localtime(fecha).strftime(formato), fecha_esperada_string.replace(".", ""))

    def _generar_mensaje_para_respuestas(self, total_resuestas,
                                         respuestas_actualizadas=None,
                                         respuestas_asociadas_a_envios_invalidos=0):
        if respuestas_actualizadas is None:
            respuestas_actualizadas = total_resuestas

        variables = {'total_resuestas': total_resuestas,
                     'respuestas_actualizadas': respuestas_actualizadas,
                     'respuestas_asociadas_a_envios_invalidos': respuestas_asociadas_a_envios_invalidos}

        mensaje_esperado = 'Se han recibido %(total_resuestas)s respuestas\n' \
                           'Respuestas Actualziadas correctamente: %(respuestas_actualizadas)s\n' \
                           'Respuestas asociadas a envios invalidos: %(respuestas_asociadas_a_envios_invalidos)s\n'\
                           % variables

        return mensaje_esperado

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSCommunicationFailMock().call)
    def test_si_falla_la_comunicacion_retorna_actualizacion_fallida(self, mock_call):
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial + 1,
                          'Deberia haberse registrado el error')

        log = LogDeErrorActualizacionDeEstadoSMS.objects.last()
        self.assertEqual(log.tipo, 'Error de comunicación')
        self.assertEqual(log.descripcion, 'Error al realizar la comunicación con el servicio')
        self.assertNotEqual(log.request, '')
        self.assertEqual(log.response, '')

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_actualizacion_estado_de_un_envio_sin_informacion(self, mock_call):
        self._inicializar_fixture_envios()
        envios = self.campania.envios.all()
        self.assertEqual(envios.count(), 2)
        self._assert_envio(self._get_envio('1', envios), '1510101010', 'hello 1510101010')
        self._assert_envio(self._get_envio('2', envios), '1520202020', 'hello 1520202020')

        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self.assertEqual(envios.count(), 2)
        self._assert_envio(self._get_envio('1', envios), '1510101010', 'hello 1510101010')
        self._assert_envio(self._get_envio('2', envios), '1520202020', 'hello 1520202020')

        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No deberia haberse registrado ningún error')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_un_envio_sin_respuestas(self, mock_call):
        self._inicializar_fixture_envios()
        # configura la respuesta del mock
        lot_output = SMSQueryStateLotMock(self._client_lot_para(self.campania.pk))

        sending_date = '2015/08/22'
        lot_output.add_query_sending('1', '1510101010', 1, sending_date)
        lot_output.add_query_sending('2', '1520202020', 0)
        query_output = SMSOutputQueryLotListMock.with_lots([lot_output])
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output).call

        envios = self.campania.envios.all()
        self.assertEqual(envios.count(), 2)
        self._assert_envio(self._get_envio('1', envios), '1510101010', 'hello 1510101010',
                           estado=EnvioDeMensaje.EN_PROCESO)
        self._assert_envio(self._get_envio('2', envios), '1520202020', 'hello 1520202020',
                           estado=EnvioDeMensaje.EN_PROCESO)

        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self._assert_envio(self._get_envio('1', envios), '1510101010', 'hello 1510101010',
                           estado=EnvioDeMensaje.REALIZADO, fecha_envio=sending_date)
        self._assert_envio(self._get_envio('2', envios), '1520202020', 'hello 1520202020',
                           estado=EnvioDeMensaje.FALLIDO)

        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No deberia haberse registrado ningún error')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_envio_con_lote_incorrecto(self, mock_call):
        # configura la respuesta del mock
        lot_output = SMSQueryStateLotMock(self._client_lot_para('123123211234'))

        sending_date = '2015/08/22'
        lot_output.add_query_sending('1', '1510101010', 1, sending_date)
        lot_output.add_query_sending('2', '1520202020', 0)
        query_output = SMSOutputQueryLotListMock.with_lots([lot_output])
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output).call

        envios = self.campania.envios
        self.assertFalse(envios.exists())
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self.assertFalse(envios.exists())
        # self._assert_mensaje_resultado_envio(mensaje_resultado, 2, 0, campanias_no_encontradas=1,
        #                                      pk_campanias_no_encontradas=['123123211234'])
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No deberia haberse registrado ningún error')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_un_envio_con_respuestas(self, mock_call):
        # configura la respuesta del mock
        sending_date = '2015/08/22'
        query_output = SMSOutputQueryLotListMock.with_sending(self._client_lot_para(self.campania.pk),
                                                              '1', '1510101010', 1, sending_date)

        fecha_respuesta = '01/01/2008 22:14:34'
        mensaje = 'Ack!!'
        answer_output = SMSAnswerListMock.with_answer('1', fecha_respuesta, '1510101010', mensaje)
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output,
                                                                        answer_list=answer_output).call
        self._inicializar_fixture_envios()
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        envios = self.campania.envios.all()
        envio_de_campania = self._get_envio('1', envios)
        self._assert_envio(envio_de_campania, telefono='1510101010', mensaje='hello 1510101010',
                           estado=EnvioDeMensaje.REALIZADO, fecha_envio=sending_date)

        respuestas = envio_de_campania.respuestas
        self.assertEqual(respuestas.count(), 1)
        respuesta = respuestas.first()
        self._assert_datetime(respuesta.fecha, fecha_respuesta.upper(), '%d/%m/%Y %H:%M:%S')
        self.assertEqual(respuesta.mensaje, mensaje)
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No deberia haberse registrado ningún error')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_un_envio_con_respuesta_con_formato_fecha_incorrecto(self, mock_call):
        # configura la respuesta del mock
        sending_date = '2015/08/22'
        query_output = SMSOutputQueryLotListMock.with_sending(self._client_lot_para(self.campania.pk),
                                                              '1', '1510101010', 1, sending_date)

        fecha_respuesta = '01/01/2008 22:14:34 p.m.'
        mensaje = 'Ack!!'
        answer_output = SMSAnswerListMock.with_answer('1', fecha_respuesta, '1510101010', mensaje)
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output,
                                                                        answer_list=answer_output).call
        self._inicializar_fixture_envios()
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()

        envios = self.campania.envios.all()
        envio_de_campania = self._get_envio('1', envios)
        self._assert_envio(envio_de_campania, telefono='1510101010', mensaje='hello 1510101010',
                           estado=EnvioDeMensaje.REALIZADO, fecha_envio=sending_date)

        respuestas = envio_de_campania.respuestas
        self.assertEqual(respuestas.count(), 1)
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No se deberia tener en cuenta los errores de parseo de fechas')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_un_envio_con_respuestas_no_asociada_a_envio(self, mock_call):
        # configura la respuesta del mock
        fecha_respuesta = '01/01/2008 22:14:34'  # Cambio!! '01/01/2008 22:14:34 p.m.'
        mensaje = 'Ack!!'
        answer_output = SMSAnswerListMock.with_answer('xxx', fecha_respuesta, '1510101010', mensaje)
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(answer_list=answer_output).call
        self._inicializar_fixture_envios()

        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial +1,
                          'Deberia haberse registrado la notificacion')

        log = LogDeErrorActualizacionDeEstadoSMS.objects.last()
        self.assertEqual(log.tipo, 'Notificacion: algunas respuestas son invalidas')
        log_de_respuestas = self._generar_mensaje_para_respuestas(total_resuestas=1, respuestas_actualizadas=0,
                                                                  respuestas_asociadas_a_envios_invalidos=1)
        self.assertEqual(log.descripcion, log_de_respuestas)
        self.assertNotEqual(log.response,'')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_con_lote_sin_identificador_debe_actualizar_el_envio(self, mock_call):
        # configura la respuesta del mock
        sending_date = '2015/08/22'
        query_output = SMSOutputQueryLotListMock.with_sending(None, '1', '1510101010', 1, sending_date)

        fecha_respuesta = '01/01/2008 22:14:34 p.m.'
        mensaje = 'Ack!!'
        answer_output = SMSAnswerListMock.with_answer('1', fecha_respuesta, '1510101010', mensaje)
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output,
                                                                        answer_list=answer_output).call
        self._inicializar_fixture_envios()
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()

        envios = self.campania.envios.all()
        envio_de_campania = self._get_envio('1', envios)
        self._assert_envio(envio_de_campania, telefono='1510101010', mensaje='hello 1510101010',
                           estado=EnvioDeMensaje.REALIZADO, fecha_envio=sending_date)

        respuestas = envio_de_campania.respuestas
        self.assertEqual(respuestas.count(), 1)
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No se deberia tener en cuenta los errores de parseo de fechas')

    @mock.patch("lib.smscover.SMSPushService.call")
    def test_actualizacion_estado_de_envios_con_id_mensaje_repetidos_debe_actualizar_solo_los_envios_de_la_campania(
            self, mock_call):
        self._inicializar_fixture_envios()
        envios = self.campania.envios.all()
        self.assertEqual(envios.count(), 2)
        envios_gemelos = self._crear_envios_gememelos_para(envios)
        self._assert_envios_en_proceso(envios)
        self._assert_envios_en_proceso(envios_gemelos)
        sending_date = '2015/08/22'
        self._configurar_mock_para_respuesta_actualizacion_exitosa(envios, mock_call, sending_date)
        self.servicio_de_mensajes.actualizar_envios_y_respuestas()
        self._assert_envios_realizados(envios, sending_date)
        self._assert_envios_en_proceso(envios_gemelos)
        self.assertEqual(LogDeErrorActualizacionDeEstadoSMS.objects.count(), self.cantidad_de_errores_inicial,
                          'No deberia haberse registrado ningún error')

    def _assert_envios_realizados(self, envios, sending_date):
        for envio in envios:
            envio_reloaded = reload_model(envio)
            self.assertTrue(envio_reloaded.fue_exitoso())
            self._assert_datetime(envio_reloaded.fecha, sending_date)

    def _assert_envios_en_proceso(self, envios):
        for envio in envios:
            envio_reloaded = reload_model(envio)
            self.assertEqual(envio_reloaded.estado, EnvioDeMensaje.EN_PROCESO)

    def _configurar_mock_para_respuesta_actualizacion_exitosa(self, envios, mock_call, sending_date):
        lot_output = SMSQueryStateLotMock(self._client_lot_para(self.campania.pk))
        self._agregar_respuesta_de_actualizacion_exitosa_para(envios, lot_output, sending_date)
        query_output = SMSOutputQueryLotListMock.with_lots([lot_output])
        mock_call.side_effect = SMSServiceAnswerMock.successfully_query(lot_status=query_output).call

    def _crear_envios_gememelos_para(self, envios):
        gemelos = []
        for envio_de_campania in envios.all():
            envio = envio_de_campania.envio
            gemelo = EnvioDeMensaje.nuevo(envio.prospecto, envio.id_mensaje, envio.telefono, envio.texto())
            gemelos.append(gemelo)
        return gemelos

    def _agregar_respuesta_de_actualizacion_exitosa_para(self, envios, lot_output, sending_date):
        for envio_de_campania in envios:
            envio = envio_de_campania.envio
            lot_output.add_query_sending(envio.id_mensaje, envio.telefono, 1, sending_date)
