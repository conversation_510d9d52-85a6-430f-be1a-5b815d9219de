# coding=utf-8
import json

from django.urls import reverse
from django.test import override_settings
from rest_framework import status

from conversaciones.occ_configuracion import OCCConfiguracion
from occ.models import ChatDeVentas, MensajeDeChat
from testing.base import BaseLoggedTest


@override_settings(CHAT_PROVIDER='occ.tests.chat_de_ventas.proveedor_chat_mock.ChatProviderSuccessMock')
class OCCEnviarMensajeDeChatDeVentaViewTest(BaseLoggedTest):
    def setUp(self):
        super(OCCEnviarMensajeDeChatDeVentaViewTest, self).setUp()
        self.occ_configuracion = OCCConfiguracion()
        self.vendedor = self.fixture['vend_1']
        self.vendedor.configuracion_de_servicios().permitir_servicio_de_chat()
        self.occ_configuracion.habilitar_servicio_chat(self.vendedor)
        ChatDeVentas.objects.all().delete()

    def _post_enviar_mensaje(self, chat_pk, mensaje=''):
        url = reverse('enviar-mensaje-de-chat')
        response = self.client.post(url, {'chatId': chat_pk, 'texto': mensaje})
        return response

    def _assert_response(self, response, response_status):
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = json.loads(response.content)
        self.assertEqual(response_data['status'], response_status)

    def _assert_response_message(self, response, mensaje):
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = json.loads(response.content)
        self.assertEqual(response_data['message'], mensaje)

    def test_mensaje_vacio_debe_responder_request_erroneo(self):
        url = reverse('enviar-mensaje-de-chat')
        response = self.client.post(url, '{}', content_type='application/json')
        self._assert_response(response, response_status=False)

    def test_enviar_mensaje_a_chat_inexistente_debe_notificar_error(self):
        response = self._post_enviar_mensaje(chat_pk=1, mensaje='hola')
        self._assert_response(response, response_status=False)

    def test_mensaje_de_otro_vendedor_debe_notificar_error(self):
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.fixture['vend_2'], token='1')
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje='Hey!')
        self._assert_response(response, response_status=False)
        chat = ChatDeVentas.objects.get(pk=chat.pk)
        self.assertEqual(chat.mensajes.count(), 1)

    def test_mensaje_con_texto_vacio_debe_responder_request_erroneo(self):
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.vendedor, token='1')
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje='')
        self._assert_response(response, response_status=False)
        chat = ChatDeVentas.objects.get(pk=chat.pk)
        self.assertEqual(chat.mensajes.count(), 1)

    @override_settings(CHAT_PROVIDER='occ.tests.chat_de_ventas.proveedor_chat_mock.ChatProviderFailMock',
                       CHAT_CANTIDAD_DE_REINTENTOS_POR_ERROR_DE_COMUNICACION=1)
    def test_envio_al_fallar_el_servicio_debe_igualmente_registrar_el_mensae(self):
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.vendedor, token='1')
        texto = 'Hey'
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje=texto)
        self._assert_response(response, response_status=True)
        chat = ChatDeVentas.objects.get(pk=chat.pk)
        self.assertEqual(chat.mensajes.count(), 2)

    def test_envio_de_vendedor_deshabilitado_debe_responder_error(self):
        self.occ_configuracion.deshabilitar_servicio_chat(self.vendedor)
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.vendedor, token='1')
        texto = 'Hey'
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje=texto)
        self._assert_response(response, response_status=False)
        self._assert_response_message(response, 'vendedor invalido')

    def test_envio_de_vendedor_bloquead_desde_admin_debe_responder_error(self):
        self.vendedor.configuracion_de_servicios().no_permitir_servicio_de_chat()
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.vendedor, token='1')
        texto = 'Hey'
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje=texto)
        self._assert_response(response, response_status=False)
        self._assert_response_message(response, 'vendedor invalido')

    def test_envio_exitoso_debe_responder_ok_y_registrar_mensaje(self):
        chat = ChatDeVentas.nuevo_con_envio_con(texto='Hello World!', vendedor=self.vendedor, token='1')
        texto = 'Hey'
        response = self._post_enviar_mensaje(chat_pk=chat.pk, mensaje=texto)
        self._assert_response(response, response_status=True)
        chat = ChatDeVentas.objects.get(pk=chat.pk)
        self.assertEqual(chat.mensajes.count(), 2)
        self.assertEqual(chat.mensajes.filter(emisor=MensajeDeChat.VENDEDOR, texto=texto).count(), 1)