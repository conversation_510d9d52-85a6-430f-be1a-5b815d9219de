# coding=utf-8
import datetime

from rest_framework import status

from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto



class CreadorDeContextoDietrich(object):
    """TODO: Crear archivos por cada uno de estas clases"""
    def agregar_informacion_adicional_default_a_prospecto(self, prospecto):
        fecha_de_venta = datetime.date(1991, 6, 12)
        fecha_de_nacimiento = datetime.date(2017, 6, 12)
        inicio_de_horario_de_contacto = datetime.time(8, 0, 0, 0)
        fin_de_horario_de_contacto = datetime.time(18, 0, 0, 0)
        self.agregar_informacion_adicional_a_prospecto(
            prospecto, ocupacion='Plomero', fecha_de_nacimiento=fecha_de_nacimiento, sexo='Masculino',
            estado_civil='Soltero', documento='29875362', empresa='Explotadora S.A', cargo='Explotador Sr.',
            cantidad_de_integrantes_familiares=4, auto='Lamborghini', cliente='Pequeña PyME',
            hobby='Robar plata', producto='Sistema de Gestión de Transacciones',
            inicio_de_horario_de_contacto=inicio_de_horario_de_contacto,
            fin_de_horario_de_contacto=fin_de_horario_de_contacto, dias_de_contacto='LMXS',
            clasificacion='Fachus Maximus', motivo='Tener más dinero', submotivo='Por placer', valor_movil=125679,
            valor_cuota=56754.87, color='Rojo', precio_venta_con_iva=456789, precio_de_lista=987777,
            fecha_de_venta=fecha_de_venta)

    def agregar_informacion_adicional_a_prospecto(
            self, prospecto, ocupacion=None, fecha_de_nacimiento=None, sexo=None, estado_civil=None, documento=None,
            empresa=None, cargo=None, cantidad_de_integrantes_familiares=None, auto=None, cliente=None, hobby=None,
            producto=None, inicio_de_horario_de_contacto=None, fin_de_horario_de_contacto=None,
            dias_de_contacto=None, clasificacion=None, motivo=None, submotivo=None, valor_movil=None, valor_cuota=None,
            color=None, precio_venta_con_iva=None, precio_de_lista=None, fecha_de_venta=None):
        campos = {'ocupacion': ocupacion, 'fecha_de_nacimiento': fecha_de_nacimiento,
                  'sexo': sexo, 'estado_civil': estado_civil, 'documento': documento, 'empresa': empresa,
                  'cargo': cargo, 'cantidad_de_integrantes_familiares': cantidad_de_integrantes_familiares,
                  'auto': auto, 'cliente': cliente, 'hobby': hobby, 'producto': producto,
                  'inicio_de_horario_de_contacto': inicio_de_horario_de_contacto.hour,
                  'fin_de_horario_de_contacto': fin_de_horario_de_contacto.hour, 'dias_de_contacto': dias_de_contacto,
                  'clasificacion': clasificacion, 'motivo_de_rechazo_de_compra': motivo,
                  'submotivo_de_rechazo_de_compra': submotivo,
                  'valor_movil': valor_movil, 'valor_de_cuota': valor_cuota, 'color': color,
                  'precio_de_venta_con_iva': precio_venta_con_iva, 'precio_de_lista': precio_de_lista,
                  'fecha_de_venta': fecha_de_venta
                  }
        gestor = GestorDeProspecto.nuevo_para(rol=prospecto.obtener_vendedor())
        for nombre_de_campo, valor in list(campos.items()):
            gestor.modificar_informacion_adicional_de(prospecto, nombre_de_campo, valor)


class RequestDietrichMock(object):
    def __init__(self, status_code, content):
        self.status_code = status_code
        self.content = content

    @classmethod
    def http_404_not_found(cls):
        return cls(status_code=status.HTTP_404_NOT_FOUND, content='')

    @classmethod
    def success(cls):
        return cls(status_code=status.HTTP_204_NO_CONTENT, content='')

    def raise_for_status(self):
        pass