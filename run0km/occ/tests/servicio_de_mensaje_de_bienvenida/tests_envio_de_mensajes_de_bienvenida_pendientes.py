# coding=utf-8
from django.utils.timezone import now, timedelta
from django.conf import settings
from testing.base import BaseFixturedTest
from prospectos.models import Prospecto
from testing.creador_de_contexto import CreadorDeContexto
from vendedores.models import LogActividad
from occ.servicio_mensaje_de_bienvenida import ServicioDeMensajeDeBienvenida
from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp


class EnvioDeMensajesDeBienvenidaPendientesTest(BaseFixturedTest):

    def setUp(self):
        super(EnvioDeMensajesDeBienvenidaPendientesTest, self).setUp()
        self.creador_de_contexto = CreadorDeContexto(fixture=self.fixture)
        self._vendedor = self.fixture['vend_1']
        Prospecto.objects.filter(vendedor=self._vendedor).delete()
        self._prospecto_creado_hace_mucho = self._obtener_prospecto_creado_hace_mucho()
        self._prospecto_creado_hace_poco = self._obtener_prospecto_creado_hace_poco()
        MensajesWhatsapp.objects.filter(prospecto__vendedor=self._vendedor).delete()
        self._prospecto_con_mensaje_ya_enviado = self._obtener_prospecto_creado_hace_poco_con_mensaje_ya_enviado()
        self._habilitar_whatsapp_y_mensaje_de_bienvenida(self._vendedor)

    def test_envia_mensajes_de_bienvenida_pendientes(self):
        # Dado
        log_de_actividad = self._registrar_actividad_sin_acceso_reciente_para(self._vendedor)

        # Cuando
        servicio = ServicioDeMensajeDeBienvenida(self._vendedor)
        servicio.enviar_mensajes_de_bienvenida_pendientes(log_de_actividad.ultima)

        # Entonces
        self._assert_nuevo_mensaje_de_bienvenida_prospecto_creado_hace_poco()

    def _obtener_prospecto_creado_hace_mucho(self):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self._vendedor)
        hace_mucho = now() - timedelta(days=settings.ANTIGUEDAD_DE_PROSPECTOS_A_ENVIAR_WHATSAPP_DE_BIENVENIDA + 1)
        prospecto.fecha_creacion = hace_mucho
        prospecto.save()
        return prospecto

    def _obtener_prospecto_creado_hace_poco(self):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self._vendedor)
        hace_poco = now() - timedelta(days=settings.ANTIGUEDAD_DE_PROSPECTOS_A_ENVIAR_WHATSAPP_DE_BIENVENIDA - 1)
        prospecto.fecha_creacion = hace_poco
        prospecto.save()
        return prospecto

    def _obtener_prospecto_creado_hace_poco_con_mensaje_ya_enviado(self):
        hace_poco = now() - timedelta(days=settings.ANTIGUEDAD_DE_PROSPECTOS_A_ENVIAR_WHATSAPP_DE_BIENVENIDA - 1)
        prospecto = self._obtener_prospecto_creado_hace_poco()
        mensaje = MensajesWhatsapp.nuevo_mensaje(
            prospecto=prospecto, fecha=hace_poco, mensaje="hello", telefono="12121212")
        mensaje.save()
        return prospecto

    def _assert_nuevo_mensaje_de_bienvenida_prospecto_creado_hace_poco(self):
        mensajes = MensajesWhatsapp.objects.filter(prospecto__vendedor=self._vendedor)
        self.assertEqual(2, mensajes.count())
        self.assertTrue(mensajes.mensajes_a_prospecto(self._prospecto_creado_hace_poco).exists())
        self.assertTrue(mensajes.mensajes_a_prospecto(self._prospecto_con_mensaje_ya_enviado).exists())

    def _habilitar_whatsapp_y_mensaje_de_bienvenida(self, vendedor):
        vendedor.habilitar_whatsapp()
        vendedor.habilitar_mensaje_bienvenida()
        concesionaria = vendedor.supervisor.concesionaria
        config_servicios = concesionaria.configuracion_de_servicios()
        config_servicios.habilitar_whatsapp()

    def _registrar_actividad_sin_acceso_reciente_para(self, vendedor):
        hace_mucho = now() - timedelta(days=settings.DIAS_DESDE_ULTIMA_ACTIVIDAD_PARA_ENVIAR_WHATSAPP_DE_BIENVENIDA,
                                       minutes=10)
        log = LogActividad(vendedor=vendedor, anio=hace_mucho.year, mes=hace_mucho.month, ultima=hace_mucho, cantidad=0)
        log.save()
        return log
