import random
import string

import mock
from django.utils import timezone

from gsuite.gmail import GMailMessage
from gsuite.models import GSuiteUserConfiguration
from occ.models import EnvioDeEmail
from conversaciones.models import Conversacion
from occ.tasks import sincronizar_cuentas_de_email
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from testing.base import BaseFixturedTest


class MockClienteGmail(object):
    @classmethod
    def get_new_messages(cls):
        return []


class ActualizacionDeCuentasDeEmailTest(BaseFixturedTest):

    def setUp(self):
        super(ActualizacionDeCuentasDeEmailTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self._configurar_emial_a_prospecto('<EMAIL>')
        vendedor = self.fixture['vend_1']
        self.repartidor = RepartidorDeProspectos.nuevo()
        self.supervisor = vendedor.responsable()
        self.repartidor.asignar_prospecto_a(prospecto=self.prospecto, vendedor=vendedor)

    @mock.patch("gsuite.gmail.client_gmail.GmailClient.get_new_messages")
    def test_respuesta_de_email_de_prospecto_actualiza_conversacion(self, mock_get_new_messages):
        mensajes = [GMailMessage.new_for(
            self._random_identifier(), 'subject', 'hello', sender=self.prospecto.email,
            receiver='<EMAIL>', date_time=timezone.now(), thread_id='1', history_id='1')]

        mock_get_new_messages.return_value = mensajes
        self._crear_configuracion_gmail_para(self.supervisor)
        self._enviar_mensaje_via_email_a(self.prospecto)
        sincronizar_cuentas_de_email()
        self._assert_conversacion(self.prospecto, mensajes)

    @mock.patch("gsuite.gmail.client_gmail.GmailClient.get_new_messages")
    def test_cuando_el_prospecto_tiene_el_mismo_mail_de_la_cuenta_los_mensajes_enviados_desde_esta_cuenta_son_ignorados(
            self, mock_get_new_messages):
        """
            Si el prospecto tiene el mismo mail que el supervisor, esos mails no son incluidos en
            la conversacion, esto es para evitar la vulnerabilidad de que el vendedor pueda ver toda
            la casilla de mails del supervisor.
        """
        self._enviar_mensaje_via_email_a(self.prospecto)
        email_del_supervisor = '<EMAIL>'
        self._configurar_emial_a_prospecto(email_del_supervisor)
        mensajes = [GMailMessage.new_for(
            self._random_identifier(), 'subject', 'hello', sender=email_del_supervisor,
            receiver='<EMAIL>', date_time=timezone.now(), thread_id='1', history_id='1')]

        mock_get_new_messages.return_value = mensajes
        self._crear_configuracion_gmail_para(self.supervisor, email=email_del_supervisor)
        sincronizar_cuentas_de_email()
        self._assert_conversacion(self.prospecto, mensajes=[])

    def _configurar_emial_a_prospecto(self, email_del_supervisor):
        self.prospecto.email = email_del_supervisor
        self.prospecto.save()

    def _enviar_mensaje_via_email_a(self, prospecto):
        EnvioDeEmail.nuevo('hello', prospecto, EnvioDeEmail.VENDEDOR)

    def _crear_configuracion_gmail_para(self, vendedor, email='<EMAIL>'):
        GSuiteUserConfiguration.new_for(user=vendedor.user, email=email)

    def _random_identifier(self):
        return ''.join([random.choice(string.digits) for _ in range(20)])

    def _assert_conversacion(self, prospecto, mensajes):
        conversacion = Conversacion.objects.conversacion_email_de_prospecto(prospecto)
        conversacion_mensajes = conversacion.mensajes()
        self.assertEqual(len(conversacion_mensajes), 1 + len(mensajes))
        self._assert_mensajes(mensajes, conversacion_mensajes)

    def _assert_mensajes(self, mensajes, conversacion_mensajes):
        for mensaje in mensajes:
            query = conversacion_mensajes.filter(_contenido=mensaje.body(), _emisor=EnvioDeEmail.CLIENTE)
            self.assertTrue(query.exists())

