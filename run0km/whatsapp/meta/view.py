import logging
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.permissions import BasePermission

from conversaciones.meta.canal_de_comunicacion_via_meta import CanalDeComunicacionDeWhatsappViaMeta
from conversaciones.meta.notificador_de_conversaciones_meta_via_pusher import NotificadorDeConversacionesMetaViaPusher
from whatsapp.meta.meta_changes_response import MetaChangesResponse
from whatsapp.meta.serializadores.serializadores_api import ApiResponseAcceptedSerializer, WebhookDeliveredSerializer, \
    WebhookFailedSerializer, WebhookReadSerializer, WebhookSentSerializer, WebhookMensajeSerializer
from whatsapp.meta.services.whatsapp_meta_service import WhatsappMetaService
from rest_framework.exceptions import ValidationError

logger = logging.getLogger('whatsapp-meta')


class WSAPIAuthentication(BasePermission):
    def has_permission(self, request, view):
        ip_address = request.META.get('REMOTE_ADDR')  # verifica que la ip sea de pani
        # auth_header = request.META.get('HTTP_AUTHORIZATION').split()[1] # si la IP no esta que corresponde verifica token. 
        logger.info(request.META)
        logger.info(f"ip: {ip_address}")
        return True


class WhatsappWebhookView(APIView):
    permission_classes = [WSAPIAuthentication, ]

    def post(self, request):
        logger.info(f"WhatsappWebhookView: Request data: {request.body}")
        try:
            meta_changes_response = MetaChangesResponse.new_from(request.data)
            canal_via_meta = CanalDeComunicacionDeWhatsappViaMeta.nuevo(
                servicio_de_meta=WhatsappMetaService.nuevo(),
                notificador=NotificadorDeConversacionesMetaViaPusher.nuevo(), logger=logger)
            canal_via_meta.actualizar_desde(meta_changes_response)
        except (ValueError, KeyError) as ke:
            logger.error(f"WhatsappWebhookView: Error: {ke}")
            return JsonResponse({'status': "error"}, status=400)
        logger.info(f'WhatsappWebhookView: Se actualizó correctamente')
        return JsonResponse({'status': "ok"}, status=200)

    def _obsoleto_post(self, request):
        logger.info(f"Request data: {request.body}")
        try:
            serializer = self._obtain_serializer(request.data)
            logger.info(f"tipo de mensaje: {type(serializer)}")
            ws_api_service = WhatsappMetaService.nuevo()
            ws_api_service.handler(serializer.data, type(serializer))
        except KeyError as ke:
            logger.error(f"KeyError: {ke}")
        except ValueError as ve:
            logger.error(f"ValueError: {ve}")
            return JsonResponse({'status': "error de validacion de datos (ValueError)"}, status=400)
        return JsonResponse({'status': "ok"}, status=200)

    def _get_serializer_object(self, data):
        try:
            if 'messaging_product' in data and 'contacts' in data and 'messages' in data:
                return ApiResponseAcceptedSerializer(data=data)
            elif 'status' in data and data['status'] == 'delivered' and 'conversation' in data and 'pricing' in data:
                return WebhookDeliveredSerializer(data=data)
            elif 'status' in data and data['status'] == 'sent' and 'conversation' in data and 'pricing' in data:
                return WebhookSentSerializer(data=data)
            elif 'status' in data and data['status'] == 'read':
                return WebhookReadSerializer(data=data)
            elif 'status' in data and data['failed'] == 'failed':
                return WebhookFailedSerializer(data=data)
            else:
                return WebhookMensajeSerializer(data=data)
        except ValidationError as e:
            raise ValidationError(f"Error al validar el mensaje: {e}")

    def _obtain_serializer(self, data):
        serializer = self._get_serializer_object(data=data)
        if serializer.is_valid():
            return serializer
        raise ValueError(serializer.errors)
