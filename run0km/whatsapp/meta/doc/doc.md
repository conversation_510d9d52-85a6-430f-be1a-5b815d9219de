
Referenaciar conversacion diagrama 
========

- Conexion interna (no a traves de WS) 
- ver envio de propectos por WS // 


-  grop de numeros  --? asociaciones
- ver numero de conversacion no ocupada//  si entra por formulario el numero saliente no importa. 

estados: accepted (api) → delivered → sent → read

0- {"messaging_product":"whatsapp","contacts":[{"input":"5491121698525","wa_id":"5491121698525"}],"messages":[{"id":"wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEkE0NjdCRDE0NTFDOEIwRDQyNQA=","message_status":"accepted"}]}

1- {"id":"wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=","status":"delivered","timestamp":"1737381219","recipient_id":"5491121698525","conversation":{"id":"b0d94b6403c4eaf1b105bdbe199ab247","origin":{"type":"marketing"}},"pricing":{"billable":true,"pricing_model":"CBP","category":"marketing"}}

2 -{"id":"wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=","status":"sent","timestamp":"1737381218","recipient_id":"5491121698525","conversation":{"id":"b0d94b6403c4eaf1b105bdbe199ab247","expiration_timestamp":"1737467400","origin":{"type":"marketing"}},"pricing":{"billable":true,"pricing_model":"CBP","category":"marketing"}}

3- {"id":"wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=","status":"read","timestamp":"1737381283","recipient_id":"5491121698525"}

'wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA='


FLUJO DE LOS MENSAJES
======
1: REQUEST url "POST /conversaciones/enviar-mensaje/ HTTP/1.1"

REQUEST body
 {
    'tipo'    : 'whatsapp'
    'prospecto_pk' : '456089'
    'texto'        : 'pete'
}

2: Clasifica los mensajes en segun el tipo 
3: 