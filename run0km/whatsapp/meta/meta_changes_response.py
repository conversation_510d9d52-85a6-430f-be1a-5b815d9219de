from django.utils import timezone

class MetaChangesResponse(object):

    def __init__(self, response_as_dict):
        self._response_as_dict = response_as_dict

    @classmethod
    def new_from(cls, response_as_dict):
        return cls(response_as_dict)

    @classmethod
    def new_example_status_change_to(cls,
                                     id_meta_message,
                                     status,
                                     phone=None, conversation_expiration_timestamp=None, conversation_id=None):
        return cls.new_from({
            "value": {
                "messaging_product": "whatsapp",
                "metadata": {
                    "display_phone_number": "5491127471997",
                    "phone_number_id": "458578827350094"
                },
                "statuses": [
                    {
                        "id": id_meta_message,
                        "status": status,
                        "timestamp": "1740582872",
                        "recipient_id": phone or '15551496593',
                        "conversation": (
                            cls._conversacion_segun_tipo_de_estado(
                                conversation_expiration_timestamp,
                                conversation_id,
                                status)),
                        "pricing": {
                            "billable": True,
                            "pricing_model": "CBP",
                            "category": "service"
                        }
                    }
                ]
            },
            "field": "messages"
        })

    @classmethod
    def _conversacion_segun_tipo_de_estado(cls, conversation_expiration_timestamp, conversation_id, status):
        if status == 'sent':
            conversation = {
                "id": conversation_id or 'f4e6e634e0f94459eae1b0d6e486cdaa',
                "expiration_timestamp": conversation_expiration_timestamp or "1748980980",
                "origin": {
                    "type": "service"
                }
            }
        else:
            conversation = {
                "id": conversation_id or 'f4e6e634e0f94459eae1b0d6e486cdaa',
                "origin": {
                    "type": "service"
                }
            }
        return conversation

    # Messages of change of statuses
    def is_change_of_statuses(self):
        return not self._response_as_dict.get('mensaje')

    def id_meta(self):
        return self._response_as_dict['value']['statuses'][0]['id']

    def status(self):
        return self._response_as_dict['value']['statuses'][0]['status']

    def has_conversation(self):
        return 'conversation' in self._response_as_dict['value']['statuses'][0]

    def conversation_id_meta(self):
        conversation = self._response_as_dict['value']['statuses'][0].get('conversation')
        return conversation['id'] if conversation is not None else None

    def conversation_expiration_datetime(self):
        expiration_datetime_string = self._conversation_expiration_timestamp_as_string()
        if expiration_datetime_string is  None:
            return None
        else:
            return self._datetime_from_timestamp(expiration_datetime_string)

    # Messages of client response
    def phone_number(self):
        return self._response_as_dict.get('telefono')

    def message(self):
        return self._response_as_dict.get('mensaje')

    def operator_meta(self):
        return self._response_as_dict.get('operator_meta')

    def _conversation_expiration_timestamp_as_string(self):
        conversation = self._response_as_dict['value']['statuses'][0].get('conversation')
        return conversation.get('expiration_timestamp') if conversation is not None else None

    def _datetime_from_timestamp(self, timestamp_string):
        expiration_timestamp = int(timestamp_string)
        from core.date_helper import DatetimeHelper
        calendario = DatetimeHelper()
        return calendario.from_timestamp(expiration_timestamp, timezone=timezone.utc)
