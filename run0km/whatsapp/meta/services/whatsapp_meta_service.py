import logging

from django.conf import settings
from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
from whatsapp.meta.models.meta_template import MetaTemplate
from whatsapp.meta.models.operador import Operador
from whatsapp.meta.models.repositorio_de_mensaje import RepositorioDeMensaje
from whatsapp.meta.send_meta_api import SendMetaAPI

from whatsapp.meta.serializadores.serializadores_api import WebhookMensajeSerializer
from whatsapp.meta.services.meta_funciones_auxiliares import validar_variables

logger = logging.getLogger(__name__)


class WhatsappMetaService:
    def __init__(self, url, token):
        self.url = url
        self.token = token

    @classmethod
    def nuevo(cls):
        return cls(settings.WS_META_URL, settings.WS_META_TOKEN)

    def iniciar_conversacion(self, telefono_destinatario, marca, dni, id_operador, nombre_template, url_notificacion):
        payload = {
            "token": self.token,
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "template": nombre_template,
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        }
        request = SendMetaAPI.new_to(self.url)
        response = request.call(payload)
        return response.json()

    def enviar_mensaje(self, telefono_destinatario, marca, dni, id_operador, texto, url_notificacion):
        payload = {
            "token": self.token,
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "message": texto,
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        }
        request = SendMetaAPI.new_to(self.url)
        response = request.call(payload)
        return response.json()

    @validar_variables
    def obsoleto_iniciar_conversacion(self, celular, marca, dni, operador: Operador, template: MetaTemplate,
                                      variables: dict) -> RepositorioDeMensaje:

        payload = {
            "token": self.token,
            "number": celular,
            "fromOperator": operador.id_operador,
            "template": template.nombre,
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
        }
        request = SendMetaAPI.new_to(self.url)
        # response =  {'messaging_product': 'whatsapp', 'contacts': [{'input': '1140583623', 'wa_id': '5491140583623'}], 'messages': [{'id': 'wamid.HBgNNTQ5MTE0MDU4MzYyMxUCABEYEjMwQzk0RDBBRTEwNjdEOTRCNAA=', 'message_status': 'accepted'}]}
        response = request.call(payload)
        template_lleno = template.contenido
        repositorio_de_mensajes = RepositorioDeMensaje.crear(
            response.json(),
            # response,
            operador,
            template_lleno)

        return repositorio_de_mensajes

    def enviar_mensaje_a(self, prospecto, texto, telefono):
        operador = Operador.objects.first()
        template = MetaTemplate.objects.first()
        # TODO: definir si inicia una coreversacion o si envia un mensaje
        repositorio_de_mensaje = self.obsoleto_iniciar_conversacion(telefono, prospecto.obtener_marca().nombre(),
                                                                    "25724484", operador, template, {})
        MensajesWhatsapp.nuevo_mensaje(prospecto, texto, telefono, fecha=None)

    def handler(self, data, tipo_mensaje=None):
        """Handler de mensajes de la API de Meta Entrantes

        Args:
            data (dict): Mensaje de Meta
            tipo_mensaje (WebhookMensajeSerializer, optional): Defaults to None.
        """
        if tipo_mensaje == WebhookMensajeSerializer:
            repositorio_de_mensajes = RepositorioDeMensaje.agregar_mensaje_a_conversacion(data)
        else:
            mensaje_id_meta = data.get("id")
            repositorio_de_mensajes = RepositorioDeMensaje.traer(mensaje_id_meta)
            repositorio_de_mensajes.actualizar(data)

    def cambiar_estado_del_mensaje(self, mensaje_id, status: str) -> None:
        # tengo que encontrar la conversacion que tiene el mensaje con el mismo ID
        pass

    def _setear_id_meta_conversacion(self, id_meta_conversacion):
        pass
