from django.contrib import admin
from django.core.exceptions import ValidationError
from django import forms
#from django.utils.translation import ugettext_lazy as _
from django.utils import timezone

from whatsapp.meta.models.conversation_in_meta import ConversationInMeta
from whatsapp.meta.models.message_in_meta import MessageInMeta
from whatsapp.meta.models.meta_template import MetaTemplate, Variable
from whatsapp.meta.models.operador import Operador, GrupoOperadores
from whatsapp.meta.services.meta_funciones_auxiliares import extraer_variables


class MetaTemplateForm(forms.ModelForm):
    class Meta:
        model = MetaTemplate
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        variables_aceptadas = Variable.objects.values_list("nombre", flat=True)
        contenido = cleaned_data.get("contenido")
        variables_en_contenido = extraer_variables(contenido)

        for variable in variables_en_contenido:
            if variable not in variables_aceptadas:
                raise ValidationError(f"La variable '{variable}' no es aceptada.")
        return cleaned_data


class MetaTemplateAdmin(admin.ModelAdmin):
    form = MetaTemplateForm
    list_display = ('nombre', 'contenido', 'fecha_alta', 'fecha_modificacion', 'activo',)
    exclude = ('variables',)


class OperadorAdmin(admin.ModelAdmin):
    list_display = ('nombre', 'id_operador', 'activo')


class MessageInMetaAdmin(admin.ModelAdmin):
    list_display = ('_status', '_phone_number', '_action', '_created_at')


class VariableAdmin(admin.ModelAdmin):
    list_display = ('nombre',)


class ConversationInMetaAdmin(admin.ModelAdmin):
    list_display = ('_meta_id', '_expiration_datetime', '_operator', '_created_at', 'is_open', 'close_datetime')
    readonly_fields = list_display
    can_delete = False
    actions_on_top = False

    def has_add_permission(self, request):
        return False

    def is_open(self, obj):
        return 'Si' if obj.is_open() else 'No'

    is_open.short_description = '¿Tiene ventana abierta?'

    def close_datetime(self, obj):
        return timezone.localtime(obj.close_datetime()).strftime('%d-%m-%Y %H:%M') if obj.close_datetime() else '-'

    close_datetime.short_description = 'Fecha cierre de ventana'


admin.site.register(GrupoOperadores)
admin.site.register(Operador, OperadorAdmin)
admin.site.register(ConversationInMeta, ConversationInMetaAdmin)
admin.site.register(Variable)
admin.site.register(MetaTemplate, MetaTemplateAdmin)
admin.site.register(MessageInMeta, MessageInMetaAdmin)
