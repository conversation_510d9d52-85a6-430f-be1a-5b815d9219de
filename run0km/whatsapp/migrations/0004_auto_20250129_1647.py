# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2025-01-29 19:47
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('whatsapp', '0003_auto_20190213_1501'),
    ]

    operations = [
        migrations.CreateModel(
            name='Conversacion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_meta', models.CharField(blank=True, max_length=64, null=True)),
                ('fecha_de_expiracion', models.DateTimeField(blank=True, null=True)),
                ('fecha_alta', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Mensaje',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_meta', models.Char<PERSON>ield(max_length=64)),
                ('status', models.CharField(choices=[('accepted', 'accepted'), ('delivered', 'delivered'), ('sent', 'sent'), ('read', 'read'), ('failed', 'failed')], default='accepted', max_length=24)),
                ('fecha_alta', models.DateTimeField(auto_now_add=True)),
                ('numero_telefono_receptor', models.CharField(max_length=14)),
                ('mensaje', models.TextField(null=True)),
                ('conversacion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='whatsapp.Conversacion')),
            ],
        ),
        migrations.CreateModel(
            name='MetaTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=64)),
                ('contenido', models.TextField()),
                ('fecha_alta', models.DateTimeField(auto_now_add=True)),
                ('fecha_modificacion', models.DateTimeField(auto_now=True)),
                ('activo', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Template',
                'verbose_name_plural': 'Templates',
            },
        ),
        migrations.CreateModel(
            name='variables',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=64)),
            ],
        ),
        migrations.AlterField(
            model_name='whatsappmessage',
            name='phone',
            field=models.CharField(blank=True, default='', max_length=64),
        ),
        migrations.AlterField(
            model_name='whatsappmessage',
            name='text',
            field=models.CharField(blank=True, default='', max_length=600),
        ),
        migrations.AlterField(
            model_name='whatsappmessagedelivery',
            name='status',
            field=models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Sending'), (2, 'Sent'), (3, 'Failed')], default=0),
        ),
        migrations.AddField(
            model_name='metatemplate',
            name='variables',
            field=models.ManyToManyField(related_name='template_variables', to='whatsapp.variables'),
        ),
    ]
