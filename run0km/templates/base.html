{% load permisos personificacion prospectos_utils %}
{% load static from staticfiles %}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>{{ dominio.title }}</title>
    {% block css %}
        <link href="{% static 'css/normalize.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/bootstrap.min.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery.taghandler.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm-filtros.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery-ui.min.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm-chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery.growl.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm-alerta-chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        {% if user.is_authenticated %}
            <link href="{% static 'css/chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
            <link href="{% static 'css/open-iconic-bootstrap.min.css' %}?v={{ version }}" type="text/css"
                  rel="stylesheet">
            <link href="{% static 'css/nuevo-maquetado/novedades.css' %}?v={{ version }}" type="text/css"
                  rel="stylesheet">
        {% endif %}

    {% endblock %}
    {% block js %}
        <script type="text/javascript" src="{% static 'js/jquery.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui-timepicker-addon.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.ui.datepicker-es.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spin.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/csrf_token.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spinners.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'lib/moment/moment-with-locales.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/common.js' %}?v={{ version }}"></script>

        {% if user.is_authenticated %}
            <script type="text/javascript" src="{% static 'js/jquery.flexslider-min.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/alerta.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/jquery.growl.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/notificador.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="https://js.pusher.com/4.1/pusher.min.js"></script>
            <script type="text/javascript" src="{% static 'js/pusher_service.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat_de_meta.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/gestor_de_notificaciones.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/novedades_controlador.js' %}?v={{ version }}"></script>

            <script type="text/javascript">
                var alerta_mensajes_url = "{% url 'alerta-mensajes' %}";
                var llamados_alertados_url = "{% url 'llamados-alertados' %}";
                var ayuda_chat_url = "{% url 'ayuda-de-chat' %}";
                var ver_llamadas_proximas_url = "{% url 'prospectos' %}?filter_llamado=dia";
                var imagen_alerta = "{% static 'img/phone-blue-icon.png' %}";
                var titulo_alerta = "{{dominio.title}}";
                var control_de_alertas;
                {% if corresponde_alertar_por_llamados %}
                var delta_alerta_llamados = {{ delta_alerta_llamados }};
                var alertas_de_llamado = {{ alertas_de_llamado|safe }};
                control_de_alertas = new ControlDeAlertasDeLlamado(alertas_de_llamado, delta_alerta_llamados);
                    $(inicializarAlertas);
                {% endif %}
            </script>
            <script type="text/javascript">
                                var listaDeChats, storageManagerListaDeChats;
                {% if user.is_vendedor %}
                    var servicioDeChatDeVentas, gestorDeNotificaciones, servicioDeChatDeMeta;
                    $(function () {
                        var pusherAppKey = "{{ PUSHER_APP_KEY }}";
                        var pusherAppCluster = "{{ PUSHER_APP_CLUSTER }}";
                        var channelName = "{{ user_pusher_channel_name }}";
                        var pusherNotificationService = new PusherService(pusherAppKey, pusherAppCluster, channelName);
                        gestorDeNotificaciones = new GestorDeNotificaciones();
                        gestorDeNotificaciones.configurarEnServicioDeNotificaciones(pusherNotificationService);

                        {% if user.vendedor.configuracion_de_servicios.habilitado_para_chatear %}
                        var chatAreaId = 'chatarea';
                        var startCompulseSoundUrl = "{% static 'sound/start-compulse-alert.mp3' %}";
                        var startChatSoundUrl = "{% static 'sound/car-lock-sound-effect.mp3' %}";
                        var propuestasParaChatUrl = "{% url 'propuestas_para_chat' '0000' %}";
                        var envioDePropuestaUrl = "{% url 'enviar_propuesta_a_chat' '0000' %}";
                        listaDeChats = new ChatListBarView(
                            chatAreaId, startChatSoundUrl, propuestasParaChatUrl, envioDePropuestaUrl);
                        var compulsaId = 'cont-popup-alerta-chat';
                        var enviarMensajeUrl = "{% url 'enviar-mensaje-de-chat' %}";
                        var buscarInformacionDelChatEnElServerUrl = "{% url 'mensajes-de-chat' %}";

                        var marcarChatComoLeidoUrl = "{% url 'marcar-chat-como-leido' '0000' %}";
                        var urlsCompulsas = {
                            'aceptar': "{% url 'aceptar_compulsa' '0000' %}",
                            'rechazar': "{% url 'rechazar_compulsa' '0000' %}"
                        };

                        servicioDeChatDeVentas = new ServicioDeChatDeCompulsas(
                            compulsaId, urlsCompulsas, enviarMensajeUrl, startCompulseSoundUrl,
                            buscarInformacionDelChatEnElServerUrl, marcarChatComoLeidoUrl);
                        storageManagerListaDeChats = listaDeChats.storageManager();
                        servicioDeChatDeVentas.configurarEnServicioDeNotificaciones(pusherNotificationService);
                        servicioDeChatDeVentas.configurarEnStorageManagerListaDeChats(storageManagerListaDeChats);
                        servicioDeChatDeVentas.iniciarChats();

                        {#TODO: tener en cuenta los permisos, por ahora solo va funcionar si user.vendedor.configuracion_de_servicios.habilitado_para_chatear#}
                        var completarChatMetaUrl = "{% url 'conversacion-unificada-json' '0000' 'tipo' %}";
                        var enviarMensajeUrlMeta = "{% url 'enviar-mensaje-json' %}";
                        servicioDeChatDeMeta = new ServicioDeChatDeMeta(listaDeChats, completarChatMetaUrl, enviarMensajeUrlMeta);
                        servicioDeChatDeMeta.configurarEnServicioDeNotificaciones(pusherNotificationService);
                        servicioDeChatDeMeta.iniciarChats();
                        {% endif %}



                    });

                {% endif %}

                function borrarStorage(){
                    if (storageManagerListaDeChats){
                        storageManagerListaDeChats.eraseStorage();
                    }
                }

                var urlListaNovedades = "{% url "lista_novedades" %}";
                var urlContarVisualizacionDeNovedad = "{% url "novedad_visualizada" %}";
                var novedadesControlador = new NovedadesControlador(urlListaNovedades, urlContarVisualizacionDeNovedad, "#modal_novedades");
                {% if debe_mostrar_novedades %}
                    $(function () {
                        novedadesControlador.abrirModalNovedades(false)
                    });
                {% endif %}
            </script>
        {% endif %}
        {% include "google-analytics/head-analytics.html" %}
    {% endblock %}
</head>


<body>
<!-- Modal Para Habilitar Notificaciones -->
<div id="habilitar_notificaciones" title="Debe habilitar Notificaciones de Escritorio" style="display:none;"></div>
<!-- Modal para ir a ver llamadas programadas-->
<div id="ver_llamadas_proximas" title="Usted tiene llamadas programadas." style="display:none;"><br>¿Desea verlas ahora?
</div>
<div id="confirmacion-de-borrado-de-objetivo" title="¿Está seguro que desea borrar este objetivo?" style="display:none;"></div>

<div class="contenido-principal">
    {% block div-principal %}
    {% endblock %}
    <div class="header contenido">

        {% if user|puede_personificar_a_otro_usuario %}
            {% include "menu_de_personificacion.html" %}
        {% endif %}

        <div style="float:left">
            {% if dominio.imagen_url %}
                <a href="{% url 'resumen' %}">
                    <img style="max-width: 315px; max-height: 140px" src="{{ dominio.imagen_url }}"
                     onerror="this.onerror=null;this.src=null;"/>
                </a>
            {% endif %}
        </div>

        <div style="height: 121px;">
            {% if user.is_authenticated %}
                <div class="panel-usuario">
                    <p>Bienvenido:</p>

                    <p>{{ user.get_full_name }}</p>
                    <img src="{% static 'img/barrita.png' %}"/>
                    {% if user.vendedor %}
                        {% if user.vendedor.tiene_conversaciones_habilitado %}
                            {% with user.vendedor.mensajes_no_leidos as mensajes_no_leidos %}
                                <div id="noti_Container">
                                    {% if user|no_pertenece_a_sitio_principal %}
                                        <a id="soporte-tecnico" class="icono-soporte-tecnico offline"
                                           href="javascript:void(Tawk_API.toggle())"
                                           title="Soporte técnico - No hay operadores en este momento"></a>
                                    {% endif %}
                                    <a id="icono-mensajes-whatsapp"
                                       class="mensajes-whatsapp{% if mensajes_no_leidos > 0 %}-cerrado{% endif %}"
                                       href="{% url 'lista-de-conversaciones' %}"
                                       title="Tiene {{ mensajes_no_leidos }} mensaje{% ifnotequal mensajes_no_leidos 1 %}s{% endifnotequal %} sin leer">
                                    </a>
                                    {% if mensajes_no_leidos > 0 %}
                                        <div class="noti_bubble">{{ mensajes_no_leidos }}</div>
                                    {% endif %}
                                </div>

                            {% endwith %}
                        {% endif %}
                        <a class="aviso-llamados{% if user.vendedor.llamados_pendientes > 0 %}-red{% endif %}"
                           href="{% url 'prospectos' %}?filter_llamado=vencidos" title="Llamados vencidos">
                        </a>
                    {% endif %}

                    {% if user.vendedor.es_supervisor %}
                        {% with user.vendedor.cantidad_de_prospectos_sin_asignar as cantidad_de_prospectos_sin_asignar %}
                            {% if cantidad_de_prospectos_sin_asignar > 0 %}
                                <a class="alerta-sin-asignar"
                                   href="{% url 'administracion' %}"
                                   title="{{ cantidad_de_prospectos_sin_asignar }} prospectos sin asignar">
                                </a>
                            {% endif %}
                        {% endwith %}
                    {% endif %}
                    {% if user.vendedor.tiene_vendedores_con_prospectos_sin_trabajar %}
                        <a class="alerta-sin-trabajar"
                           href="{% url 'vendedores' %}" title="Sus vendedores tienen prospectos sin trabajar">
                        </a>
                    {% endif %}
                    <a class="cerrar-sesion" style="padding-right: 22px" href="{% url 'logout' %}" onclick="borrarStorage()">Cerrar Sesión</a>
                </div>
                <div class="ajustar"></div>
            {% endif %}
        </div>
        {% include "menu_principal.html" %}
    </div>
    <div id="cont-popup-alerta-chat" style="display:none; text-transform: uppercase;"></div>
    {% block content %}{% endblock %}
    {% if user.is_authenticated %}
        <div class="chatarea" id="chatarea"></div>
    {% endif %}
    {% if dominio.tiene_acceso_a_terminos_legales_oficiales %}
        <div class="isologotipo" >
            <a href="http://www.jus.gob.ar/datos-personales.aspx/"><img src="{% static 'img/isologotipo.png' %}" alt="Isologotipo"
        title="El titular de los datos personales tiene la facultad de ejercer el derecho de acceso a los mismos en forma gratuita, a intervalos no inferiores a seis meses, salvo que se acredite un interés legítimo al efecto, conforme lo establecido en el artículo 14, inciso 3 de la Ley N° 25.326 La DIRECCIÓN NACIONAL DE PROTECCIÓN DE DATOS PERSONALES, órgano de control de la Ley N° 25.326, tiene la atribución de atender las denuncias y reclamos que se interpongan con relación al incumplimiento de las normas sobre protección de datos personales.">
            </a>
        </div>
    {% endif %}
</div>
<div class="footer">
    <div class="contenido">
        |<a class="secure" href="#">Secure Web Site</a>
        |<a href="#">{{ dominio.derechos }}</a>
        |<a href="{% url 'terminos-y-condiciones' %}">Términos y Condiciones Generales del Sitio Web</a>|
    </div>
</div>

{% if user|no_pertenece_a_sitio_principal %}
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        var nombre = '{{ user.get_full_name }} ({{ user.concesionaria }})';

        Tawk_API.visitor = {
            name: nombre.slice(0, 38),
            email: '{{ tawk_email }}'
        };

        Tawk_API.onStatusChange = function (status) {
            var $botonSoporteTecnico = $("#soporte-tecnico");
            $botonSoporteTecnico.removeClass("online offline away").addClass(status);
            if (status === "offline") {
                $botonSoporteTecnico.attr("title", "Soporte técnico - No hay operadores en este momento")
            } else {
                $botonSoporteTecnico.attr("title", "Soporte técnico - Hay operadores disponibles")
            }
        };

        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = '{{ tawk_url }}';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
{% endif %}

</body>
</html>
