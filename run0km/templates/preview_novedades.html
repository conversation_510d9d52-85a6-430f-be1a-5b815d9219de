{% load permisos %}
{% load personificacion %}
{% load prospectos_utils %}
{% load static from staticfiles %}

<!DOCTYPE html>
<html lang="es">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>{{ dominio.title }}</title>
    {% block css %}
        <link href="{% static 'css/normalize.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/bootstrap.min.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery.taghandler.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/nuevo-maquetado/runkm.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery-ui.min.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm-chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/jquery.growl.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        <link href="{% static 'css/runkm-alerta-chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
        {% if user.is_authenticated %}
            <link href="{% static 'css/chat.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
            <link href="{% static 'css/open-iconic-bootstrap.min.css' %}?v={{ version }}" type="text/css"
                  rel="stylesheet">
            <link href="{% static 'css/nuevo-maquetado/novedades.css' %}?v={{ version }}" type="text/css"
                  rel="stylesheet">
        {% endif %}
    {% endblock %}
    {% block js %}
        <script type="text/javascript" src="{% static 'js/jquery.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui-timepicker-addon.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.ui.datepicker-es.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/bootstrap.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spin.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/csrf_token.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spinners.js' %}?v={{ version }}"></script>
        <script type="text/javascript"
                src="{% static 'lib/moment/moment-with-locales.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/common.js' %}?v={{ version }}"></script>

        {% if user.is_authenticated %}
            <script type="text/javascript" src="{% static 'js/jquery.flexslider-min.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/alerta.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/jquery.growl.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/notificador.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="https://js.pusher.com/4.1/pusher.min.js"></script>
            <script type="text/javascript" src="{% static 'js/pusher_service.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat_de_meta.js' %}?v={{ version }}"></script>
            <script type="text/javascript">
                var alerta_mensajes_url = "{% url 'alerta-mensajes' %}";
                var llamados_alertados_url = "{% url 'llamados-alertados' %}";
                var ayuda_chat_url = "{% url 'ayuda-de-chat' %}";
                var ver_llamadas_proximas_url = "{% url 'prospectos' %}?filter_llamado=dia";
                var imagen_alerta = "{% static 'img/phone-blue-icon.png' %}";
                var titulo_alerta = "{{ dominio.title }}";
                var control_de_alertas;
                {% if corresponde_alertar_por_llamados %}
                    var delta_alerta_llamados = {{ delta_alerta_llamados }};
                    var alertas_de_llamado = {{ alertas_de_llamado|safe }};
                    control_de_alertas = new ControlDeAlertasDeLlamado(alertas_de_llamado, delta_alerta_llamados);
                    $(inicializarAlertas);
                {% endif %}
            </script>
            <script type="text/javascript">
                var listaDeChats, storageManagerListaDeChats, servicioDeChatDeMeta;
                {% if user.is_vendedor and user.vendedor.configuracion_de_servicios.habilitado_para_chatear %}
                    var servicioDeChatDeVentas;
                    $(function () {
                        var chatAreaId = 'chatarea';
                        var startCompulseSoundUrl = "{% static 'sound/start-compulse-alert.mp3' %}";
                        var startChatSoundUrl = "{% static 'sound/car-lock-sound-effect.mp3' %}";
                        var propuestasParaChatUrl = "{% url 'propuestas_para_chat' '0000' %}";
                        var envioDePropuestaUrl = "{% url 'enviar_propuesta_a_chat' '0000' %}";
                        listaDeChats = new ChatListBarView(
                            chatAreaId, startChatSoundUrl, propuestasParaChatUrl, envioDePropuestaUrl);

                        var pusherAppKey = "{{ PUSHER_APP_KEY }}";
                        var pusherAppCluster = "{{ PUSHER_APP_CLUSTER }}";
                        var channelName = "{{ user_pusher_channel_name }}";
                        var pusherNotificationService = new PusherService(pusherAppKey, pusherAppCluster, channelName);
                        var compulsaId = 'cont-popup-alerta-chat';
                        var enviarMensajeUrl = "{% url 'enviar-mensaje-de-chat' %}";
                        var buscarInformacionDelChatEnElServerUrl = "{% url 'mensajes-de-chat' %}";
                        var marcarChatComoLeidoUrl = "{% url 'marcar-chat-como-leido' '0000' %}";
                        var urlsCompulsas = {
                            'aceptar': "{% url 'aceptar_compulsa' '0000' %}",
                            'rechazar': "{% url 'rechazar_compulsa' '0000' %}"
                        };
                        servicioDeChatDeVentas = new ServicioDeChatDeCompulsas(
                            compulsaId, urlsCompulsas, enviarMensajeUrl, startCompulseSoundUrl,
                            buscarInformacionDelChatEnElServerUrl, marcarChatComoLeidoUrl);
                        storageManagerListaDeChats = listaDeChats.storageManager();
                        servicioDeChatDeVentas.configurarEnServicioDeNotificaciones(pusherNotificationService);
                        servicioDeChatDeVentas.configurarEnStorageManagerListaDeChats(storageManagerListaDeChats);
                        servicioDeChatDeVentas.iniciarChats();

                    });
                {% endif %}

                $(function () {
                    abrirModalNovedades()
                });

                function borrarStorage() {
                    if (storageManagerListaDeChats) {
                        storageManagerListaDeChats.eraseStorage();
                    }
                }

                function abrirModalNovedades() {
                    function agregarFondoNegro() {
                        $("body").prepend('<div class="black-overlay"></div>');
                    }

                    function quitarFondoNegro() {
                        $(".black-overlay").remove();
                    }

                    function quitarDialog(dialog) {
                        dialog.remove();
                    }

                    function inicializarSlider(dialog) {
                        var $customDirectionNav = $(dialog).find(".custom-navigation span");
                        $(dialog).flexslider({
                            animation: "slide",
                            controlNav: false,
                            customDirectionNav: $customDirectionNav,
                            start: function (slider) {
                                if (slider.count <= 1) {
                                    $customDirectionNav.parent().hide();
                                }
                                //jquery UI modal flexslider fix
                                $(window).trigger('resize');
                            }
                        });
                        cerrarSliderConCruz(dialog);
                    }

                    function cerrarSliderConCruz(slider) {
                        $(slider).find('.close-dialog').click(function () {
                            $(slider).dialog("close");
                        }.bind(slider));
                    }

                    var dialog = $("#modal_novedades");
                    dialog.dialog({
                        autoOpen: false,
                        resizable: false,
                        width: 900,
                        height: 600,
                        modal: true,
                        open: function () {
                            agregarFondoNegro();
                            inicializarSlider(this);
                        },
                        close: function () {
                            quitarFondoNegro();
                            quitarDialog(this);
                        }
                    });
                    dialog.dialog("open");
                }
            </script>
        {% endif %}

    {% endblock %}
</head>


<body>
<!-- Modal Para Habilitar Notificaciones -->
<div id="habilitar_notificaciones" title="Debe habilitar Notificaciones de Escritorio" style="display:none;"></div>
<!-- Modal para ir a ver llamadas programadas-->
<div id="ver_llamadas_proximas" title="Usted tiene llamadas programadas." style="display:none;"><br>¿Desea verlas ahora?
</div>
{% if novedades %}
    <!-- Modal Para Novedades -->
    <div id="modal_novedades" style="display:none;">
        <span class="oi oi-x noselect close-dialog"></span>
        <ul class="slides">
            {% for novedad in novedades %}
                <li>
                    {{ novedad.contenido.dibujar }}
                </li>
            {% endfor %}
        </ul>
        <div class="custom-navigation">
            <span class="oi oi-chevron-left flex-prev noselect"></span>
            <span class="oi oi-chevron-right flex-next noselect"></span>
        </div>
    </div>
{% endif %}


<div class="contenido-principal">
    {% block div-principal %}
    {% endblock %}
    <div class="header contenido">
        <div class="row">
            <div class="col-4">
                {% if dominio.imagen_url %}
                    <a href="{% url 'resumen' %}">
                        <img style="width: 315px; height: 120px" src="{{ dominio.imagen_url }}"/>
                    </a>
                {% endif %}
            </div>

            <div class="col-4">
            </div>

            <div class="col-4">
                {% if user.is_authenticated %}
                    <div class="panel-usuario">
                        <p>Bienvenido:</p>

                        <p>{{ user.get_full_name }}</p>
                        <img src="{% static 'img/barrita.png' %}"/>
                        {% if user.vendedor %}
                            {% if user.vendedor.tiene_conversaciones_habilitado %}
                                {% with user.vendedor.mensajes_no_leidos as mensajes_no_leidos %}
                                    <div id="noti_Container">
                                        <a id="icono-mensajes-whatsapp"
                                           class="mensajes-whatsapp{% if mensajes_no_leidos > 0 %}-cerrado{% endif %}"
                                           href="{% url 'lista-de-conversaciones' %}"
                                           title="Tiene {{ mensajes_no_leidos }} mensaje{% ifnotequal mensajes_no_leidos 1 %}s{% endifnotequal %} sin leer">
                                        </a>
                                        {% if mensajes_no_leidos > 0 %}
                                            <div class="noti_bubble">{{ mensajes_no_leidos }}</div>
                                        {% endif %}
                                    </div>

                                {% endwith %}
                            {% endif %}
                            <a class="aviso-llamados{% if user.vendedor.llamados_pendientes > 0 %}-red{% endif %}"
                               href="{% url 'prospectos' %}?filter_llamado=vencidos" title="Llamados vencidos">
                            </a>
                        {% endif %}
                        {% if user.vendedor.es_supervisor %}
                            {% with user.vendedor.cantidad_de_prospectos_sin_asignar as cantidad_de_prospectos_sin_asignar %}
                                {% if cantidad_de_prospectos_sin_asignar > 0 %}
                                    <a class="alerta-sin-asignar"
                                       href="{% url 'administracion' %}"
                                       title="{{ cantidad_de_prospectos_sin_asignar }} prospectos sin asignar">
                                    </a>
                                {% endif %}
                            {% endwith %}
                        {% endif %}
                        {% if user.vendedor.tiene_vendedores_con_prospectos_sin_trabajar %}
                            <a class="alerta-sin-trabajar"
                               href="{% url 'vendedores' %}" title="Sus vendedores tienen prospectos sin trabajar">
                            </a>
                        {% endif %}
                        <a class="cerrar-sesion" style="padding-right: 22px" href="{% url 'logout' %}"
                           onclick="borrarStorage()">Cerrar Sesión</a>
                    </div>
                    <div class="ajustar"></div>
                {% endif %}
            </div>
        </div>
        <div class="menu">
            {% if user.is_authenticated %}
                <a href="{% url 'occ-configuracion' %}" class="occ">
                    <img class="occ" src="{{ STATIC_URL }}img/icono-occ.png"/>
                </a>
            {% endif %}
        </div>
        <img src="{{ STATIC_URL }}img/sombra-menu.png"/>
    </div>
    <div id="cont-popup-alerta-chat" style="display:none; text-transform: uppercase;"></div>
    {% block content %}{% endblock %}
    {% if user.is_authenticated %}
        <div class="chatarea" id="chatarea"></div>
    {% endif %}
    {% if dominio.tiene_acceso_a_terminos_legales_oficiales %}
        <div class="isologotipo">
            <a href="http://www.jus.gob.ar/datos-personales.aspx/"><img src="{% static 'img/isologotipo.png' %}"
                                                                        alt="Isologotipo"
                                                                        title="El titular de los datos personales tiene la facultad de ejercer el derecho de acceso a los mismos en forma gratuita, a intervalos no inferiores a seis meses, salvo que se acredite un interés legítimo al efecto, conforme lo establecido en el artículo 14, inciso 3 de la Ley N° 25.326 La DIRECCIÓN NACIONAL DE PROTECCIÓN DE DATOS PERSONALES, órgano de control de la Ley N° 25.326, tiene la atribución de atender las denuncias y reclamos que se interpongan con relación al incumplimiento de las normas sobre protección de datos personales.">
            </a>
        </div>
    {% endif %}
</div>
<div class="footer">
    <div class="contenido">
        |<a class="secure" href="#">Secure Web Site</a>
        |<a href="#">{{ dominio.derechos }}</a>
        |<a href="{% url 'terminos-y-condiciones' %}">Términos y Condiciones Generales del Sitio Web</a>|
    </div>
</div>

</body>
</html>
