{% extends "occ_layout.html" %}
{% load static from staticfiles %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

{% block css %}
    <link href="{% static 'proposals/css/normalize.css' %}" type="text/css" rel="stylesheet" />
    <link href="//fonts.googleapis.com/css?family=Ubuntu:400,700,300italic,700italic" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="//code.jquery.com/ui/1.10.3/themes/smoothness/jquery-ui.css" />
    <link href="{% static 'css/react-spinner.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'proposals/css/jquery.taghandler.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'proposals/css/runkm.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'proposals/css/prospecto-occ.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'proposals/css/personificar.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'proposals/proposals.css' %}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/jquery.growl.css' %}?v={{ version }}" type="text/css" rel="stylesheet">
    <link href="{% static 'css/solapas.css'%}" type="text/css" rel="stylesheet"/>
    {{ block.super }}
    {% block 'css_header' %}
    {% endblock 'css_header' %}
{% endblock %}

{% block js %}
        <script type="text/javascript" src="{% static 'js/jquery.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/bootstrap.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery-ui-timepicker-addon.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.ui.datepicker-es.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spin.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/csrf_token.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/spinners.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'lib/moment/moment-with-locales.min.js' %}?v={{ version }}"></script>
        <script type="text/javascript" src="{% static 'js/common.js' %}?v={{ version }}"></script>

        {% if user.is_authenticated %}
            <script type="text/javascript" src="{% static 'js/jquery.flexslider-min.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/alerta.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/jquery.growl.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/notificador.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/system_unavailable.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="https://js.pusher.com/4.1/pusher.min.js"></script>
            <script type="text/javascript" src="{% static 'js/pusher_service.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/servicio_de_chat_de_meta.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/gestor_de_notificaciones.js' %}?v={{ version }}"></script>
            <script type="text/javascript" src="{% static 'js/novedades_controlador.js' %}?v={{ version }}"></script>
            <script type="text/javascript">
                var alerta_mensajes_url = "{% url 'alerta-mensajes' %}";
                var llamados_alertados_url = "{% url 'llamados-alertados' %}";
                var ayuda_chat_url = "{% url 'ayuda-de-chat' %}";
                var ver_llamadas_proximas_url = "{% url 'prospectos' %}?filter_llamado=dia";
                var imagen_alerta = "{% static 'img/phone-blue-icon.png' %}";
                var titulo_alerta = "{{ dominio.title }}";
                var control_de_alertas;
                {% if corresponde_alertar_por_llamados %}
                    var delta_alerta_llamados = {{ delta_alerta_llamados }};
                    var alertas_de_llamado = {{ alertas_de_llamado|safe }};
                    control_de_alertas = new ControlDeAlertasDeLlamado(alertas_de_llamado, delta_alerta_llamados);
                    $(inicializarAlertas);
                {% endif %}
            </script>
            <script type="text/javascript">
                var listaDeChats, storageManagerListaDeChats, servicioDeChatDeMeta;
                {% if user.is_vendedor %}
                    var servicioDeChatDeVentas, gestorDeNotificaciones;
                    $(function () {
                        var pusherAppKey = "{{ PUSHER_APP_KEY }}";
                        var pusherAppCluster = "{{ PUSHER_APP_CLUSTER }}";
                        var channelName = "{{ user_pusher_channel_name }}";
                        var pusherNotificationService = new PusherService(pusherAppKey, pusherAppCluster, channelName);
                        gestorDeNotificaciones = new GestorDeNotificaciones();
                        gestorDeNotificaciones.configurarEnServicioDeNotificaciones(pusherNotificationService);

                        {% if user.vendedor.configuracion_de_servicios.habilitado_para_chatear %}
                        var chatAreaId = 'chatarea';
                        var startCompulseSoundUrl = "{% static 'sound/start-compulse-alert.mp3' %}";
                        var startChatSoundUrl = "{% static 'sound/car-lock-sound-effect.mp3' %}";
                        var propuestasParaChatUrl = "{% url 'propuestas_para_chat' '0000' %}";
                        var envioDePropuestaUrl = "{% url 'enviar_propuesta_a_chat' '0000' %}";
                        listaDeChats = new ChatListBarView(
                            chatAreaId, startChatSoundUrl, propuestasParaChatUrl, envioDePropuestaUrl);

                        var compulsaId = 'cont-popup-alerta-chat';
                        var enviarMensajeUrl = "{% url 'enviar-mensaje-de-chat' %}";
                        var buscarInformacionDelChatEnElServerUrl = "{% url 'mensajes-de-chat' %}";
                        var marcarChatComoLeidoUrl = "{% url 'marcar-chat-como-leido' '0000' %}";
                        var urlsCompulsas = {
                            'aceptar': "{% url 'aceptar_compulsa' '0000' %}",
                            'rechazar': "{% url 'rechazar_compulsa' '0000' %}"
                        };

                        servicioDeChatDeVentas = new ServicioDeChatDeCompulsas(
                            compulsaId, urlsCompulsas, enviarMensajeUrl, startCompulseSoundUrl,
                            buscarInformacionDelChatEnElServerUrl, marcarChatComoLeidoUrl);
                        storageManagerListaDeChats = listaDeChats.storageManager();
                        servicioDeChatDeVentas.configurarEnServicioDeNotificaciones(pusherNotificationService);
                        servicioDeChatDeVentas.configurarEnStorageManagerListaDeChats(storageManagerListaDeChats);
                        servicioDeChatDeVentas.iniciarChats();

                        {#TODO: tener en cuenta los permisos, por ahora solo va funcionar si user.vendedor.configuracion_de_servicios.habilitado_para_chatear#}
                        var completarChatMetaUrl = "{% url 'conversacion-unificada-json' '0000' 'tipo' %}";
                        var enviarMensajeUrlMeta = "{% url 'enviar-mensaje-json' %}";
                        servicioDeChatDeMeta = new ServicioDeChatDeMeta(listaDeChats, completarChatMetaUrl, enviarMensajeUrlMeta);
                        servicioDeChatDeMeta.configurarEnServicioDeNotificaciones(pusherNotificationService);
                        servicioDeChatDeMeta.iniciarChats();
                        {% endif %}
                    });

                {% endif %}

                function borrarStorage() {
                    if (storageManagerListaDeChats) {
                        storageManagerListaDeChats.eraseStorage();
                    }
                }

                var urlListaNovedades = "{% url "lista_novedades" %}";
                var urlContarVisualizacionDeNovedad = "{% url "novedad_visualizada" %}";
                var novedadesControlador = new NovedadesControlador(urlListaNovedades, urlContarVisualizacionDeNovedad, "#modal_novedades");
                {% if debe_mostrar_novedades %}
                    $(function () {
                        novedadesControlador.abrirModalNovedades(false);
                    });
                {% endif %}
            </script>
        {% endif %}
        {% include "google-analytics/head-analytics.html" %}
    {% endblock %}


{% block occ_title %} OCC - Generador de Propuestas {% endblock %}
{% block occ_content %}
    <nav class="solapas">
        <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas" class="activo">Propuestas</a>
        {% if user.is_vendedor %}
            {% if user.vendedor.configuracion_de_servicios.puede_modificar_servicio_de_chat %}
        <a href="{% url 'occ-mis-chats' %}">Mis Chats</a>
            {% endif %}
            {% if user.vendedor.configuracion_de_servicios.tiene_sms_habilitado or user.vendedor.tiene_al_menos_una_campania %}
        <a href="{% url 'occ-campanias' %}">Campañas</a>
            {% endif %}
         {% endif %}
        <a href="{% url 'occ-configuracion' %}">Configuración</a>
    </nav>
     {% block 'main-content' %}
     {% endblock 'main-content' %}
{%endblock%}

</html>


