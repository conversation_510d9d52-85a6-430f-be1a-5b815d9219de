{% extends "admin/prospectos/change_list.html" %}
{% load i18n %}
{% block extrahead %}
    {{ block.super }}
    <link href="{{ STATIC_URL }}css/jquery.growl.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="{{STATIC_URL}}js/jquery.min.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery.growl.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/notificador.js"></script>
    <style>
        .url-button {
            background: white url("{{ STATIC_URL }}img/copy-link.png") no-repeat;
            padding: 5px 5px 21px 21px;
            vertical-align: middle;
            border: none;
            cursor: pointer;
            margin-left: 5px;
        }
    </style>
    <script>
        function copyUrlToClipboard(urlId) {
            var texto = $("#"+urlId).attr('href');
            var $temp = $("<input>");
            $("body").append($temp);
            $temp.val(texto).select();
            document.execCommand("copy");
            $temp.remove();
            notificador = new Notificador();
            notificador.notificarExito("Se copio un enlace al clipboard para compartir el reporte.");
        }
    </script>
{% endblock %}
