# coding=utf-8
from lib.api_client.errors import ClientValidationError
from lib.client_tecnom import TecnomSender, TecnomLead
from crms.integraciones.base import IntegradorDeProspectoConCRM

from prospectos.models import InformacionDeRedesSociales


class IntegradorConTecnom(IntegradorDeProspectoConCRM):

    def cliente_para(self, prospecto):
        return TecnomSender.new_for(
            url=self._configuracion.url(),
            username=self._configuracion.usuario(),
            password=self._configuracion.contrasenia())

    def generar_lead_para(self, prospecto):
        data = self._generar_datos_desde(prospecto=prospecto)
        try:
            request = TecnomLead.new_with(data=data)
        except ClientValidationError as exc:
            exc.key = 'Falló la generación del Lead para Tecnom para el Prospecto de id {0}'.format(prospecto.id)
            raise exc
        else:
            return request

    def _generar_datos_desde(self, prospecto):
        marca = prospecto.obtener_marca()
        modelos = prospecto.obtener_modelos()
        modelo = modelos.last()
        data = {
            'requestdate': prospecto.obtener_fecha_de_creacion(),
            'comment': '',
            'emails': [prospecto.obtener_email()] if prospecto.obtener_email() else [],
            # Soporte para Prospectos futuros con múltiples emails.
            'name': prospecto.obtener_nombre(),  # No tenemos separación entre nombre y apellido.
            'phones': self._generar_telefonos_para_data_tecnom(prospecto=prospecto),
            'addresses': self._generar_direcciones_para_data_tecnom(prospecto=prospecto),  # Es sólo un string.
            'make': marca.nombre(),  # Le dicen "make" a la marca.
            'model': modelo.nombre() if modelo else 'Sin modelo',  # Este campo es requerido
            'trim': '',  # Versión del Modelo.
            'year': '',  # Año del Modelo.
            'vendor_contact': '',
            'vendor_email': self._configuracion.vendor_email(),
            'vendor_name': self._vendor_name_para(prospecto),
            'provider_name': self._configuracion.nombre_de_proveedor() or self._configuracion.usuario(),
            'provider_service': '',
        }
        return data

    def _generar_direcciones_para_data_tecnom(self, prospecto):
        direcciones_tecnom = []
        direcciones = prospecto.obtener_informacion_de_redes_sociales().filter(tipo=InformacionDeRedesSociales.DIRECCION)
        if direcciones.exists():
            for direccion in direcciones:
                direcciones_tecnom.append(direccion.valor_a_mostrar())
        return direcciones_tecnom

    def _generar_telefonos_para_data_tecnom(self, prospecto):
        """
        :param prospecto: El Prospecto del cual se sacarán los telefonos a devolver
        :return: Lista de tuplas ('cellphone', '1234'), '1234' es un ejemplo
        """
        telefonos_tecnom = [('cellphone', telefono) for telefono in prospecto.telefonos_activos()]
        return telefonos_tecnom

    def _vendor_name_para(self, prospecto):
        if prospecto.tiene_vendedor() and prospecto.obtener_vendedor().email():
            return prospecto.obtener_vendedor().email()
        if prospecto.tiene_responsable() and prospecto.obtener_responsable().email():
            return prospecto.obtener_responsable().email()
        return self._configuracion.vendor_name()
