# coding=utf-8
from crms.client_spreadsheet.crm_spreadsheet_client import CRMSpreadsheetClient
from crms.client_spreadsheet.spreadsheet_lead import SpreadsheetLead
from crms.integraciones.base import IntegradorDeProspectoConCRM


class IntegradorConGoogleSpreadsheet(IntegradorDeProspectoConCRM):
    def generar_lead_para(self, prospecto):
        return SpreadsheetLead.nuevo_para(prospecto)

    def cliente_para(self, prospecto):
        return CRMSpreadsheetClient.nuevo_para(supervisor=prospecto.obtener_responsable())
