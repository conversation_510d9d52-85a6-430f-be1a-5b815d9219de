# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-09-13 02:08


import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('prospectos', '0063_auto_20160720_1505'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConfiguracionDePilot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('descripcion', models.CharField(max_length=128)),
                ('appkey', models.CharField(max_length=128)),
                ('suborigin_id', models.CharField(max_length=64)),
            ],
        ),
        migrations.CreateModel(
            name='OpcionClienteCRM',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('crm', models.PositiveIntegerField(choices=[(0, 'Pilot')])),
                ('configuracion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crms.ConfiguracionDePilot')),
                ('pedido', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clientes_crms', to='prospectos.PedidoDeProspecto')),
            ],
        ),
    ]
