from django.test import override_settings

from crms.integraciones import IntegradorConTecnom
from crms.tests.integraciones.test_integracion_de_pedidos_con_crm import IntegracionDePedidosConCRMTest
from prospectos.models import InformacionDeRedesSociales


class IntegradorConTecnomTest(IntegracionDePedidosConCRMTest):
    TEST_TECNOM_TEMPLATE_URL = 'https://%s.tecnomcrm.com/api-testing'

    @override_settings(TECNOM_TEMPLATE_URL=TEST_TECNOM_TEMPLATE_URL)
    def test_cliente_es_configurado_desde_los_parametros_de_la_configuracion(self):
        # Dado
        subdominio = 'one'
        configuracion = self._crear_configuracion_tecnom(subdominio=subdominio)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Cuando
        cliente = integrador.cliente_para(prospecto=None)

        # Entonces
        self.assertEqual(cliente.name(), 'Tecnom')
        self.assertEqual(cliente.username(), configuracion.usuario())
        self.assertEqual(cliente.password(), configuracion.contrasenia())
        self.assertEqual(cliente.url, self.TEST_TECNOM_TEMPLATE_URL % subdominio)

    def test_la_generacion_de_request_desde_prospecto_con_telefono_sin_email_es_exitosa(self):
        # Dado
        celular = '11778899'
        email = ''
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(celular, email, nombre, nombre_de_marca, nombres_de_modelos)

        vendor_name = 'hello'
        configuracion = self._crear_configuracion_tecnom(vendor_name=vendor_name, vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Cuando
        request = integrador.generar_lead_para(prospecto)
        data_para_tecnom = request.as_dict()

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=data_para_tecnom, fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[],
            vendor_name=prospecto.obtener_vendedor().email(), vendor_email='', nombre_de_proveedor=configuracion.usuario())

    def test_la_generacion_de_request_desde_prospecto_con_informacion_de_redes_sociales_es_exitosa(self):
        # Dado
        telefono = '12345678'
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(
            celular, email, nombre, nombre_de_marca, nombres_de_modelos, telefono_extra=telefono)
        direccion_desde_redes_sociales = self._crear_direccion_desde_redes_sociales_con(
            calle='Av Juan Bautista Lafuente 1100', localidad='Ciudad Autonoma Buenos Aires',
            provincia='CAPITAL FEDERAL', prospecto=prospecto)

        vendor_name = 'hello'
        nombre_de_proveedor = 'roberto'
        configuracion = self._crear_configuracion_tecnom(
            nombre_de_proveedor=nombre_de_proveedor, vendor_name=vendor_name, vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Cuando
        request = integrador.generar_lead_para(prospecto)
        data_para_tecnom = request.as_dict()

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=data_para_tecnom, fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular, telefono],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[direccion_desde_redes_sociales.valor_a_mostrar()],
            vendor_name=prospecto.obtener_vendedor().email(), vendor_email='', nombre_de_proveedor=nombre_de_proveedor)

    def test_vendor_name_usa_email_del_vendedor_cuando_vendedor_tiene_email_valido(self):
        # Dado
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(
            celular, email, nombre, nombre_de_marca, nombres_de_modelos)

        configuracion = self._crear_configuracion_tecnom(vendor_name='configuracion_vendor_name', vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Cuando
        request = integrador.generar_lead_para(prospecto)

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=request.as_dict(), fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[],
            vendor_name=prospecto.obtener_vendedor().email(), vendor_email='', nombre_de_proveedor=configuracion.usuario())

    def test_vendor_name_usa_email_del_supervisor_cuando_vendedor_no_tiene_email(self):
        # Dado
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(
            celular, email, nombre, nombre_de_marca, nombres_de_modelos)

        configuracion = self._crear_configuracion_tecnom(vendor_name='configuracion_vendor_name', vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Quitar email al vendedor
        self.quitar_email_al_vendedor_uno()

        # Cuando
        request = integrador.generar_lead_para(prospecto)

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=request.as_dict(), fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[],
            vendor_name='<EMAIL>', vendor_email='', nombre_de_proveedor=configuracion.usuario())

    @override_settings(MAILS_FALSOS=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    def test_vendor_name_usa_configuracion_cuando_vendedor_tiene_email_falso(self):
        # Dado
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(
            celular, email, nombre, nombre_de_marca, nombres_de_modelos)

        configuracion = self._crear_configuracion_tecnom(vendor_name='configuracion_vendor_name', vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Asignar email falso al vendedor
        self.vendedor_uno.editar_email_de_usuario('<EMAIL>')

        # Cuando
        request = integrador.generar_lead_para(prospecto)

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=request.as_dict(), fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[],
            vendor_name='<EMAIL>', vendor_email='', nombre_de_proveedor=configuracion.usuario())

    @override_settings(MAILS_FALSOS=["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"])
    def test_vendor_name_usa_configuracion_cuando_ni_vendedor_ni_supervisor_tienen_email_valido(self):
        # Dado
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'tecnom_nombre_1'
        nombre_de_marca = 'marqua_tecnom_1'
        nombres_de_modelos = ['modelo_tecnom_1', 'modelo_tecnom_2']
        prospecto = self._crear_prospecto_asinado_al_vendedor_uno_con(
            celular, email, nombre, nombre_de_marca, nombres_de_modelos)

        configuracion = self._crear_configuracion_tecnom(vendor_name='configuracion_vendor_name', vendor_email=None)
        integrador = IntegradorConTecnom.nuevo_para(configuracion=configuracion)

        # Quitar email al vendedor y supervisor
        self.quitar_email_al_vendedor_uno()
        self.quitar_email_al_supervisor_uno()

        # Cuando
        request = integrador.generar_lead_para(prospecto)

        # Entonces
        self._validar_generacion_de_data(
            data_tecnom=request.as_dict(), fecha_creacion=prospecto.obtener_fecha_de_creacion(),
            comentario='', emails=[email], telefonos=[celular],
            nombre=nombre, marca='Marqua_Tecnom_1', modelo='modelo_tecnom_2',
            direcciones=[],
            vendor_name='configuracion_vendor_name', vendor_email='', nombre_de_proveedor=configuracion.usuario())

    def quitar_email_al_vendedor_uno(self):
        self.vendedor_uno.editar_email_de_usuario('')

    def quitar_email_al_supervisor_uno(self):
        self.supervisor_uno.editar_email_de_usuario('')

    def _crear_prospecto_asinado_al_vendedor_uno_con(self, celular, email, nombre, nombre_de_marca, nombres_de_modelos,
                                                     mensaje='', telefono_extra=None, campos_extra=None):
        marca = self._crear_marcas_y_modelos(
            nombre_de_marca=nombre_de_marca, nombres_de_modelos=nombres_de_modelos)
        modelos = marca.modelos()
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(), telefono=celular,
            mensaje=mensaje, email=email, es_telefono_movil=True, nombre=nombre, proveedor='proveedor_1')
        if telefono_extra:
            from prospectos.models import TelefonoExtra
            TelefonoExtra.nuevo(prospecto=prospecto, vendedor=self.vendedor_uno, prefijo='', telefono=telefono_extra)

        if campos_extra:
            for nombre_campo, valor in campos_extra.items():
                self.creador_de_contexto.agregar_campo_extra_a(prospecto, nombre_campo, valor)

        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        from testing.test_utils import reload_model
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        gestor.reemplazar_marca_de(prospecto=prospecto, marca=marca)
        gestor.cambiar_modelos_de(prospecto=prospecto, modelos=modelos)
        prospecto = reload_model(prospecto)
        return prospecto

    def _crear_configuracion(self):
        return self._crear_configuracion_tecnom()

    def _validar_generacion_de_data(self, data_tecnom, fecha_creacion, comentario, emails, telefonos, nombre,
                                    direcciones, marca, modelo, vendor_name, vendor_email, nombre_de_proveedor):

        """
            Pendiente delegar esta validacion a un TecnonLeadTest, que valide que el lead genere correctamente
            el json esperado.
        """

        self.assertEqual(data_tecnom['prospect']['requestdate'], fecha_creacion.isoformat())
        self.assertEqual(data_tecnom['prospect']['customer']['comments'], comentario)
        contacts = data_tecnom['prospect']['customer']['contacts']
        self.assertEqual(len(contacts), 1)
        contact = contacts[0]
        self.assertEqual(contact['names'], [{'part': 'full', 'value': nombre}])
        self.assertEqual(contact['emails'], [{'value': email} for email in emails if email])
        self.assertEqual(contact['phones'], [{'type': 'cellphone', 'value': telefono} for telefono in telefonos])
        self.assertEqual(contact['addresses'], [{'street': direccion} for direccion in direcciones])
        self.assertEqual(data_tecnom['prospect']['vehicles'][0]['make'], marca)
        self.assertEqual(data_tecnom['prospect']['vehicles'][0]['model'], modelo)
        self.assertEqual(data_tecnom['prospect']['provider']['name'], {'value': nombre_de_proveedor})
        self.assertEqual(data_tecnom['prospect']['vendor']['vendorname']['value'], vendor_name)
        self.assertEqual(
            data_tecnom['prospect']['vendor']['contacts'], [{
                'emails': [{'value': vendor_email}] if vendor_email else [],
                'phones': []}
        ])

    def _direccion_json_con(self, calle='Av Juan Bautista Lafuente 1100',
                            localidad='Ciudad Autonoma Buenos Aires', provincia='CAPITAL FEDERAL'):
        return '{"Direccion": {"Provincia": {"Nombre": "%(provincia)s", "Pais": {"Nombre": "ARGENTINA"}}, ' \
               '"Tipo": "Direccion", "Calle": "%(calle)s", ' \
               '"Localidad": {"Nombre": "%(localidad)s", "Provincia": {"Nombre": "%(provincia)s", ' \
               '"Pais": {"Nombre": "ARGENTINA"}}}, "Pais": {"Nombre": "ARGENTINA"}, "CP": "C1406ETX"}, ' \
               '"Prioridad": 0, "Tipo": "Direccion", "Medio": {"Razon": "PERSONAL"}}' % {
            'provincia': provincia, 'calle': calle, 'localidad': localidad }

    def _crear_direccion_desde_redes_sociales_con(self, calle, localidad, provincia, prospecto):
        direccion = self._direccion_json_con(calle, localidad, provincia)
        return InformacionDeRedesSociales.nuevo(
            prospecto=prospecto, tipo=InformacionDeRedesSociales.DIRECCION, valor=direccion)

