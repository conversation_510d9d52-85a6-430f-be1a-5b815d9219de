# coding=utf-8
# Imagino un modelo con clase base Origen, y una herencia. Hoy por hoy no era necesario, pero esta el framework hecho.
# Por ahí estaría bueno para que guarde más contexto del origen, no sólo el nombre.
# Por ejemplo del CSV guardar filename. En este caso, estos objetos pasarían a ser más tipo "ContextoJotform", etc.


class OrigenDeIngreso(object):
    @classmethod
    def nombre(cls):
        raise NotImplementedError('Subclass responsibility')


class OrigenDeProspectoJotform(OrigenDeIngreso):
    @classmethod
    def nombre(cls):
        return 'Jotform'


class OrigenDeProspectoAPI(OrigenDeIngreso):
    @classmethod
    def nombre(cls):
        return 'API'


class OrigenDeProspectoCSV(OrigenDeIngreso):
    @classmethod
    def nombre(cls):
        return 'CSV'


class OrigenDeProspectoWeb(OrigenDeIngreso):
    @classmethod
    def nombre(cls):
        return 'Web'
