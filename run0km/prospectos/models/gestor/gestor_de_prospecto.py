# coding=utf-8
from datetime import timedelta

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone

from core.date_helper import DatetimeHelper
from core.notificador.decorators import sincronizar_prospecto_modificado
from core.support import make_aware_when_is_naive
from occ.models.anura import LlamadaRealizadaDeAnura
from prospectos.models import Marca, TarjetaDeProspecto, Llamado, Comentario, TelefonoExtra, EmailExtra, \
    LlamadaRealizada, Prospecto, DatosDeArchivadoParaProspecto, Finalizacion, Rechazo, LlamadoProgramadoCaduco, \
    InformacionAdicionalDeProspecto
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.exceptions import GestorDeProspectoError
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.models.logger import Logger
from prospectos.models.proponente_de_llamados import ProponenteDeLlamados
from prospectos.utils.selector_de_horario_de_agendado import SelectorDeHorarioDeProgramacionDeLlamadas
from vendedores.gestion_de_ventas import GestorDeVenta


class GestorDeProspecto(object):
    """Modelo las interacciones posibles de un vendedor o un supervisor sobre un prospecto"""

    # TODO: separa los gestores segun el cargo, asi quitamos las validaciones de rol

    @classmethod
    def nuevo_para(cls, rol):
        gestor = cls(rol)
        return gestor

    def __init__(self, rol):
        self._rol = rol
        self._logger = Logger()
        super(GestorDeProspecto, self).__init__()

    def rol(self):
        return self._rol

    @sincronizar_prospecto_modificado
    def modificar_informacion_adicional_de(self, prospecto, nombre, valor):
        nombre = nombre if nombre.startswith('_') else '_%s' % nombre
        self._validar_nombre_para_campo_de_informacion_adicional(nombre)
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        informacion_adicional = prospecto.obtener_informacion_adicional()
        valor = self._actualizar_valor_para_campos_numericos_y_fechas(valor, nombre)
        setattr(informacion_adicional, nombre, valor)
        informacion_adicional.full_clean()
        informacion_adicional.save()

    def cambiar_marca_de_nombre(self, prospecto, nombre_de_marca):
        marca = Marca.obtener_or_crear_con_nombre(nombre_de_marca)
        self.reemplazar_marca_de(prospecto, marca)

    def cambiar_marca_tarjeta_de_credito(self, prospecto, marca_de_tarjeta_de_credito):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        if marca_de_tarjeta_de_credito is None:
            self.eliminar_tarjeta_de_credito(prospecto)
        else:
            self.cambiar_o_definir_tarjeta_de_credito(prospecto, marca_de_tarjeta_de_credito)
        return marca_de_tarjeta_de_credito

    def eliminar_tarjeta_de_credito(self, prospecto):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        tarjetas = prospecto.tarjetas_de_credito.all()
        if tarjetas.exists():
            tarjetas[0].delete()

    def cambiar_o_definir_tarjeta_de_credito(self, prospecto, marca_de_tarjeta_de_credito):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        tarjetas = prospecto.tarjetas_de_credito.all()
        if len(tarjetas) == 0:
            tarjeta = TarjetaDeProspecto.nueva(prospecto=prospecto, marca=marca_de_tarjeta_de_credito)
        else:
            tarjeta = tarjetas[0]
            tarjeta._marca = marca_de_tarjeta_de_credito
        tarjeta.full_clean()
        tarjeta.save()

    def proponer_fecha_y_horario_de_llamado_para(self, prospecto, vendedor):
        proponente_de_llamados = ProponenteDeLlamados.nuevo()
        fecha_y_horario_propuestos = proponente_de_llamados.evaluar(prospecto, vendedor)
        return fecha_y_horario_propuestos

    def modificar_habilitacion_de_la_tarjeta_de_credito(self, tarjeta, esta_habilitada):
        tarjeta._esta_habilitada = esta_habilitada
        tarjeta.save()

    @sincronizar_prospecto_modificado
    def reemplazar_marca_de(self, prospecto, marca):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.cambiar_marca_por(marca=marca)
        if marca.es_marca_blanca():
            # Si se está borrando la marca, se deben borrar los modelos asociados.
            prospecto.reemplazar_modelos(modelos=[])

    @sincronizar_prospecto_modificado
    def cambiar_modelos_de(self, prospecto, modelos):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.reemplazar_modelos(modelos=modelos)
        if not prospecto.obtener_marca().es_marca_blanca():
            self._agregar_marca_de_modelos(modelos, prospecto)

    @sincronizar_prospecto_modificado
    def borrar_llamada_programada_para(self, prospecto):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        try:
            llamado = prospecto.llamado
        except Llamado.DoesNotExist:
            raise GestorDeProspectoError.llamado_no_existe()
        fecha = llamado.fecha
        self._borrar_llamada_programada(prospecto)
        self._logger.loggear_llamado_cancelado(prospecto, self.rol(), fecha)

    @sincronizar_prospecto_modificado
    def programar_nuevo_llamado_para(self, prospecto, fecha):
        if fecha < (timezone.now() - relativedelta(years=1)):
            raise ValidationError("La fecha del llamado no es válida")
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        self._validar_horario_entre_llamados(fecha=fecha)
        self._borrar_llamada_programada(prospecto)
        llamado = Llamado.nuevo(prospecto, fecha, vendedor_programador=self.rol())
        self._logger.loggear_llamado(llamado)
        return llamado

    def registrar_llamada_realizada(
            self, intento_de_llamado, fecha_comienzo, id_externo, duracion, audio_url):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=intento_de_llamado.prospecto())
        existe_el_llamado = LlamadaRealizadaDeAnura.objects.existe_con(intento_de_llamado)
        if existe_el_llamado:
            raise ValidationError(message="Ya existe una llamada realizada para este intento de llamado "
                                          "(id intento: %s)" % intento_de_llamado.id)
        llamada_realizada = LlamadaRealizadaDeAnura.nueva(
            intento_de_llamado, fecha_comienzo, id_externo, duracion, audio_url)
        self._logger.loggear_llamada_realizada(llamada_realizada)
        return llamada_realizada

    @sincronizar_prospecto_modificado
    def comentar_prospecto(self, prospecto, texto, audio=None, es_automatico=False, fecha=None):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        comentario = Comentario.nuevo(prospecto, self.rol(), texto, audio, automatico=es_automatico, fecha=fecha)
        self._borrar_llamada_programada_de_mas_de_un_dia_de_antiguedad(prospecto)
        return comentario

    @sincronizar_prospecto_modificado
    def agregar_comentario_con_semantica(self, prospecto, etiqueta_semantica, fecha=None):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        comentario = Comentario.nuevo_con_semantica(prospecto, self.rol(), etiqueta_semantica, fecha)
        self._borrar_llamada_programada_de_mas_de_un_dia_de_antiguedad(prospecto)

        # TODO: pensar como relacionar mensaje_a_incontactable_por_comentario con el gestor.
        #  Idea, agregar un colaborador que sea como un asistente de gestion, en el cual le avisamos que se agrego un
        #  comentario?. Habria que unificar
        # con el caso de finalizacion, que se esta usando las signals de django al_finalizar
        from prospectos import tasks
        transaction.on_commit(lambda: tasks.mensaje_a_incontactable_por_comentario.delay(comentario_pk=comentario.pk))
        return comentario

    @sincronizar_prospecto_modificado
    def agregar_telefono_extra_a_prospecto(self, prospecto, telefono):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        telefono = TelefonoExtra.nuevo(prospecto=prospecto, vendedor=self.rol(), telefono=telefono, prefijo='')
        self._logger.loggear_extra(telefono, 'tel')
        return telefono

    @sincronizar_prospecto_modificado
    def agregar_telefonos_extra_a_prospecto(self, prospecto, telefonos):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        telefonos_a_crear = []
        for telefono in telefonos:
            telefono_a_crear = TelefonoExtra(prospecto=prospecto, vendedor=self.rol(), telefono=telefono, prefijo='')
            telefonos_a_crear.append(telefono_a_crear)
            self._logger.loggear_extra_pre_creacion(nombre_de_campo='Teléfono', prospecto=prospecto,
                                                    vendedor=self.rol())
        TelefonoExtra.objects.bulk_create(telefonos_a_crear)
        return telefonos

    @sincronizar_prospecto_modificado
    def agregar_emails_extra_a_prospecto(self, prospecto, emails):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        emails_a_crear = []
        for email in emails:
            email_a_crear = EmailExtra(prospecto=prospecto, vendedor=self.rol(), email=email, activo=True)
            emails_a_crear.append(email_a_crear)
            self._logger.loggear_extra_pre_creacion(nombre_de_campo='Email', prospecto=prospecto,
                                                    vendedor=self.rol())
        EmailExtra.objects.bulk_create(emails_a_crear)
        return emails

    @sincronizar_prospecto_modificado
    def agregar_campos_extras_a(self, prospecto, datos_extra):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        repartidor = RepartidorDeProspectos.nuevo()
        repartidor.agregar_campos_extras_a(prospecto, datos_extra)

    @sincronizar_prospecto_modificado
    def cambiar_nombre_alternativo(self, prospecto, nombre):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.nombre_alternativo = nombre
        prospecto.full_clean()
        prospecto.save()

    @sincronizar_prospecto_modificado
    def toggle_telefono_activo(self, prospecto):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.telefono_activo = not prospecto.telefono_activo
        prospecto.full_clean()
        prospecto.save()

    @sincronizar_prospecto_modificado
    def toggle_email_activo(self, prospecto):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.email_activo = not prospecto.email_activo
        prospecto.full_clean()
        prospecto.save()

    @sincronizar_prospecto_modificado
    def toggle_telefono_extra_activo(self, prospecto, id_telefono):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        try:
            extra = TelefonoExtra.objects.get(id=id_telefono, prospecto=prospecto)
        except TelefonoExtra.DoesNotExist:
            raise GestorDeProspectoError.telefono_extra_no_existe(id_telefono)
        else:
            extra.activo = not extra.activo
            extra.full_clean()
            extra.save()
            return extra

    @sincronizar_prospecto_modificado
    def toggle_email_extra_activo(self, prospecto, id_telefono):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        try:
            extra = EmailExtra.objects.get(id=id_telefono, prospecto=prospecto)
        except EmailExtra.DoesNotExist:
            raise GestorDeProspectoError.email_extra_no_existe(id_telefono)
        else:
            extra.activo = not extra.activo
            extra.full_clean()
            extra.save()
            return extra

    @sincronizar_prospecto_modificado
    def agregar_email_extra_a_prospecto(self, prospecto, email):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        email = EmailExtra.nuevo(prospecto=prospecto, vendedor=self.rol(), email=email, activo=True)
        self._logger.loggear_extra(email, 'mail')
        return email

    @sincronizar_prospecto_modificado
    def finalizar_prospecto(self, prospecto, comentario='', motivo=None, otro_motivo=None, fecha=None):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        self._validar_prospecto_en_proceso(prospecto=prospecto)
        finalizacion = self._finalizar_prospecto(
            prospecto=prospecto, comentario=comentario, motivo=motivo, otro_motivo=otro_motivo, fecha=fecha)
        return finalizacion

    @sincronizar_prospecto_modificado
    def reactivar_seguimiento(self, prospecto):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        prospecto.reactivar_seguimiento()
        self._logger.loggear_reactivacion_de_seguimiento(prospecto, self.rol())

    def horarios_para_llamados(self, prospectos, cantidad_de_llamados_diarios):
        prospectos = prospectos.consulta_limpia()
        self._validar_puede_gestionar_prospectos(prospectos)
        vendedores_ids = prospectos.asignado_a_algun_vendedor().vendedores().distinct()
        fechas = []
        for vendedor_id in vendedores_ids:
            prospectos_de_vendedor = prospectos.con_vendedor(vendedor_id)
            cantidad = prospectos_de_vendedor.count()
            fechas_para_vendedor = self._horarios_para_llamados(vendedor_id, cantidad, cantidad_de_llamados_diarios)
            fechas.extend(fechas_para_vendedor)
        return fechas

    @sincronizar_prospecto_modificado
    def programar_llamadas_en_el_mejor_horario_a_prospectos(self, prospectos, cantidad_de_llamados_diarios):
        prospectos = prospectos.consulta_limpia()
        self._validar_puede_gestionar_prospectos(prospectos)
        vendedores_ids = prospectos.asignado_a_algun_vendedor().vendedores().distinct()
        for vendedor_id in vendedores_ids:
            prospectos_de_vendedor = prospectos.con_vendedor(vendedor_id)
            self._programar_llamadas_en_el_mejor_horario(
                vendedor_id, prospectos_de_vendedor, cantidad_de_llamados_diarios)

    def agregar_llamada_realizada(self, prospecto, fecha_inicio, duracion):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        fecha_inicio_aware = make_aware_when_is_naive(fecha_inicio)

        llamados_iguales = LlamadaRealizada.objects.filter(
            prospecto=prospecto, fecha_comienzo=fecha_inicio_aware, duracion=duracion)
        if llamados_iguales.exists():
            return llamados_iguales.first()

        llamada = LlamadaRealizada.nueva(prospecto=prospecto, fecha_comienzo=fecha_inicio_aware, duracion=duracion)
        self._borrar_llamado_programada_del_mismo_dia(fecha_inicio_aware, prospecto)
        comentario = self._comentario_para_llamada_programada(duracion, fecha_inicio_aware)
        self._logger.loggear(prospecto, self.rol(), comentario)
        return llamada

    # TODO: agregar llamada realizada de anura desde este gestor

    def agregar_formulario_de_llamada_realizada(self, prospecto, fecha_inicio, duracion, respuestas):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        fecha_inicio_aware = make_aware_when_is_naive(fecha_inicio)
        llamada = LlamadaRealizada.obtener_o_crear(
            prospecto=prospecto, fecha_comienzo=fecha_inicio_aware, duracion=duracion)
        llamada.agregar_respuestas(respuestas)

    @sincronizar_prospecto_modificado
    def cargar_venta(self, prospecto, marca, modelo, fecha_de_realizacion, numero_de_contrato, precio):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        gestor = GestorDeVenta()
        venta = gestor.nueva_venta(
            prospecto=prospecto, vendedor=self.rol(), marca=marca, modelo=modelo, precio=precio,
            fecha_de_realizacion=fecha_de_realizacion, numero_de_contrato=numero_de_contrato)
        self._borrar_llamada_programada(prospecto)
        return venta

    # Para Supervisores o Gerente
    @sincronizar_prospecto_modificado
    def cancelar_venta(self, prospecto, motivo_de_cancelacion, estado_a_pasar):
        self._validar_rol_supervisor_o_gerente()
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        if prospecto.tiene_venta_no_cancelada():
            venta = prospecto.obtener_venta_activa()
            gestor = GestorDeVenta()
            gestor.cancelar_venta(rol=self.rol(), venta=venta, motivo=motivo_de_cancelacion)
            self._logger.loggear_venta_cancelada(prospecto=prospecto, supervisor=prospecto.obtener_responsable(),
                                                 nombre_del_evaluador=prospecto.obtener_gestor().full_name())
        self._cambiar_estado_de_prospecto(estado_a_pasar, motivo_de_cancelacion, prospecto)

    # Para Supervisores
    def asignar_prospecto_a_vendedor(self, prospecto, vendedor, modificar_fecha_asignacion=True):
        self.rol().validar_cargo_supervisor()
        if vendedor is not None and vendedor.responsable() != self.rol():
            raise ValidationError('El %s no es responsable del vendedor %s' % (self.rol(), vendedor))
        prospectos = Prospecto.objects.filter(pk=prospecto.pk)
        repartidor = RepartidorDeProspectos()
        if vendedor is None:
            repartidor.asignar_responsable_a(prospecto, self.rol())
        else:
            repartidor.asignar_prospectos_a(
                vendedor=vendedor, prospectos=prospectos, modificar_fecha_asignacion=modificar_fecha_asignacion)

    # Para Supervisores
    def archivar_prospectos(self, prospectos):
        self.rol().validar_cargo_supervisor()
        prospectos_id = prospectos.ids()
        prospectos_filtrados = Prospecto.objects.con_ids(list(prospectos_id))
        prospectos_sin_datos_de_archivado = Prospecto.objects.sin_datos_de_archivado(qs=prospectos_filtrados)
        prospectos_sin_datos_de_archivado_ids = prospectos_sin_datos_de_archivado.values_list('id', flat=True)
        self._validar_es_responsable_de_prospectos(self.rol(), prospectos=prospectos_filtrados)
        archivados = [DatosDeArchivadoParaProspecto(prospecto_id=prospecto_id) for prospecto_id in
                      prospectos_sin_datos_de_archivado_ids]
        DatosDeArchivadoParaProspecto.objects.bulk_create(archivados)
        RepartidorDeProspectos.nuevo().quitar_asignacion_a_prospectos(prospectos_filtrados)

    # Para Supervisores
    @transaction.atomic
    def cambiar_de_estado_a_nuevo_o_en_proceso(self, prospectos, nuevo_estado):
        """
            Modifica el estado de los prospecto a nuevo_estado (nuevo o en proceso). Los prospectos ya vendidos
            no se le modifica el estado.

            Si nuevo_estado es finalizado, los prospectos ya finalizados no se le cambia el estado.
        """
        prospectos_filtrados = prospectos.consulta_limpia()
        self.rol().validar_cargo_supervisor()
        self._validar_puede_gestionar_prospectos(prospectos_filtrados)
        self._validar_que_sea_estado_nuevo_o_en_proceso(nuevo_estado)
        cantidad = prospectos_filtrados.count()
        prospectos_filtrados = prospectos_filtrados.excluir_los_de_estado_vendido()
        cantidad_de_no_vendidos = prospectos_filtrados.count()
        excluidos_por_vendidos = cantidad - cantidad_de_no_vendidos

        prospectos_filtrados.borrar_finalizaciones()
        prospectos_filtrados.update(estado=nuevo_estado)
        return self._resultado_de_cambio_de_estado(
            cantidad_de_no_vendidos, excluidos_por_vendidos=excluidos_por_vendidos)

    @transaction.atomic
    def cambiar_de_estado_a_finalizado(self, prospectos, motivo=None, otro_motivo=None, texto=''):
        """
            Modifica el estado de los prospectos a finalizado. Los prospectos ya vendidos o ya finalizados
            no se le modifica el estado.
        """
        prospectos_filtrados = prospectos.consulta_limpia()
        self.rol().validar_cargo_supervisor()
        self._validar_puede_gestionar_prospectos(prospectos_filtrados)
        if motivo is None and not otro_motivo:
            raise ValidationError('Debe ingresar un motivo de finalización')
        if motivo is not None:
            otro_motivo = ''

        cantidad = prospectos_filtrados.count()
        prospectos_filtrados = prospectos_filtrados.excluir_los_de_estado_vendido()
        cantidad_de_no_vendidos = prospectos_filtrados.count()
        excluidos_por_vendidos = cantidad - cantidad_de_no_vendidos

        prospectos_filtrados = prospectos_filtrados.excluir_los_de_estado_finalizados()
        cantidad_de_actualizados = prospectos_filtrados.count()
        excluidos_por_finalizados = cantidad_de_no_vendidos - cantidad_de_actualizados
        self._finalizar_prospectos(
            prospectos_filtrados, motivo=motivo, otro_motivo=otro_motivo, texto=texto)
        return self._resultado_de_cambio_de_estado(
            cantidad_de_actualizados,
            excluidos_por_vendidos=excluidos_por_vendidos, excluidos_por_finalizados=excluidos_por_finalizados)

    def _validar_que_sea_estado_nuevo_o_en_proceso(self, nuevo_estado):
        if nuevo_estado not in [Prospecto.NUEVO, Prospecto.EN_PROCESO]:
            raise ValidationError('El estado %s no es un estado valido.' % nuevo_estado)

    def _resultado_de_cambio_de_estado(
            self, cantidad_actualizados, excluidos_por_vendidos, excluidos_por_finalizados=0):
        resultado = 'Se ha actualizado el estado de %s prospectos.' % cantidad_actualizados
        if excluidos_por_vendidos > 0:
            resultado += ' Se ha excluido %s prospectos por estar ya vendidos.' % excluidos_por_vendidos
        if excluidos_por_finalizados > 0:
            resultado += ' Se ha excluido %s prospectos por estar ya finalizados.' % excluidos_por_finalizados
        return resultado

    def _finalizar_prospectos(self, prospectos, motivo=None, otro_motivo=None, texto='', fecha=None):
        fecha = fecha or timezone.now()
        vendedor = self.rol()
        finalizaciones = []
        for prospecto in prospectos:
            finalizacion = Finalizacion(datetime=fecha, prospecto=prospecto,
                                        vendedor=vendedor, comentario=texto,
                                        motivo=motivo, otro_motivo=otro_motivo)
            finalizaciones.append(finalizacion)

        prospectos.borrar_llamadas_programadas()
        prospectos.update(estado=Prospecto.FINALIZADO)
        finalizaciones_creadas = Finalizacion.objects.bulk_create(finalizaciones)
        self._logger.loggear_lista_de_finalizaciones(finalizaciones_creadas)

    def transferir_prospecto_entre_vendedores(self,prospecto, vendedor_destinatario):
        self.validar_asignacion_del_prospecto(rol=self.rol(), prospecto=prospecto)
        self._validar_vendedor_destinatario_es_distinto_al_actual(prospecto, vendedor_destinatario)
        repartidor = RepartidorDeProspectos()
        repartidor.asignar_prospecto_a(vendedor_destinatario, prospecto)
        self._logger.loggear(prospecto, vendedor_destinatario, self.rol().full_name() + ' transfirio el prospecto a '
                             + vendedor_destinatario.full_name())


    # Para Supervisores
    @transaction.atomic
    def rechazar_prospectos(self, prospectos):
        """
            Asume que todos los prospectos son del supervisor
        """
        self.rol().validar_cargo_supervisor()
        rechazos = []
        for prospecto in prospectos:
            rechazo = Rechazo(prospecto=prospecto, responsable=self.rol())
            rechazos.append(rechazo)
        Rechazo.objects.bulk_create(rechazos)
        ids_prospectos = list(prospectos.values_list('id', flat=True))
        prospectos = Prospecto.objects.filter(id__in=ids_prospectos)
        self._pasar_prospectos_a_nuevo(prospectos)
        prospectos = Prospecto.objects.filter(id__in=ids_prospectos)
        RepartidorDeProspectos.nuevo().quitar_responsable_a_prospectos(prospectos)

    # Para Gerentes
    def transferir_todo_de_entre_supervisores(self, supervisor, supervisor_destinatario):
        self._validar_rol_gerente()
        prospectos = Prospecto.objects.activos(supervisor.prospectos)
        repartidor = RepartidorDeProspectos.nuevo()
        repartidor.asignar_prospectos_a_responsable(supervisor_destinatario, prospectos)
        equipos = supervisor.equipos
        equipos.update(supervisor=supervisor_destinatario)
        vendedores_a_cargo = supervisor.vendedores
        vendedores_a_cargo.update(supervisor=supervisor_destinatario)
        # Prospectos de sus nuevos vendedores
        repartidor.asignar_responsable_y_mantener_vendedor(supervisor_destinatario, supervisor.prospectos_a_cargo.all())

    # Para Gerentes
    def transferir_prospectos_sin_vendedor_entre_supervisores(self, supervisor, supervisor_destinatario):
        self._validar_rol_gerente()
        prospectos = supervisor.prospectos_a_cargo.sin_vendedor()
        RepartidorDeProspectos.nuevo().asignar_prospectos_a_responsable(supervisor_destinatario, prospectos)

    # Privados
    def _cambiar_estado_de_prospecto(self, estado_a_pasar, motivo_de_cancelacion, prospecto):
        if estado_a_pasar == Prospecto.EN_PROCESO:
            self._pasar_prospecto_a_en_proceso(prospecto=prospecto)
        else:
            if not prospecto.finalizado:
                self._finalizar_prospecto(prospecto=prospecto, comentario=motivo_de_cancelacion)

    def _finalizar_prospecto(self, prospecto, comentario, motivo=None, otro_motivo=None, fecha=None):
        vendedor = prospecto.obtener_vendedor() or prospecto.obtener_responsable()
        finalizacion = Finalizacion.nueva_para(fecha=fecha, prospecto=prospecto,
                                               vendedor=vendedor, comentario=comentario,
                                               motivo=motivo, otro_motivo=otro_motivo)
        prospecto.cambiar_a_estado(Prospecto.FINALIZADO)
        self._borrar_llamada_programada(prospecto)
        self._logger.loggear_finalizacion(finalizacion=finalizacion)
        return finalizacion

    def _programar_llamadas_en_el_mejor_horario(self, vendedor_id, prospectos, cantidad_de_llamados_diarios):
        llamados = self._generar_llamados(prospectos, vendedor_id, cantidad_de_llamados_diarios)
        prospectos.borrar_llamados()
        llamados = Llamado.objects.bulk_create(llamados)
        self._logger.loggear_lista_de_llamados(llamados)
        return llamados

    def _llamados_excluidos_de(self, prospecto):
        if prospecto.tiene_llamado():
            llamados_excluidos = [prospecto.llamado]
        else:
            llamados_excluidos = []
        return llamados_excluidos

    def _generar_llamados(self, prospectos, vendedor_id, cantidad_de_llamados_diarios):
        cantidad = prospectos.count()
        fechas = self._horarios_para_llamados(vendedor_id, cantidad, cantidad_de_llamados_diarios)
        llamados = []
        for indice in range(0, cantidad):
            prospecto = prospectos[indice]
            fecha = fechas[indice]
            llamado = Llamado(prospecto=prospecto, vendedor_id=vendedor_id, fecha=fecha)
            llamados.append(llamado)
        return llamados

    def _horarios_para_llamados(self, vendedor, cantidad, cantidad_de_llamados_diarios):
        helper = DatetimeHelper()
        ahora = helper.as_start_of_day(helper.now())
        llamados = Llamado.objects.de_vendedor(vendedor).posteriores_a(ahora)
        selector = SelectorDeHorarioDeProgramacionDeLlamadas.nuevo_con_limite_diario(llamados, cantidad_de_llamados_diarios, self._minutos_entre_llamados_propuestos(), settings.SELECTOR_HORARIOS_MINUTOS_DE_MARGEN)
        fechas = selector.seleccionar(cantidad)
        return fechas

    def _borrar_llamado_programada_del_mismo_dia(self, fecha_inicio_aware, prospecto):
        if prospecto.tiene_llamado() and prospecto.llamado.fecha.date() == fecha_inicio_aware.date():
            self._borrar_llamada_programada(prospecto)

    def _borrar_llamada_programada_de_mas_de_un_dia_de_antiguedad(self, prospecto):
        try:
            llamado = prospecto.llamado
        except Llamado.DoesNotExist:
            return
        ayer = timezone.now() - timezone.timedelta(days=1)
        llamado_tiene_mas_de_un_dia = llamado.datetime < ayer
        if llamado_tiene_mas_de_un_dia:
            self._borrar_llamada_programada(prospecto)

    @transaction.atomic
    def _borrar_llamada_programada(self, prospecto):
        """
            Validamos que el llamado este persistido, ya que en la Sincronizacion nos quedan dirty
            con relacion a la base de datos.
        """
        if prospecto.tiene_llamado() and prospecto.llamado and prospecto.llamado.id is not None:
            LlamadoProgramadoCaduco.nuevo_desde(prospecto.llamado)
            prospecto.borrar_llamado()

    def _agregar_marca_de_modelos(self, modelos, prospecto):
        # Se modifican las marcas del prospecto (sin marcas) con las marcas de los modelos agregados.
        lista_de_modelos = list(modelos)
        if not prospecto.tiene_marca() and len(lista_de_modelos) > 0:
            marca = (lista_de_modelos[0]).marca()
            self._validar_que_sean_de_la_marca(modelos, marca)
            prospecto.cambiar_marca_por(marca=marca)

    def _actualizar_valor_para_campos_numericos_y_fechas(self, valor, nombre):
        if nombre in InformacionAdicionalDeProspecto.campos_numericos_y_fechas() and not valor:
            return None
        else:
            return valor

    def _validar_nombre_para_campo_de_informacion_adicional(self, nombre):
        if nombre not in InformacionAdicionalDeProspecto.nombres_validos_de_carga():
            raise ValidationError('La celda %s no existe ó no puede ser modificada.' % nombre)

    def _comentario_para_llamada_programada(self, duracion, fecha_inicio_aware):
        duracion_formateada = "desconocida"
        if duracion is not None:
            duracion = timezone.timedelta(seconds=duracion)
            duracion_formateada = self._format_timedelta(duracion.total_seconds())

        comentario = fecha_inicio_aware.strftime('Se realizó un llamado a las %H:%Mhs ')
        comentario += 'de duración %s.' % duracion_formateada
        return comentario

    def _pasar_prospectos_a_nuevo(self, prospectos):
        prospectos.update(estado=Prospecto.NUEVO)

    def _pasar_prospecto_a_en_proceso(self, prospecto):
        prospecto.cambiar_a_estado(Prospecto.EN_PROCESO)
        evaluador = prospecto.responsable
        comentario = "El prospecto fue puesto en proceso por %s" % evaluador.full_name()
        self._logger.loggear(prospecto, evaluador, comentario)

    def _validar_comentario(self, comentario):
        if comentario is None or comentario == "":
            raise ValidationError('El Prospecto necesita un comentario para quedar finalizado o en proceso.')

    def _validar_prospecto_en_proceso(self, prospecto):
        if not prospecto.en_proceso:
            raise ValidationError('El prospecto debe estar en proceso para ser finalizado.')

    def validar_asignacion_del_prospecto(self, rol, prospecto):
        if rol is None or not rol.tiene_asignado(prospecto):
            mensaje = self.mensaje_vendedor_no_tiene_a_cargo_el_prospecto()
            raise ValidationError(message=mensaje)

    def _validar_puede_gestionar_prospectos(self, prospectos):
        if self.rol().es_supervisor():
            self._validar_es_responsable_de_prospectos(self.rol(), prospectos)
        elif self.rol().es_vendedor():
            self._validar_es_vendedor_de_prospectos(self.rol(), prospectos)
        else:
            raise ValidationError('No puede gestionar prospectos.')

    def _validar_es_responsable_de_prospectos(self, responsable, prospectos):
        prospectos_con_otro_responsable = prospectos.exclude(responsable=responsable)
        if prospectos_con_otro_responsable.exists():
            raise ValidationError('El vendedor no tiene todos los prospectos seleccionados.')

    def _validar_es_vendedor_de_prospectos(self, vendedor, prospectos):
        prospectos_de_otro_vendedor = prospectos.exclude(vendedor=vendedor)
        if prospectos_de_otro_vendedor.exists():
            raise ValidationError('El supervisor no tiene a cargo todos los prospectos seleccionados.')

    def _validar_que_sean_de_la_marca(self, modelos, marca):
        if not marca.es_marca_blanca():
            for modelo in modelos:
                if modelo.marca() != marca:
                    raise ValidationError('Los modelos seleccionados deben ser de la misma marca.')

    def _validar_si_es_staff_de(self, supervisor, vendedor):
        if not supervisor:
            raise ValidationError('Debe indicar supervisor.')
        if vendedor and vendedor.responsable() != supervisor:
            raise ValidationError('El vendedor no es parte del staff del supervisor.')

    def _format_timedelta(self, duracion):
        horas = duracion // 3600
        minutos = duracion % 3600 // 60
        segundos = duracion % 60
        formato = self._formatear_si_es_mayor_a_cero(horas, unidad='hs')
        formato += self._formatear_si_es_mayor_a_cero(minutos, unidad='min')
        formato += self._formatear_si_es_mayor_a_cero(segundos, unidad='s')
        return formato.strip()

    def _formatear_si_es_mayor_a_cero(self, valor, unidad):
        if valor > 0:
            return '%d%s ' % (valor, unidad)
        else:
            return ''

    def _validar_rol_gerente(self):
        if not self.rol().es_gerente():
            raise ValidationError('El usuario debe ser un gerente.')

    def _validar_rol_supervisor_o_gerente(self):
        if not self.rol().es_supervisor() and not self.rol().es_gerente():
            raise ValidationError('El usuario debe ser un supervisor o gerente.')

    def _validar_vendedor_destinatario_es_distinto_al_actual(self, prospecto, vendedor_destinatario):
        if prospecto.tiene_vendedor_asignado_a(vendedor_destinatario):
            raise ValidationError('El prospecto ya esta asignado a ese vendedor.')

    def _validar_horario_entre_llamados(self, fecha):
        intervalo_de_tiempo_entre_llamados = self.rol().obtener_concesionaria().minutos_entre_llamados()

        if self._tiene_llamados_entre(fecha, intervalo_de_tiempo_entre_llamados):
            raise ValidationError(u'Ya existe un llamado en ese rango de tiempo.')

    def _tiene_llamados_entre(self, fecha, intervalo_de_tiempo_entre_llamados):
        minutos_entre_llamados = timedelta(minutes=intervalo_de_tiempo_entre_llamados)
        return Llamado.objects.de_vendedor(
            vendedor=self.rol()).entre_fechas(
            fecha - minutos_entre_llamados,
            fecha + minutos_entre_llamados).exists()

    @classmethod
    def mensaje_vendedor_no_tiene_a_cargo_el_prospecto(cls):
        return 'El vendedor o gerente no tiene a cargo el prospecto.'

    def _minutos_entre_llamados_propuestos(self):
        return self.rol().obtener_concesionaria().minutos_entre_llamados()




