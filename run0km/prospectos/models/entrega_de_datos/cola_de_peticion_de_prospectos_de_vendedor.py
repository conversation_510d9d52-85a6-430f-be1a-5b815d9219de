from datetime import timedelta

from django.core.exceptions import ValidationError

from core.date_helper import DatetimeHelper
from core.models import Sistema
from prospectos.models.entrega_de_datos.gestor_de_peticiones_de_prospectos_por_vendedor import \
    GestorDePeticionesDeProspectosPorVendedor
from prospectos.models.peticion_de_prospecto_por_parte_del_vendedor import PeticionDeProspectoPorParteDelVendedor


class ColaDePeticionDeProspectoDeVendedor:

    def __init__(self, tamanio_maximo, notificador_de_resultado):
        super().__init__()
        self._tamanio_maximo = tamanio_maximo
        self._notificador_de_resultado = notificador_de_resultado


    @classmethod
    def nuevo_con(cls, tamanio_maximo, notificador_de_resultado):
        return cls(tamanio_maximo, notificador_de_resultado)

    def esta_vacia(self):
        return self.cantidad_de_peticiones() == 0

    def cantidad_de_peticiones(self):
        return self._peticiones_pendientes().count()

    def procesar(self):
        if self.esta_vacia():
            raise ValueError('No hay pedidos de prospecto para procesar')

        peticion = self._siguiente_peticion()
        gestor = GestorDePeticionesDeProspectosPorVendedor.nuevo_para(peticion.vendedor())
        try:
            prospecto_entregado = gestor.entregar_prospecto()
        except ValidationError as error:
            detalle = ', '.join(error.messages)
            peticion.marcar_como_no_entregada(detalle)
            self._notificar_entrega_no_realizada(peticion.vendedor(), detalle)
        else:
            peticion.marcar_como_entregada(f'Prospecto entregado, ID: {prospecto_entregado.id}')
            self._notificar_entrega_realizada(peticion.vendedor())

    def agregar_peticion_de_prospecto_para(self, vendedor):
        self._assert_la_cola_no_esta_llena()
        self._assert_no_existe_peticion_de_prospecto_para(vendedor)
        self._assert_la_peticion_tiene_inhabilitacion(vendedor)

        PeticionDeProspectoPorParteDelVendedor.nuevo_para(vendedor)

    def _assert_no_existe_peticion_de_prospecto_para(self, vendedor):
        if self.esta_inhabilitado_por_peticiones_pendientes_para(vendedor):
            raise ValueError(f'Ya existe un pedido para el vendedor {vendedor}')

    def _assert_la_cola_no_esta_llena(self):
        if self.esta_inhabilitado_al_tener_la_cola_llena():
            raise ValueError('La cantidad de pedidos supera el límite máximo')

    def _assert_la_peticion_tiene_inhabilitacion(self, vendedor):
        esta_inhabilitado = self.esta_inhabilitado_al_tener_peticiones_no_entregadas(vendedor)
        if esta_inhabilitado:
            raise ValueError('No es posible hacer otra solicitud en este momento')

    def esta_inhabilitado_al_tener_la_cola_llena(self):
        return self.cantidad_de_peticiones() >= self._tamanio_maximo

    def esta_inhabilitado_por_peticiones_pendientes_para(self, vendedor):
        return self._peticiones_pendientes().para_vendedor(vendedor=vendedor).exists()

    def esta_inhabilitado_al_tener_peticiones_no_entregadas(self, vendedor):
        fecha_limite = self._calculo_fecha_de_inhabilitacion()
        esta_inhabilitado = self._tiene_peticiones_en_intervalo_de_inhabilitacion(fecha_limite, vendedor)
        return esta_inhabilitado

    def _tiene_peticiones_en_intervalo_de_inhabilitacion(self, fecha_limite, vendedor):
        return PeticionDeProspectoPorParteDelVendedor.objects.para_vendedor(
            vendedor=vendedor).no_entregadas().con_fecha_de_resolucion_posteriores_a(fecha_limite=fecha_limite).exists()

    def _calculo_fecha_de_inhabilitacion(self):
        date_helper = DatetimeHelper()
        tiempo_de_inhabilitacion_para_pedir_prospectos = Sistema.instance().tiempo_de_inhabilitacion_para_pedir_prospectos
        fecha_limite = date_helper.now() - timedelta(minutes=tiempo_de_inhabilitacion_para_pedir_prospectos)
        return fecha_limite

    def _notificar_entrega_no_realizada(self, vendedor, mensaje):
        self._notificador_de_resultado.entrega_no_realizada(vendedor, mensaje)

    def _notificar_entrega_realizada(self, vendedor):
        self._notificador_de_resultado.entrega_realizada(vendedor)

    def _peticiones_pendientes(self):
        return PeticionDeProspectoPorParteDelVendedor.objects.pendientes()

    def _siguiente_peticion(self):
        return self._peticiones_pendientes().ordenar_por_fecha_mas_antigua().first()


