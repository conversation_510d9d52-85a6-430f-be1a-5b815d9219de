# coding=utf-8
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.timezone import now, localtime

from core.locker.errors import ResourceLockedError
from core.locker.mem_locker import Locker
from prospectos.models.entrega_de_datos.entregas import ResultadoDeAsignacion, PuestaACargo, \
    AsignacionDeVendedor
from prospectos.models.entrega_de_datos.metodos_de_seleccion import NingunVendedorSatisfaceError, SeleccionDeEquipo, \
    MetodoDeSeleccionUniforme, SeleccionDeUnicoVendedor, SeleccionDeTodos, SeleccionVaciaError
from prospectos.models.entrega_de_datos.restricciones import ListaDeRestriccionesDeVendedores, \
    AccesoRestriccionesDeVendedores


class DistribucionDeProspectos(object):
    def __init__(self, accion, seleccion):
        super(DistribucionDeProspectos, self).__init__()
        self._accion = accion
        self._seleccion = seleccion

    def descripcion(self):
        return self._accion.descripcion()

    def evaluar_para(self, prospectos):
        lockeo = Locker.new_for_group('asignacion_de_prospectos')

        def evaluar():
            resultado = self._seleccion.evaluar_accion(prospectos=prospectos, accion=self._accion)
            return resultado
        try:
            return lockeo.do_locking_each([prospecto.id for prospecto in prospectos], evaluar, [])
        except ResourceLockedError:
            return ResultadoDeAsignacion.con_prospectos_no_entregados(
                prospectos, "Ya se esta ejecutando una asignacion. Intente de nuevo en unos minutos")

    @classmethod
    def asignar_directa_vendedor(cls, vendedor, restricciones=None, descontar_a=None):
        seleccion = SeleccionDirecta.para_vendedor(vendedor, restricciones, pedidos_a_descontar=descontar_a)
        distribucion = cls.nueva(
            accion=AsignarVendedor(),
            seleccion=seleccion)
        return distribucion

    @classmethod
    def asignar_directa_equipo(cls, equipo, restricciones=None):
        seleccion = SeleccionDirecta.para_equipo(equipo, restricciones)
        distribucion = cls.nueva(
            accion=AsignarVendedor(),
            seleccion=seleccion)
        return distribucion

    @classmethod
    def asignar_directa_a_todos(cls, supervisor, restricciones=None):
        seleccion = SeleccionDirecta.para_los_vendedores_de(supervisor, restricciones)
        distribucion = cls.nueva(
            accion=AsignarVendedor(),
            seleccion=seleccion)
        return distribucion

    @classmethod
    def poner_a_cargo(cls, seleccion):
        distribucion = cls.nueva(
            accion=PonerACargo(),
            seleccion=seleccion)
        return distribucion

    @classmethod
    def asignar_vendedor(cls, seleccion):
        distribucion = cls.nueva(
            accion=AsignarVendedor(),
            seleccion=seleccion
        )
        return distribucion

    @classmethod
    def asignar_vendedor_via_supervisores(cls, supervisores, tipo_de_seleccion_de_vendedor, metodo_de_seleccion,
                                          restricciones=None):

        distribucion = cls.nueva(
            accion=AsignarVendedorViaSeleccion(tipo_de_seleccion_de_vendedor=tipo_de_seleccion_de_vendedor,
                                               metodo_de_seleccion=metodo_de_seleccion),
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme(),
                                                     lista_de_restricciones=restricciones)
        )
        return distribucion

    @classmethod
    def nueva(cls, accion, seleccion):
        distribucion = cls(accion=accion, seleccion=seleccion)
        return distribucion


class Asignacion(object):

    def __init__(self):
        super(Asignacion, self).__init__()
        from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
        self._repartidor = RepartidorDeProspectos()

    def evaluar_en_masa(self, prospectos, destinatario, fecha):
        raise NotImplementedError('subclass responsibility')

    def evaluar(self, prospecto, destinatario, fecha, lista_de_restricciones):
        raise NotImplementedError('subclass responsibility')

    def evaluar_en_masa_con_restricciones(self, prospectos, destinatario, restricciones):
        resultado = ResultadoDeAsignacion.nuevo()
        for prospecto in prospectos:
            try:
                ahora = localtime(timezone.now())
                entrega = self.evaluar(prospecto, destinatario, ahora, restricciones)
            except NingunVendedorSatisfaceError:
                resultado.registrar_prospecto_no_entregado(prospecto)
            else:
                resultado.registrar(entrega)
        return resultado

    def restricciones_para_supervisores(self, lista_de_restricciones):
        raise NotImplementedError('subclass responsibility')

    def es_poner_a_cargo(self):
        return False

    def es_asignacion(self):
        return False

    def restricciones_segun_accion(self, lista_de_restricciones):
        raise NotImplementedError('subclass responsibility')

    def descripcion(self):
        raise NotImplementedError('subclass responsibility')

    def asignado_en(self, prospecto):
        raise NotImplementedError('subclass responsibility')

    def _registrar_consumo_en_pedido(self, pedido, prospecto, debe_registrar_consumo, resultado):
        if debe_registrar_consumo:
            from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
            administrador = AdministradorDePedidos()
            consumo = prospecto.campania.categoria.valor
            administrador.asignar_prospecto(a_prospecto=prospecto, pedido=pedido,
                                            cantidad_consumida=consumo)
            resultado.registrar_consumo(pedido, consumo)


class PonerACargo(Asignacion):
    def evaluar_en_masa(self, prospectos, destinatario, fecha):
        self._repartidor.asignar_prospectos_a_responsable(destinatario, prospectos, con_fecha=fecha)

    def evaluar(self, prospecto, destinatario, fecha, lista_de_restricciones):
        restricciones = lista_de_restricciones.convertir_para_supervisor()
        if restricciones.esta_restringido(destinatario):
            raise NingunVendedorSatisfaceError()
        else:
            self._repartidor.asignar_responsable_a(prospecto, destinatario, con_fecha=fecha)
            entrega = PuestaACargo.para(destinatario, prospecto)
            return entrega.convertir_a_resultado()

    def evaluar_via_pedido(self, prospecto, pedido, lista_de_restricciones, fecha, seleccion_via_pedidos):
        supervisor = pedido.supervisor
        resultado = self.evaluar(prospecto, supervisor, fecha, lista_de_restricciones)
        debe_registrar_consumo = seleccion_via_pedidos.debe_registrar_consumo()
        self._registrar_consumo_en_pedido(pedido, prospecto, debe_registrar_consumo, resultado)
        return resultado

    def evaluar_via_supervisores(self, prospecto, supervisor, lista_de_restricciones, fecha):
        entrega = self.evaluar(prospecto, supervisor, fecha, lista_de_restricciones)
        return entrega

    def restricciones_para_supervisores(self, lista_de_restricciones):
        return lista_de_restricciones.convertir_para_supervisor()

    def es_poner_a_cargo(self):
        return True

    def restricciones_segun_accion(self, lista_de_restricciones):
        restricciones = lista_de_restricciones.convertir_para_supervisor()
        return restricciones

    def descripcion(self):
        return u'Asignación de responsable'


class AsignarVendedor(Asignacion):
    def evaluar_en_masa(self, prospectos, destinatario, fecha):
        self._repartidor.asignar_prospectos_a(vendedor=destinatario, prospectos=prospectos, con_fecha=fecha)
        entrega = AsignacionDeVendedor.en_masa_para(destinatario, prospectos)
        return entrega

    def evaluar_sin_restricciones(self, prospecto, destinatario, fecha):
        self._repartidor.asignar_prospecto_a(vendedor=destinatario, prospecto=prospecto, con_fecha=fecha)
        entrega = AsignacionDeVendedor.para(destinatario, prospecto)
        return entrega.convertir_a_resultado()

    def evaluar(self, prospecto, destinatario, fecha, lista_de_restricciones):
        if lista_de_restricciones.esta_restringido(destinatario):
            raise NingunVendedorSatisfaceError()
        entrega = self.evaluar_sin_restricciones(prospecto, destinatario, fecha)
        return entrega

    def evaluar_via_pedido(self, prospecto, pedido, lista_de_restricciones, fecha, seleccion_via_pedidos):
        try:
            forzar_por_productividad = seleccion_via_pedidos.forzar_por_productividad()
            vendedor = pedido.seleccionar_vendedor_con_restricciones(lista_de_restricciones, forzar_por_productividad)
        except SeleccionVaciaError:
            raise NingunVendedorSatisfaceError()
        else:
            entrega = self.evaluar_sin_restricciones(prospecto, vendedor, fecha)
            debe_registrar_consumo = seleccion_via_pedidos.debe_registrar_consumo()
            self._registrar_consumo_en_pedido(pedido, prospecto, debe_registrar_consumo, entrega)
            return entrega

    def restricciones_para_supervisores(self, lista_de_restricciones):
        return ListaDeRestriccionesDeVendedores.vacia()

    def es_asignacion(self):
        return True

    def restricciones_segun_accion(self, lista_de_restricciones):
        return lista_de_restricciones

    def descripcion(self):
        return u'Asignación de vendedor'


class AsignarVendedorViaSeleccion(Asignacion):
    def evaluar_en_masa(self, prospectos, destinatario, fecha):
        return self._accion.evaluar_en_masa(prospectos, destinatario, fecha)

    def __init__(self, tipo_de_seleccion_de_vendedor, metodo_de_seleccion):
        super(AsignarVendedorViaSeleccion, self).__init__()
        self._tipo_de_seleccion_de_vendedor = tipo_de_seleccion_de_vendedor
        self._metodo_de_seleccion = metodo_de_seleccion
        self._accion = AsignarVendedor()

    def restricciones_para_supervisores(self, lista_de_restricciones):
        return self._accion.restricciones_para_supervisores(lista_de_restricciones)

    def es_poner_a_cargo(self):
        return self._accion.es_poner_a_cargo()

    def es_asignacion(self):
        return self._accion.es_asignacion()

    def evaluar(self, prospecto, destinatario, fecha, lista_de_restricciones):
        vendedores = self._tipo_de_seleccion_de_vendedor.seleccionar_vendedores(destinatario, lista_de_restricciones)
        if not vendedores.exists():
            raise NingunVendedorSatisfaceError()
        vendedor = self._metodo_de_seleccion.seleccionar_para(vendedores)
        if vendedor is None:
            raise NingunVendedorSatisfaceError()
        else:
            entrega = self._accion.evaluar(prospecto, vendedor, fecha, lista_de_restricciones)
            return entrega

    def restricciones_segun_accion(self, lista_de_restricciones):
        return self._accion.restricciones_segun_accion(lista_de_restricciones)

    def descripcion(self):
        return u'Asignación de vendedor via selección'


class SeleccionParaAsignacion(object):
    def evaluar_accion(self, prospectos, accion):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def es_via_supervisores(cls):
        return False

    @classmethod
    def es_directa(cls):
        return False

    @classmethod
    def es_via_pedidos(cls):
        return False


class SeleccionDirecta(SeleccionParaAsignacion):
    def __init__(self, seleccion_de_vendedor, lista_de_restricciones=None, pedidos_a_descontar=None):
        super(SeleccionDirecta, self).__init__()
        self._seleccion_de_vendedor = seleccion_de_vendedor
        self._pedidos_a_descontar = pedidos_a_descontar or []
        self._lista_de_restricciones = lista_de_restricciones or ListaDeRestriccionesDeVendedores.vacia()

    def evaluar_accion(self, prospectos, accion):
        self._validar_accion(accion)
        resultado = self._seleccion_de_vendedor.evaluar_asignacion(self,
                                                                   prospectos,
                                                                   accion)
        return resultado

    def evaluar_modo_seleccion(self, prospectos, accion):
        resultado = ResultadoDeAsignacion.nuevo()
        for prospecto in prospectos:
            try:
                ahora = timezone.now()
                entrega = self._evaluar_para_prospecto(prospecto, accion, self._lista_de_restricciones, ahora)
            except NingunVendedorSatisfaceError:
                resultado.registrar_prospecto_no_entregado(prospecto)
            else:
                resultado.registrar(entrega)

        return resultado

    def evaluar_modo_directo(self, prospectos, accion):
        resultado = ResultadoDeAsignacion.nuevo()
        try:
            vendedor = self._seleccion_de_vendedor.seleccionar_para(self._lista_de_restricciones)
        except NingunVendedorSatisfaceError:
            resultado.registrar_prospectos_no_entregados(prospectos)
        else:
            entrega = accion.evaluar_en_masa_con_restricciones(prospectos, vendedor, self._lista_de_restricciones)
            self._descontar_a_pedidos_para(prospectos, resultado)
            resultado.registrar(entrega)
        return resultado

    def _descontar_a_pedidos_para(self, prospectos, resultado):
        if not self._pedidos_a_descontar:
            return
        # TODO: descontar a pedidos, ver si se puede utilizar el algoritmode distribucion uniforme
        from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
        from prospectos.models.base import Prospecto
        administrador = AdministradorDePedidos()
        cantidad_de_prospectos = prospectos.count()
        cantidad_de_pedidos = len(self._pedidos_a_descontar)
        cantidad_a_descontar = cantidad_de_prospectos // cantidad_de_pedidos
        resto = cantidad_de_prospectos % cantidad_de_pedidos
        for indice_pedido in range(0, cantidad_de_pedidos):
            pedido = self._pedidos_a_descontar[indice_pedido]
            indice_inicial = indice_pedido * cantidad_a_descontar
            indice_final = indice_inicial + cantidad_a_descontar
            if resto > 0:
                indice_final += 1
                resto -= 1
            prospectos_por_pedido = prospectos[indice_inicial:indice_final]
            prospectos_queryset = Prospecto.objects.filter(pk__in=[p.pk for p in prospectos_por_pedido])
            consumido = prospectos_queryset.valor_total()
            administrador.registrar_asignacion_de_prospectos(pedido, prospectos, consumido)
            prospectos_queryset.update(pedido=pedido)
            resultado.registrar_consumo(pedido, consumido)

    def _evaluar_para_prospecto(self, prospecto, accion, lista_de_restricciones, fecha):
        vendedor = self._seleccion_de_vendedor.seleccionar_para(lista_de_restricciones)
        entrega = accion.evaluar_sin_restricciones(prospecto, vendedor, fecha)
        return entrega

    def _validar_accion(self, accion):
        if not accion.es_asignacion():
            raise ValidationError('Asignacion directa solo acepta accion de asignacion')

    @classmethod
    def es_directa(cls):
        return True

    @classmethod
    def para_equipo(cls, equipo, lista_de_restricciones, modo_de_priorizacion_de_vendedores=None):
        if not modo_de_priorizacion_de_vendedores:
            modo_de_priorizacion_de_vendedores = MetodoDeSeleccionUniforme()
        seleccion = SeleccionDeEquipo.nuevo(equipo, modo_de_priorizacion_de_vendedores)
        return cls.nuevo(seleccion, lista_de_restricciones)

    @classmethod
    def para_los_vendedores_de(cls, supervisor, lista_de_restricciones, modo_de_priorizacion_de_vendedores=None):
        if not modo_de_priorizacion_de_vendedores:
            modo_de_priorizacion_de_vendedores = MetodoDeSeleccionUniforme()
        seleccion = SeleccionDeTodos.nuevo(supervisor, modo_de_priorizacion_de_vendedores)
        return cls.nuevo(seleccion, lista_de_restricciones)

    @classmethod
    def para_vendedor(cls, vendedor, lista_de_restricciones, pedidos_a_descontar=None):
        seleccion = SeleccionDeUnicoVendedor.nuevo(vendedor)
        return cls.nuevo(seleccion, lista_de_restricciones, pedidos_a_descontar)

    @classmethod
    def nuevo(cls, seleccion, lista_de_restricciones, pedidos_a_descontar=None):
        return cls(seleccion, lista_de_restricciones, pedidos_a_descontar)


class SeleccionViaSupervisores(SeleccionParaAsignacion):
    def __init__(self, supervisores, metodo,  debe_registrar_consumo, lista_de_restricciones=None):
        super(SeleccionViaSupervisores, self).__init__()
        self._supervisores = supervisores
        self._metodo = metodo
        self._debe_registrar_consumo = debe_registrar_consumo
        self._lista_de_restricciones = lista_de_restricciones or ListaDeRestriccionesDeVendedores.vacia()

    @classmethod
    def entre(cls, supervisores, metodo,  debe_registrar_consumo=False, lista_de_restricciones=None):
        seleccion = cls(supervisores, metodo, debe_registrar_consumo, lista_de_restricciones)
        return seleccion

    def evaluar_accion(self, prospectos, accion,):
        resultado = self._metodo.realizar_para(prospectos=prospectos,
                                               destinatarios=self._supervisores,
                                               lista_de_restricciones=self._lista_de_restricciones,
                                               accion=accion,
                                               debe_registrar_consumo=self._debe_registrar_consumo)
        return resultado

    @classmethod
    def es_via_supervisores(cls):
        return True


class SeleccionViaPedidos(SeleccionParaAsignacion):
    def __init__(self, pedidos, debe_registrar_consumo, acceso_a_restricciones=None, forzar_por_productividad=False):
        super(SeleccionViaPedidos, self).__init__()
        self._pedidos = pedidos
        self._debe_registrar_consumo = debe_registrar_consumo
        from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
        self._administrador_de_pedidos = AdministradorDePedidos()
        self._acceso_a_restricciones = acceso_a_restricciones or AccesoRestriccionesDeVendedores.preconfigurado_vacio()
        self._forzar_por_productividad = forzar_por_productividad

    def debe_registrar_consumo(self):
        return self._debe_registrar_consumo

    def forzar_por_productividad(self):
        return self._forzar_por_productividad

    def evaluar_accion(self, prospectos, accion):
        resultado = ResultadoDeAsignacion.nuevo()
        for prospecto in prospectos:
            ahora = now()
            entrega = self._evaluar_accion_para(accion, ahora, self._acceso_a_restricciones, prospecto)
            resultado.registrar(entrega)
        return resultado

    def _evaluar_accion_para(self, accion, fecha, acceso_a_restricciones, prospecto):
        resultado = ResultadoDeAsignacion.nuevo()
        pedidos = self._pedidos_priorizados_para(prospecto, accion)
        if not pedidos:
            resultado.registrar_prospecto_no_entregado(prospecto)
        for pedido in pedidos:
            try:
                lista_de_restricciones = acceso_a_restricciones.obtener_desde(pedido)
                entrega = accion.evaluar_via_pedido(prospecto, pedido, lista_de_restricciones,
                                                    fecha, self)
            except NingunVendedorSatisfaceError:
                pass
            else:
                resultado.registrar(entrega)
                return resultado
        resultado.registrar_prospecto_no_entregado(prospecto)
        return resultado

    def _pedidos_priorizados_para(self, prospecto, accion):
        from prospectos.models import PedidoDeProspecto
        pedidos = PedidoDeProspecto.objects.filtrar_pedidos_para(self._pedidos, accion)
        return self._administrador_de_pedidos.pedidos_priorizados_para(pedidos, prospecto)

    @classmethod
    def es_via_pedidos(cls):
        return True

    @classmethod
    def para(cls, pedidos, debe_registrar_consumo=False, acceso_a_restricciones=None, forzar_por_productividad=False):
        return cls(pedidos=pedidos, debe_registrar_consumo=debe_registrar_consumo,
                   acceso_a_restricciones=acceso_a_restricciones, forzar_por_productividad=forzar_por_productividad)

    @classmethod
    def de_supervisores(cls, supervisores, debe_registrar_consumo=False, acceso_a_restricciones=None,
                        forzar_por_productividad=False):
        from prospectos.models.base import PedidoDeProspecto
        pedidos = PedidoDeProspecto.objects.de_supervisores(supervisores)
        return cls.para(pedidos, debe_registrar_consumo, acceso_a_restricciones, forzar_por_productividad)

    @classmethod
    def para_todos_los_pedidos(cls, debe_registrar_consumo=False, acceso_a_restricciones=None,
                               forzar_por_productividad=False):
        from prospectos.models import PedidoDeProspecto
        pedidos = PedidoDeProspecto.objects.activos_actuales()
        return cls.para(pedidos, debe_registrar_consumo, acceso_a_restricciones, forzar_por_productividad)


class DistribucionPorFactor(object):

    def __init__(self, metodo_de_seleccion_de_supervisores):
        super(DistribucionPorFactor, self).__init__()
        self._metodo_de_seleccion_de_supervisores = metodo_de_seleccion_de_supervisores

    @classmethod
    def para(cls, metodo_de_seleccion_de_supervisores):
        return cls(metodo_de_seleccion_de_supervisores)

    @classmethod
    def uniforme(cls):
        return cls(MetodoDeSeleccionUniforme())

    def realizar_para(self, prospectos, destinatarios, lista_de_restricciones, accion, debe_registrar_consumo):
        restricciones_para_supervisores = accion.restricciones_para_supervisores(lista_de_restricciones)
        destinatarios_habilitados = restricciones_para_supervisores.filtrar(destinatarios)
        if not destinatarios_habilitados.exists():
            return ResultadoDeAsignacion.con_prospectos_no_entregados(prospectos)
        cantidad_de_prospectos = prospectos.count()
        ids = prospectos.values_list('id', flat=True)
        recorridos, resultado = self._asignar_prospectos(accion,
                                                         cantidad_de_prospectos,
                                                         destinatarios_habilitados,
                                                         ids, restricciones_para_supervisores,
                                                         lista_de_restricciones,
                                                         debe_registrar_consumo)
        # El resto es menor a la cantidad de vendedores a asignar.
        if cantidad_de_prospectos > recorridos or resultado.tiene_prospectos_no_entregados():
            ids_de_no_entregados = resultado.ids_de_no_entregados()
            ids_restantes = list(ids[recorridos:cantidad_de_prospectos]) + ids_de_no_entregados
            self._asignar_prospectos_restantes(accion, destinatarios_habilitados, ids_restantes,
                                               lista_de_restricciones, resultado, debe_registrar_consumo)

        return resultado

    def _asignar_prospectos(self, accion, cantidad_de_prospectos, destinatarios, ids,
                            restricciones_para_supervisores, restricciones, debe_registrar_consumo):
        from prospectos.models import Prospecto
        resultado = ResultadoDeAsignacion.nuevo()
        entregas_futuras = self._calcular_entregas(destinatarios,
                                                   cantidad_de_prospectos,
                                                   restricciones_para_supervisores)
        recorridos = 0
        for entrega_futura in list(entregas_futuras.values()):
            asignar = entrega_futura.cantidad()
            if asignar > 0:
                ids_a_asignar = ids[recorridos:recorridos + asignar]
                prospectos_seleccionados = Prospecto.objects.filter(pk__in=list(ids_a_asignar))
                supervisor = entrega_futura.supervisor()
                entrega = accion.evaluar_en_masa_con_restricciones(prospectos_seleccionados,
                                                                   supervisor,
                                                                   restricciones)
                if debe_registrar_consumo:
                    self._descontar_consumo_para(accion, supervisor, entrega, resultado)
                resultado.registrar(entrega)
                recorridos += asignar

        return recorridos, resultado

    def _asignar_prospectos_restantes(self, accion, destinatarios, ids_restantes, restricciones, resultado,
                                      debe_registrar_consumo):
        from prospectos.models import Prospecto
        prospectos_restantes = Prospecto.objects.filter(pk__in=list(ids_restantes))
        destinatarios_ordenados = list(destinatarios)
        destinatarios_ordenados.sort(key=lambda each: resultado.cantidad_entregrada_a(each))
        fila = list(range(0, len(destinatarios_ordenados)))

        for prospecto in prospectos_restantes:
            fila = self._buscar_destinatario_y_evaluar_accion(destinatarios_ordenados, fila, accion, prospecto,
                                                             restricciones, resultado, debe_registrar_consumo)

    def _buscar_destinatario_y_evaluar_accion(self, destinatarios, fila, accion, prospecto, restricciones,
                                             resultado, debe_registrar_consumo):
        for posicion in fila:
            destinatario = destinatarios[posicion]
            fecha = localtime(timezone.now())
            try:
                entrega = accion.evaluar(prospecto, destinatario, fecha, restricciones)
            except NingunVendedorSatisfaceError:
                resultado.registrar_prospecto_no_entregado(prospecto)
            else:
                resultado.desregistrar_prospecto_no_entregado(prospecto)
                resultado.registrar(entrega)
                if debe_registrar_consumo:
                    self._descontar_consumo_para(accion, destinatario, entrega, resultado)
                return self._enviar_al_final_de_la_fila(posicion, fila)
        return fila

    def _enviar_al_final_de_la_fila(self, indice, fila):
        elemento = fila.pop(indice)
        fila.append(elemento)
        return fila

    def _calcular_entregas(self, destinatarios, cantidad, restricciones):
        calculo = CalculoDeEntrega.para(destinatarios, self._metodo_de_seleccion_de_supervisores, restricciones)
        entregas = calculo.evaluar_para(cantidad=cantidad)
        return entregas

    def _descontar_consumo_para(self, accion, supervisor, entrega, resultado):
        from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
        administrador = AdministradorDePedidos()
        pedido = administrador.pedido_para_descontar(supervisor, accion)
        if pedido is not None:
            self._asignar_pedido(administrador, pedido, entrega, resultado)

    def _asignar_pedido(self, administrador, pedido, entrega, resultado):
        from prospectos.models.base import Prospecto
        prospectos = entrega.prospectos()
        prospectos_queryset = Prospecto.objects.filter(pk__in=[p.pk for p in prospectos])
        consumido = prospectos_queryset.valor_total()
        administrador.registrar_asignacion_de_prospectos(pedido, prospectos_queryset, consumido)
        prospectos_queryset.update(pedido=pedido)
        resultado.registrar_consumo(pedido, consumido)


class CalculoDeEntrega(object):
    # vendedor, cantidad, cantidada_inicial
    def __init__(self, supervisores, seleccion_de_supervisor, restricciones):
        self._supervisores = supervisores
        self._seleccion_de_supervisor = seleccion_de_supervisor
        self._restricciones = restricciones.convertir_para_supervisor()

    def evaluar_para(self, cantidad):
        from vendedores.models import Vendedor
        supervisores = Vendedor.objects.anotar_ultima_asignacion_de_supervisores(self._supervisores)
        entregas, resto = self._entrega_inicial(supervisores, cantidad)
        for entrega in range(0, resto):
            try:
                entrega = self._buscar_siguiente(entregas)
                entrega.entregar_datos(cantidad=1)
            except NingunVendedorSatisfaceError:
                return entregas
        return entregas

    def _buscar_siguiente(self, entregas):
        """
            Selecciona el vendedor con menor factor y menos tiempo de acceso
        """
        entrega_ordenadas = sorted(list(entregas.values()), key=self._clave_sort_distribucion)
        for entrega in entrega_ordenadas:
            if entrega.acepta_datos():
                return entrega
        raise NingunVendedorSatisfaceError()

    def _clave_sort_distribucion(self, condicion_de_entrega):
        from dateutil.relativedelta import relativedelta
        ratio = condicion_de_entrega.ratio()
        ultima_asignacion = condicion_de_entrega.ultima_asignacion()
        if not ultima_asignacion:
            ultima_asignacion = now() - relativedelta(years=2)

        return ratio, ultima_asignacion

    def _entrega_inicial(self, supervisores, cantidad):
        factores_de_distribucion = self._seleccion_de_supervisor.factor_de_distribucion_de_vendedores(supervisores)
        total_de_fatores = sum(factores_de_distribucion.values())
        supervisores_optimizados = self._restricciones.optimizar_consulta(supervisores)
        porcion_entera = cantidad // total_de_fatores
        resto = int(cantidad % total_de_fatores)
        distribuciones = {}
        no_entregados = 0
        for supervisor in supervisores_optimizados:
            factor = factores_de_distribucion.get(supervisor.id)
            condicion_de_entrega = self._condicion_de_entrega_para(supervisor, factor)
            no_entregados += condicion_de_entrega.entregar_datos(cantidad=porcion_entera * factor)
            distribuciones[supervisor] = condicion_de_entrega
        return distribuciones, resto + no_entregados

    def _condicion_de_entrega_para(self,  supervisor, factor):
        maxima_cantidad_aceptada = self._restricciones.maxima_cantidad_a_entregar_para(supervisor)
        condicion_de_entrega = CondicionDeEntrega(supervisor, factor, maxima_cantidad_aceptada)
        return condicion_de_entrega

    @classmethod
    def para(cls, supervisores, seleccion_de_vendedor, restricciones=None):
        if restricciones is None:
            restricciones = ListaDeRestriccionesDeVendedores.vacia()
        return cls(supervisores, seleccion_de_vendedor, restricciones)


class CondicionDeEntrega(object):
    def __init__(self, supervisor, factor, maxima_cantidad_aceptada):
        super(CondicionDeEntrega, self).__init__()
        self._supervisor = supervisor
        self._factor = factor
        self._maxima_cantidad_aceptada = maxima_cantidad_aceptada
        self._cantidad_entregada = 0

    def supervisor(self):
        return self._supervisor

    def ultima_asignacion(self):
        """
          Muy FEO: Asumme que el supervisor fue anotado con ese atributo desde anotar_ultima_asignacion_de_supervisores"
        """
        return self._supervisor.ultima_asignacion

    def ratio(self):
        if self._factor > 0:
            ratio = float(self.cantidad() + 1.0) / self._factor
        else:
            ratio = 0
        return ratio

    def cantidad(self):
        return self._cantidad_entregada

    def entregar_datos(self, cantidad):
        cantidad_datos_aceptados = min(self._maxima_cantidad_aceptada - self._cantidad_entregada, cantidad)
        self._cantidad_entregada += cantidad_datos_aceptados
        no_entregados = cantidad - cantidad_datos_aceptados
        return int(no_entregados)

    def restante(self):
        return self._maxima_cantidad_aceptada - self._cantidad_entregada

    def acepta_datos(self):
        return self.restante() > 0
