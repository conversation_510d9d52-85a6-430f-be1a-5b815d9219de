# coding=utf-8
from django.db import models

from prospectos.models.entrega_de_datos.opciones import EntregaChoices


class RegistroDeResultadoDeAsignacionInicial(models.Model):
    _fecha = models.DateTimeField(auto_now_add=True)
    _usuario = models.ForeignKey('users.User', related_name='resultados_de_asignaciones_iniciales')
    _cantidad_de_datos_entregados = models.IntegerField()
    _cantidad_de_datos_no_entregados = models.IntegerField()
    _descripcion = models.CharField(max_length=128)

    def asignaciones(self):
        return self._asignaciones

    def descripcion(self):
        return self._descripcion

    def ids_de_prospectos_asignados(self):
        return self._asignaciones.values_list('_prospecto_id', flat=True)

    @classmethod
    def nuevo_para(cls, usuario, descripcion, resultado_de_asignacion):
        registro = cls(
            _usuario=usuario,
            _descripcion=descripcion,
            _cantidad_de_datos_entregados=resultado_de_asignacion.cantidad_entregada(),
            _cantidad_de_datos_no_entregados=resultado_de_asignacion.cantidad_no_entregada(),
        )
        registro.save()
        ProspectoAsignadoDesdeAsignacionInicial.nuevos_para(resultado_de_asignacion, registro)
        return registro

    class Meta:
        verbose_name_plural = 'Registro de resultados de asignación iniciales'


class ProspectoAsignadoDesdeAsignacionInicial(models.Model):
    _registro = models.ForeignKey(RegistroDeResultadoDeAsignacionInicial, related_name='_asignaciones',
                                  on_delete=models.CASCADE)
    _prospecto = models.ForeignKey('prospectos.Prospecto', on_delete=models.CASCADE)
    _asignado = models.ForeignKey('vendedores.Vendedor', related_name='_registros_de_asignaciones',
                                  on_delete=models.CASCADE)
    _tipo_de_asignacion = models.CharField(max_length=30, choices=EntregaChoices.choices())

    @classmethod
    def nuevos_para(cls, resultado_de_asignacion, registro_de_asignacion_inicial):
        registros_de_asignaciones = []
        for entrega_de_prospecto in resultado_de_asignacion.entregas():
            registros = cls._nuevos_para_entrega_de_prospecto(entrega_de_prospecto, registro_de_asignacion_inicial)
            registros_de_asignaciones.extend(registros)

        return cls.objects.bulk_create(registros_de_asignaciones)

    @classmethod
    def _nuevos_para_entrega_de_prospecto(cls, entrega_de_prospecto, registro_de_asignacion_inicial):
        asignaciones = []
        for prospecto in entrega_de_prospecto.prospectos():
            registro = cls(
                _registro=registro_de_asignacion_inicial, _prospecto=prospecto,
                _asignado=entrega_de_prospecto.asignado(),
                _tipo_de_asignacion=EntregaChoices.key_for(entrega_de_prospecto.__class__)
            )
            asignaciones.append(registro)
        return asignaciones

    def prospecto(self):
        return self._prospecto

    def prospecto_id(self):
        return self._prospecto_id

    def __unicode__(self):
        return u'%s sobre el prospecto %s' % (self._registro.descripcion(), self._prospecto_id)
