import abc
import json
from datetime import time

from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from django.utils.timezone import datetime, get_current_timezone, make_aware

from campanias.models import Campania
from campanias.models import TipoDeOrigen
from occ.models import CampaniaDeComunicacion
from prospectos.models import Prospecto, Tag, Modelo
from prospectos.views.forms import FiltrosResumenForm


class FiltrosParaProspectos(object):
    opciones_de_filtros_de_llamado = ['vencidos', 'futuros', 'dia', 'proximos']

    def __init__(self, filtrados, usuario):
        self.filtrados = filtrados
        self.usuario = usuario

    def prospectos_filtrados(self):
        return self.filtrados

    def get_filtro_tipo_origen(self, filtros):
        tipo = filtros.get('tipo_origen')
        tipo_de_origen = TipoDeOrigen.objects.filter(codigo__iexact=tipo)
        return tipo_de_origen[0] if tipo_de_origen.count() > 0 else None

    def obtener_filtros_para_resumen(self, request, puede_administrar_equipos):
        result = {'equipo': None, 'vendedor': None}
        datos = request.GET
        form = FiltrosResumenForm(datos)
        form.is_valid()
        cd = form.cleaned_data
        # Fechas
        desde = cd.get('fecha_desde')
        if desde:
            desde = make_aware(datetime(desde.year, desde.month, desde.day), get_current_timezone())
        result['desde'] = desde
        hasta = cd.get('fecha_hasta')
        if hasta:
            hasta = make_aware(datetime(hasta.year, hasta.month, hasta.day), get_current_timezone())
        result['hasta'] = hasta
        result['tipo_de_fecha'] = request.GET.get('tipo_de_fecha')
        result['buscar'] = cd.get('buscar')
        distribuidor = datos.get('distribuidor')
        marca = datos.get('marca')
        if marca:
            result['marcas'] = [marca]
        result['distribuidor'] = distribuidor

        filtrador = FiltrosParaProspectos(filtrados=None, usuario=request.user)
        result['tipo'] = filtrador.get_filtro_tipo_origen(datos)
        if request.user.is_vendedor() and puede_administrar_equipos:
            result['equipo'] = datos.get('equipo') if datos.get('equipo') and datos.get(
                'equipo').isdigit() else None
            result['vendedor'] = datos.get('vendedor') if datos.get('vendedor') and datos.get(
                'vendedor').isdigit() else None
        elif request.user.is_gerente():
            result['equipo'] = datos.get('equipo') if datos.get('equipo') and datos.get(
                'equipo').isdigit() else None
            result['vendedor'] = datos.get('vendedor') if datos.get('vendedor') and datos.get(
                'vendedor').isdigit() else None
        result['campania'] = datos.get('campania', None)
        self.obtener_filtros_por_llamado(datos, result)

        ordenamiento_default = '1'
        if result.get('filtro_llamado') == 'dia':
            ordenamiento_default = '0'
        result['ordenamiento'] = datos.get('ordenamiento', ordenamiento_default)
        cantidad = datos.get('cantidad', str(settings.PAGE_SIZE))
        try:
            result['cantidad'] = int(cantidad)
        except ValueError:
            result['cantidad'] = settings.PAGE_SIZE

        result['filtro_estado'] = datos.get('filter_estado', 'activo')
        result['motivo_de_finalizacion'] = datos.get('motivo_de_finalizacion')
        result['inactividad'] = datos.get('inactividad', None)
        result['tags'] = datos.getlist('tags')
        result['sin_agendar'] = self._debe_filtrar_por_prospectos_sin_agendar(request)
        return result

    def _debe_filtrar_por_prospectos_sin_agendar(self, request):
        sin_agendar = request.GET.get('sin_agendar', '')
        debe_filtrar_por_prospectos_sin_agendar = sin_agendar.isdigit() and int(sin_agendar) != 0
        return debe_filtrar_por_prospectos_sin_agendar

    def obtener_filtros_por_llamado(self, request, result):
        filtro_llamado = request.get('filter_llamado')
        if filtro_llamado in self.opciones_de_filtros_de_llamado:
            result['filtro_llamado'] = filtro_llamado
            if filtro_llamado == 'dia':
                result['filtro_llamado_proximo'] = False
                dia = request.get('filtro_dia')
                if dia:
                    result['filtro_llamado_dia'] = dia
                else:
                    result['filtro_llamado_dia'] = timezone.now().strftime("%Y-%m-%d")
                    result['filtro_llamado_proximo'] = True

    def filtrar_por_sin_agendar(self, filtros):
        if filtros.get('sin_agendar', False):
            self.filtrados = self.filtrados.sin_agendar()

    def filtrar_por_estado(self, filtros, vendedor):
        filtro = filtros.get('filtro_estado')
        if filtro in ['N', 'P', 'V', 'F']:
            self.filtrados = self.filtrados.filter(estado=filtro)
        if filtro == 'activo':
            self.filtrados = self.filtrados.filter(estado__in=['N', 'P'])
        if filtro == 'no_vendido':
            self.filtrados = self.filtrados.filter(estado__in=['N', 'P', 'F'])

        if filtro == 'antiguo':
            fecha_limite = vendedor.fecha_limite_para_atender_prospectos_en_tiempo()
            self.filtrados = Prospecto.objects.en_rojo(self.filtrados, fecha_limite)

        if filtro == 'vencido':
            self.filtrados = self.filtrados.con_llamado_vencido()

        if filtro == 'N':   #Prospectos en estado Nuevo
            self._filtrar_por_inactividad(filtros)
            self._filtrar_por_dias_en_reposo(filtros)

        if filtro == 'F': #Prospectos en estado Finalizado
            id_motivo_de_finalizacion = filtros.get('motivo_de_finalizacion')
            if id_motivo_de_finalizacion:
                if id_motivo_de_finalizacion == '0':
                    self.filtrados = self.filtrados.finalizados_con_otro_motivo()
                else:
                    self.filtrados = self.filtrados.finalizados_por(id_motivo_de_finalizacion)

    def _filtrar_por_inactividad(self, filtros):
        inactividad = filtros.get('inactividad', None)
        if inactividad:
            hoy = timezone.now()
            hoy = make_aware(datetime(hoy.year, hoy.month, hoy.day), get_current_timezone())
            if inactividad == 'mas':
                self.filtrados = self.filtrados.filter(
                    asignacion__fecha_de_asignacion_a_vendedor__lt=hoy + timezone.timedelta(days=-9))
            elif inactividad == '3':
                self.filtrados = self.filtrados.filter(
                    asignacion__fecha_de_asignacion_a_vendedor__lt=hoy + timezone.timedelta(days=-2))
            elif inactividad == '5':
                self.filtrados = self.filtrados.filter(
                    asignacion__fecha_de_asignacion_a_vendedor__lt=hoy + timezone.timedelta(days=-4))

    def _filtrar_por_dias_en_reposo(self, filtros):
        filtro_dias_en_reposo = filtros.get('dias-en-reposo', None)
        if filtro_dias_en_reposo:
            hoy = timezone.localtime(timezone.now())
            hoy = make_aware(datetime(hoy.year, hoy.month, hoy.day), get_current_timezone())
            if filtro_dias_en_reposo == 'mas-de-10':
                self.filtrados = self.filtrados.filter(fecha_creacion__lt=hoy + timezone.timedelta(days=-10))
            elif filtro_dias_en_reposo == 'entre-5-y-10':
                self.filtrados = self.filtrados.filter(
                    fecha_creacion__gte=hoy + timezone.timedelta(days=-10),
                    fecha_creacion__lte=hoy + timezone.timedelta(days=-5))
            elif filtro_dias_en_reposo == 'menor-a-5':
                self.filtrados = self.filtrados.filter(fecha_creacion__gt=hoy + timezone.timedelta(days=-5))

    def filtrando_por_llamado(self, filtros):
        filtro_llamado = filtros.get('filtro_llamado')
        return filtro_llamado in self.opciones_de_filtros_de_llamado

    def filtrar_por_llamado(self, filtros):
        if not self.filtrando_por_llamado(filtros):
            return

        filtro_llamado = filtros.get('filtro_llamado')
        filtro_llamado_dia = filtros.get('filtro_llamado_dia')
        ahora = timezone.now()
        hoy = make_aware(datetime(ahora.year, ahora.month, ahora.day), timezone.utc)
        self.filtrados = self.filtrados.filter(llamado__isnull=False)
        if filtro_llamado == 'vencidos':
            # Listar los llamados pendientes hasta el dia de hoy a ultima hora.
            maniana = hoy + timezone.timedelta(days=1)
            maniana = make_aware(datetime(maniana.year, maniana.month, maniana.day), timezone.utc)
            self.filtrados = self.filtrados.filter(llamado__fecha__lt=maniana)
        elif filtro_llamado == 'futuros':
            # Listar los llamados de hoy en adelante
            self.filtrados = self.filtrados.filter(llamado__fecha__gte=hoy)
        elif filtro_llamado == 'dia':
            try:
                dia_date = datetime.strptime(filtro_llamado_dia, '%Y-%m-%d')
                dia_min = make_aware(datetime.combine(dia_date, time.min), timezone.utc)
                dia_max = make_aware(datetime.combine(dia_date, time.max), timezone.utc)
            except:
                pass
            else:
                self.filtrados = self.filtrados.filter(llamado__fecha__gte=dia_min, llamado__fecha__lte=dia_max)

    def filtrar_por_fecha_y_origen(self, desde, hasta, tipo_de_origen, tipo_de_fecha=None):
        if hasta:
            hasta = hasta + timezone.timedelta(days=1)
        tipo_de_fecha = tipo_de_fecha or FiltroPorFechasDelDato.clave()
        filtro = FiltroPorFechas.nuevo_para(tipo_de_fecha, fecha_desde=desde, fecha_hasta=hasta)
        self.filtrados = filtro.aplicar(self.filtrados)
        filtro = FiltroPorTipoDeOrigen.nuevo(tipo_de_origen)
        self.filtrados = filtro.aplicar(self.filtrados)

    def filtrar_por_equipo(self, equipo, vendedor):
        if vendedor and vendedor.isdigit():
            self.filtrados = self.filtrados.filter(vendedor=vendedor)
        elif equipo and equipo.isdigit():
            self.filtrados = self.filtrados.filter(vendedor__equipo=equipo)

    def filtrar_por_campania(self, campania):
        if campania:
            self.filtrados = self.filtrados.filter(campania__id=campania)

    def filtrar_por_busqueda(self, texto):
        if texto:
            self.filtrados = Prospecto.objects.buscar_con_texto(self.filtrados, texto)

    def filtrar_por_tags(self, vendedor, filtros):
        tags = filtros.get('tags')
        if tags and not tags == ['']:
            seleccionados = Tag.objects.filter(vendedor=vendedor, nombre__in=tags)
            ids = seleccionados.values_list('prospectos', flat=True).distinct()
            self.filtrados = self.filtrados.filter(id__in=ids)

    def filtrar_por_distribuidor(self, id_campanias, id_distribuidor):
        if not id_distribuidor:
            return self.filtrados
        else:
            campanias_con_distribuidores_seleccionados = Campania.objects.filter(id__in=id_campanias).filter(
                categoria__distribuidor=id_distribuidor)
            self.filtrados = self.filtrados.filter(campania__in=campanias_con_distribuidores_seleccionados)

    def filtrar_por_marca(self, filtros):
        ids = filtros.get('marcas', [])
        if ids:
            self.filtrados = self.filtrados.filter(_marca__in=ids)


class FiltroAdministracion(FiltrosParaProspectos):

    def aplicar_filtros_globales(self, filtros):
        tipo_origen = self.get_filtro_tipo_origen(filtros)
        if tipo_origen:
            self.filtrados = self.filtrados.filter(campania__categoria__tipo_de_origen=tipo_origen)

        vendedor = self.usuario.vendedor
        self.filtrar_por_estado(self.obtener_filtros_por_estado(filtros), vendedor)
        self.filtrar_por_rango_de_fechas(filtros)
        filtros_de_llamado = dict()
        self.obtener_filtros_por_llamado(filtros, filtros_de_llamado)
        self.filtrar_por_llamado(filtros_de_llamado)
        if vendedor.es_supervisor():
            self.filtrar_por_exportado(filtros)
        self.filtrar_por_dias_sin_sms(filtros)

        campania = filtros.get('campania', None)
        if campania:
            self.filtrados = self.filtrados.filter(campania__id=campania)

        self.filtrar_por_vendedor_asignado(filtros)

    def aplicar_filtros_de_marca_provincia_prefijo_y_etiquetas(self, filtros):
        self.filtrar_por_marca(filtros)
        self.filtrar_por_modelo(filtros)
        self.filtrar_por_provincia(filtros)
        self.filtrar_por_prefijo(filtros)
        self.filtrar_por_etiqueta(filtros)

    def filtrar_por_no_archivado(self):
        self.filtrados = self.filtrados.filter(archivado__isnull=True)

    def filtrar_por_exportado(self, filtros):
        exportado = filtros.get('exportados')
        if exportado and exportado == 'no':
            self.filtrados = self.filtrados.filter(exportado=False)
        if exportado and exportado == 'si':
            self.filtrados = self.filtrados.filter(exportado=True)

    def filtrar_por_dias_sin_sms(self, filtros):
        dias_sin_sms = filtros.get('dias_sin_sms')
        dias = {'7': -7, '30': -30, '90': -90}
        if not dias_sin_sms or dias_sin_sms not in dias:
            return
        hace_dias = timezone.now() + timezone.timedelta(days=dias[dias_sin_sms], seconds=-1)
        self.filtrar_prospectos_enviados_por_sms(hace_dias)

    def filtrar_prospectos_enviados_por_sms(self, dias):
        campanias_creadas_hace_dias = CampaniaDeComunicacion.objects.creadas_posterior_a(dias)
        prospectos = Prospecto.objects.filter(campanias_de_comunicaciones__in=campanias_creadas_hace_dias)
        self.filtrados = self.filtrados.exclude(id__in=prospectos)

    def filtrar_por_vendedor_asignado(self, filtros):
        asignados = filtros.get('asignados')
        if not asignados == 'si':
            self.filtrados = self.filtrados.filter(vendedor__isnull=True)
        else:
            vendedores = filtros.getlist('vendedores')
            if vendedores and vendedores != ['']:
                self.filtrados = self.filtrados.filter(vendedor__in=vendedores)
            else:
                self.filtrados = self.filtrados.none()

    def obtener_lista_a_filtrar(self, nombre, filtros, remover_vacio=True):
        lista = filtros.getlist(nombre, [])
        if remover_vacio and '' in lista:
            lista.remove('')
        if 'sin-dato' in lista:
            lista.remove('sin-dato')
            lista.append('')
        return lista

    def filtrar_por_provincia(self, filtros):
        provincias = self.obtener_lista_a_filtrar('provincias', filtros, remover_vacio=False)
        if provincias:
            self.filtrados = self.filtrados.filter(provincia__in=provincias)

    def filtrar_por_marca(self, filtros):
        ids = self.obtener_lista_a_filtrar('marcas', filtros, remover_vacio=False)
        if ids:
            self.filtrados = self.filtrados.filter(_marca__in=ids)

    def filtrar_por_modelo(self, filtros, incluye_sin_modelos=True):
        ids = self.obtener_lista_a_filtrar('modelos', filtros, remover_vacio=False)
        if '' in ids:
            agregar_opcion_sin_modelos = incluye_sin_modelos
            ids.remove('')
        else:
            agregar_opcion_sin_modelos = False
        # marcas_ids = self.obtener_lista_a_filtrar('marcas', filtros)
        # Quita todos los ids de modelos de otras marcas
        # Comento el filtro por marcas porque tenemos prospectos mal formados
        # modelos = Modelo.objects.con_ids(ids).de_marcas(marcas_ids)
        modelos = Modelo.objects.con_ids(ids)
        modelos_filtrados_ids = modelos.ids()

        if not agregar_opcion_sin_modelos and not modelos_filtrados_ids:
            return

        if modelos_filtrados_ids or agregar_opcion_sin_modelos:
            query = Q(_modelos__in=modelos_filtrados_ids)
            if agregar_opcion_sin_modelos:
                query |= Q(_modelos__isnull=True)
            self.filtrados = self.filtrados.filter(query)

    def filtrar_por_prefijo(self, filtros):
        prefijos = self.obtener_lista_a_filtrar('prefijos', filtros, remover_vacio=False)
        if prefijos:
            self.filtrados = self.filtrados.filter(prefijo__in=prefijos)

    def filtrar_por_etiqueta(self, filtros):
        etiquetas = self.obtener_lista_a_filtrar('etiquetas', filtros, remover_vacio=False)
        incluir_sin_etiqueta = '' in etiquetas

        # Solo incluye a sin etiqueta:
        if incluir_sin_etiqueta and len(etiquetas) == 1:
            self.filtrados = self.filtrados.filter(tags__isnull=True)
        # Incluye a sin etiqueta y a otras etiquetas
        elif incluir_sin_etiqueta and len(etiquetas) > 1:
            self.filtrados = self.filtrados.filter(Q(tags__nombre__in=etiquetas) | Q(tags__isnull=True)).distinct()
        # Otras etiquetas y sin tener en cuenta a lo que no tienen etiquetas
        elif not incluir_sin_etiqueta and len(etiquetas) > 0:
            self.filtrados = self.filtrados.filter(tags__nombre__in=etiquetas).distinct()

    def obtener_filtros_por_estado(self, request):
        result = {'filtro_estado': request.get('filter_estado', 'activo'),
                  'inactividad': request.get('inactividad', None),
                  'dias-en-reposo': request.get('dias-en-reposo', None)}
        if result['filtro_estado'] == 'F':
            result['motivo_de_finalizacion'] = request.get('motivo_de_finalizacion', '')
        return result

    def filtrar_por_seleccion(self, seleccion):
        modo_de_seleccion = seleccion.get('mode')
        valores = json.loads(seleccion.get('values', '[]'))
        primary_keys = [int(pk) for pk in valores]
        if modo_de_seleccion == 'selection':
            self.filtrados = self.filtrados.filter(pk__in=primary_keys)
        elif modo_de_seleccion == 'deselection':
            self.filtrados = self.filtrados.exclude(pk__in=primary_keys)

    def filtrar_por_rango_de_fechas(self, filtros):
        desde = filtros.get('fecha_desde')
        hasta = filtros.get('fecha_hasta')
        estado = filtros.get('filter_estado', 'activo')
        if desde:
            desde = make_aware(datetime.strptime(desde, '%Y-%m-%d'), timezone.utc)
        if hasta:
            hasta = make_aware(datetime.strptime(hasta, '%Y-%m-%d'), timezone.utc)

        if estado == 'V':
            if desde:
                self.filtrados = self.filtrados.filter(ventas__fecha_de_realizacion__gte=desde).distinct()
            if hasta:
                self.filtrados = self.filtrados.filter(
                    ventas__fecha_de_realizacion__lt=hasta + timezone.timedelta(days=1)).distinct()
        else:
            if desde:
                self.filtrados = self.filtrados.filter(fecha_creacion__gte=desde)
            if hasta:
                self.filtrados = self.filtrados.filter(fecha_creacion__lt=hasta + timezone.timedelta(days=1))

    def filtrar_por_accion(self, accion):
        if accion == 'rechazar':
            self.filtrados = Prospecto.objects.prospectos_rechazables_segun_fecha(qs=self.filtrados)


class Filtro(object, metaclass=abc.ABCMeta):
    """
        TODO: refactorizando filtros de a poco. La idea es ir reemplanzando por estos objetos, quizas modelar
        algun filtro compuesto

    """

    def aplicar(self, queryset):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def nuevo_para(cls, clave, *args, **kwargs):
        subclases = cls._sub_filtros()
        for each in subclases:
            if each.tiene_clave(clave):
                return each(*args, **kwargs)
        return cls.vacio()

    @classmethod
    def _sub_filtros(cls):
        return cls.__subclasses__()

    @classmethod
    def tiene_clave(cls, clave):
        return clave == cls.clave()

    @classmethod
    def clave(cls):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def vacio(cls):
        return FiltroVacio()


class FiltroVacio(Filtro):
    def aplicar(self, queryset):
        return queryset

    @classmethod
    def clave(cls):
        return ''


class FiltroDeProspectos(Filtro, metaclass=abc.ABCMeta):
    pass


class FiltroPorFechas(FiltroDeProspectos, metaclass=abc.ABCMeta):
    def __init__(self, fecha_desde, fecha_hasta):
        super(FiltroPorFechas, self).__init__()
        self._fecha_desde = fecha_desde
        self._fecha_hasta = fecha_hasta


class FiltroPorFechasDelDato(FiltroPorFechas):
    def aplicar(self, queryset):
        return queryset.entre_fechas_del_dato(self._fecha_desde, self._fecha_hasta)

    @classmethod
    def clave(cls):
        return 'fecha-del-dato'


class FiltroPorFechasDeCreacion(FiltroPorFechas):
    def aplicar(self, queryset):
        return queryset.entre_fechas(self._fecha_desde, self._fecha_hasta)

    @classmethod
    def clave(cls):
        return 'fecha-de-creacion'


class FiltroPorFechasDeAsignacionDeResponsable(FiltroPorFechas):
    def aplicar(self, queryset):
        return queryset.entre_fechas_de_asignacion_a_responsable(self._fecha_desde, self._fecha_hasta)

    @classmethod
    def clave(cls):
        return 'fecha-de-asignacion-de-responsable'


class FiltroPorFechasDeAsignacionDeVendedor(FiltroPorFechas):
    def aplicar(self, queryset):
        return queryset.entre_fechas_de_asignacion_a_vendedor(self._fecha_desde, self._fecha_hasta)

    @classmethod
    def clave(cls):
        return 'fecha-de-asignacion-de-vendedor'


class FiltroPorTipoDeOrigen(FiltroDeProspectos):

    def __init__(self, tipo_de_origen):
        super(FiltroPorTipoDeOrigen, self).__init__()
        self._tipo_de_origen = tipo_de_origen

    def aplicar(self, queryset):
        if self._tipo_de_origen:
            return queryset.con_tipo_de_origen(self._tipo_de_origen)
        else:
            return queryset

    @classmethod
    def nuevo(cls, tipo_de_origen):
        return cls(tipo_de_origen)

    @classmethod
    def clave(cls):
        return 'tipo-de-origen'