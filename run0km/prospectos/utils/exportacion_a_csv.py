# coding=utf-8
import csv
import re
from datetime import datetime

from django.http import StreamingHttpResponse
from django.http.response import HttpResponse, HttpResponseForbidden
from django.utils.encoding import smart_str

from prospectos.configuracion import CAMPOS_DE_PROSPECTO_PARA_EXPORTAR, CAMPOS_EXTRA_QUE_NO_DEBEN_EXPORTARSE
from prospectos.models import Prospecto, LogDeExportacionDeProspecto

# Pasar a Settings
FORMATO_FECHA_EXPORTACION = '%d/%m/%Y %H:%M'


class CSVHttpResponseWriter(object):
    def __init__(self):
        self._response = HttpResponse(content_type='text/csv')
        self._response['Content-Disposition'] = 'attachment; filename=prospectos.csv'
        self._response.write('\\ufeff'.encode('utf8'))  # BOM (optional...Excel needs it to open UTF-8 file properly)
        self._writer = csv.writer(self._response, csv.excel)
        self._clean_exp = re.compile('\r\n|\n|\r')

    def write_row(self, row):
        self._writer.writerow(row)

    def write_header(self, headerrow):
        row = [smart_str(each.title()) for each in headerrow]
        self.write_row(row)

    def write_row_list(self, row_list, header):
        for each_row in row_list:
            # writer.writerow([re.sub(exp, ' ', smart_str(info.get(x, ''))) for x in campos])
            cells = [self._cell_value_for(each_cell, each_row) for each_cell in header]
            self.write_row(cells)

    def _cell_value_for(self, cell_key, row):
        # re.sub(exp, ' ', smart_str(info.get(x, '')))
        value = row.get(cell_key, '')
        return self.clean_value(value)

    def clean_value(self, value):
        # re.sub(exp, ' ', smart_str(info.get(x, '')))
        if value:
            return re.sub(self._clean_exp, ' ', smart_str(value))
        else:
            return ''

    def response(self):
        return self._response

    def responder_error_por_falta_de_permisos_de_exportacion(self, user):
        return HttpResponseForbidden(content='El Usuario {0} de id {1} no tiene Permisos de Exportacion.'.format(
            user.username, user.id))


class Echo(object):
    """An object that implements just the write method of the file-like
    interface.
    """

    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


class GeneradorDeDatosDeExportacionDeProspectos(object):

    def __init__(self, campos_extras_exportables=None):
        """ Si campos_extras_exportables_exportables es None, se exportan todos los campos extras menos
        los indicados por CAMPOS_EXTRA_QUE_NO_DEBEN_EXPORTARSE."""
        super().__init__()
        if campos_extras_exportables is not None:
            campos_extras_exportables = [campo.lower() for campo in campos_extras_exportables]
        self._campos_extras_exportables = campos_extras_exportables

    @classmethod
    def nuevo_para_spreadsheet(cls):
        return cls(campos_extras_exportables=cls._campos_extra_para_exportar())

    @classmethod
    def nuevo_para_csv(cls):
        return cls(campos_extras_exportables=[])

    @classmethod
    def campos_por_defecto(cls):
        campos = sorted(list(CAMPOS_DE_PROSPECTO_PARA_EXPORTAR))
        campos.remove('campania')
        campos.insert(0, 'campaña')
        campos.insert(0, 'origen')
        campos.remove('modelo')
        campos.append('modelos')
        return campos

    def generar_datos_para(self, prospectos):
        campos = self._generar_campos(prospectos)
        filas = self._generar_lista_de_filas_para(prospectos, campos)
        return campos, filas

    def generar_fila_para(self, prospecto, campos=None, agregar_campos_extras=False):
        if campos is None:
            campos = self.__class__.campos_por_defecto()

        datos_del_prospecto = prospecto.__dict__.copy()
        fila_prospecto = {key: value for key, value in list(datos_del_prospecto.items()) if key in campos}
        if "telefono" in fila_prospecto:
            fila_prospecto["telefono"] = self._formatear_telefono(fila_prospecto["telefono"])
        fila_prospecto.update({'vendedor': self._nombre_de_vendedor_de(prospecto),
                               'campaña': prospecto.campania.nombre,
                               'origen': prospecto.campania.origen,
                               'marca': prospecto.obtener_marca().nombre(),
                               'modelos': prospecto.modelos_como_string(),
                               'fecha': self._formatear_fecha(prospecto.fecha),
                               'estado': prospecto.get_estado_display()})

        self._agregar_info_de_finalizacion(campos, fila_prospecto, prospecto)
        if agregar_campos_extras:
            self._agregar_info_de_campos_extra_en_fila_y_campos(campos, fila_prospecto, prospecto)
        return fila_prospecto

    def _agregar_info_de_finalizacion(self, campos, fila_prospecto, prospecto):
        if 'motivo de finalizacion' in campos and prospecto.finalizado:
            fila_prospecto['motivo de finalizacion'] = self._motivo_finalizacion_de(prospecto)
            fila_prospecto['comentario de finalizacion'] = prospecto.finalizacion.comentario

    def _generar_campos(self, prospectos):
        campos = self.__class__.campos_por_defecto()
        finalizado = prospectos.finalizados().exists()
        if finalizado:
            campos.append('motivo de finalizacion')
            campos.append('comentario de finalizacion')
        return campos

    def _generar_lista_de_filas_para(self, prospectos, campos):
        filas = []
        prospectos_optimizados = self._consulta_optmizada_para(prospectos)
        for prospecto in prospectos_optimizados.all():
            fila = self.generar_fila_para(prospecto, campos, agregar_campos_extras=True)
            filas.append(fila)
        return filas

    def _consulta_optmizada_para(self, prospectos):
        prospectos = Prospecto.objects.filter(id__in=prospectos.values_list('id', flat=True))
        consulta_optmizada = prospectos.select_related('vendedor__user', 'campania',
                                                       'campania__categoria__tipo_de_origen',
                                                       '_marca',
                                                       'finalizacion', 'finalizacion__motivo',
                                                       ).prefetch_related('campos_extra')
        return consulta_optmizada

    def _agregar_info_de_campos_extra_en_fila_y_campos(self, campos, info, prospecto):
        for extra in prospecto.campos_extra.exclude(nombre__in=CAMPOS_EXTRA_QUE_NO_DEBEN_EXPORTARSE):
            nombre = extra.nombre.lower()
            if self._es_un_campo_extra_exportable(nombre):
                if nombre not in campos:
                    campos.append(nombre)
                info[nombre] = extra.valor

    def _motivo_finalizacion_de(self, prospecto):
        finalizacion = prospecto.finalizacion
        if finalizacion.motivo:
            motivo = prospecto.finalizacion.motivo.descripcion
        elif prospecto.finalizacion.otro_motivo:
            motivo = prospecto.finalizacion.otro_motivo
        else:
            motivo = ''
        return motivo

    def _nombre_de_vendedor_de(self, prospecto):
        if prospecto.vendedor:
            return prospecto.vendedor.username()
        else:
            return ''

    def _formatear_fecha(self, fecha):
        if fecha:
            return fecha.strftime(FORMATO_FECHA_EXPORTACION)
        else:
            return ''

    def _formatear_telefono(self, telefono):
        if telefono:
            return telefono.replace(' ', '')
        else:
            return ''

    def _es_un_campo_extra_exportable(self, nombre):
        return self._campos_extras_exportables == [] or nombre in self._campos_extras_exportables

    def campos_a_exportar_para_spreadsheet(self):
        exportables = self._campos_extras_exportables if self._campos_extras_exportables else []
        return self.campos_por_defecto() + exportables

    @classmethod
    def _campos_extra_para_exportar(cls):
        return ["transcriptUrl"]


class ExportadorDeProspectosACSV(object):
    def exportar(self, prospectos, user):
        writer = CSVHttpResponseWriter()
        if user.puede_exportar_prospectos():
            self._escribir(prospectos, writer)
            cantidad_prospectos_exportados = prospectos.count()
            prospectos.update(exportado=True)
            self._logear_exportacion_realizada(cantidad_prospectos_exportados=cantidad_prospectos_exportados, user=user)
            return writer.response()
        return writer.responder_error_por_falta_de_permisos_de_exportacion(user=user)

    def exportar_en_streaming_response(self, prospectos, user):
        campos, filas = self._generar_datos_para(prospectos)
        cantidad_prospectos_exportados = prospectos.count()
        prospectos.update(exportado=True)
        self._logear_exportacion_realizada(cantidad_prospectos_exportados=cantidad_prospectos_exportados, user=user)

        pseudo_buffer = Echo()
        writer = csv.writer(pseudo_buffer)
        rows = self._preparar_filas_para_stream(campos, filas)
        response = StreamingHttpResponse((writer.writerow(row) for row in rows),
                                         content_type="text/csv")
        response['Content-Disposition'] = 'attachment; filename="prospectos.csv"'
        return response

    def _generar_datos_para(self, prospectos):
        generador = GeneradorDeDatosDeExportacionDeProspectos.nuevo_para_csv()
        return generador.generar_datos_para(prospectos)

    def _escribir(self, prospectos, writer):
        campos, filas = self._generar_datos_para(prospectos)
        writer.write_header(campos)
        writer.write_row_list(filas, campos)

    def _logear_exportacion_realizada(self, cantidad_prospectos_exportados, user):
        LogDeExportacionDeProspecto.registrar(
            user=user,
            fecha=datetime.now().date(),
            cantidad=cantidad_prospectos_exportados)

    def _preparar_filas_para_stream(self, campos, filas):
        _clean_exp = re.compile('\r\n|\n|\r')
        filas_completas = [campos]
        for fila in filas:
            nueva_fila = [self._valores_de_fila(nombre_campo, fila, _clean_exp) for nombre_campo in campos]
            filas_completas.append(nueva_fila)
        return filas_completas

    def _valores_de_fila(self, nombre_campo, fila, _clean_exp):
        value = fila.get(nombre_campo, '')
        if value:
            return re.sub(_clean_exp, ' ', smart_str(value))
        else:
            return ''


class ExportadorDeCargasFallidasACSV(object):
    def __init__(self, cargas):
        self.cargas = cargas

    def response_para_exportar_a_csv(self):
        (campos, info_cargas) = self.procesar_info_para_exportar()

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename=cargas_fallidas.csv'
        writer = csv.writer(response, csv.excel)

        writer.writerow([smart_str(x.title(), encoding='iso-8859-15') for x in campos])
        for info in info_cargas:
            writer.writerow([smart_str(info.get(x, ''), encoding='iso-8859-15') for x in campos])

        self.cargas.update(exportado=True)

        return response

    def procesar_info_para_exportar(self):
        campos = list()
        campos.append('fecha')
        info_cargas = list()

        for carga in self.cargas:
            info = dict()
            datos_carga = carga.obtener_datos_de_carga()
            for campo in datos_carga:
                if not campo in campos:
                    campos.append(campo)
                info[campo] = datos_carga[campo]
            info['fecha'] = carga.fecha.strftime('%d/%m/%Y %H:%M')
            info['error-de-carga'] = carga.error

            info_cargas.append(info)
        campos.append('error-de-carga')

        return campos, info_cargas
