import mock
from django.conf import settings
from django.core.management.base import BaseCommand

from notificaciones import FormaDeEnvioEmail
from prospectos.models import Prospecto
from prospectos.utils.notificaciones_de_prospectos import NotificadorDeProspectos
from vendedores.models import Vendedor


class Command(BaseCommand):
    help = 'prueba envio de mail'

    def handle(self, *args, **options):
        notificador = NotificadorDeProspectos()
        forma_de_envio = FormaDeEnvioEmail
        prospecto = Prospecto.objects.last()
        prospecto.email = '<EMAIL>'
        prospecto.save()
        vendedor = Vendedor.objects.last()

        # override_settings(STATIC_URL='http://delivery-staging.eryxsoluciones.com.ar/static/')
        with mock.patch("vendedores.models.Vendedor.email", return_value='<EMAIL>') as mock_email_vendedor:
            settings.STATIC_URL = 'http://delivery-staging.eryxsoluciones.com.ar/static/'
            # print(settings.STATIC_URL)
            notificador.notificar_asignacion(prospecto, vendedor, [forma_de_envio])
