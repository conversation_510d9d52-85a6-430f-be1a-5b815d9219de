from itertools import groupby

from django.core.management.base import BaseCommand, CommandError

from prospectos.models import PedidoDeProspecto
from prospectos.models.entrega_de_datos.metodos_de_seleccion import ConfiguracionDeEntrega
from vendedores.models import Vendedor


class Command(BaseCommand):
    help = 'Analiza funcionamiento de entregas'

    def add_arguments(self, parser):
        # Positional arguments
        parser.add_argument('--pedido', dest='pedido_id')

    def handle(self, *args, **options):
        pedido_pk = options.get('pedido_id')
        try:
            pedido = PedidoDeProspecto.objects.get(pk=pedido_pk)
        except PedidoDeProspecto.DoesNotExist:
            raise CommandError('PedidoDeProspecto "%s" does not exist' % pedido_pk)
        analizar_entregas_de(pedido)


def print_restricciones(vendedor):
    configuracion = ConfiguracionDeEntrega(vendedor)
    print("limite de acceso: %s, limite datos nuevos: %s, limite datos diarios: %s" %
          (configuracion.limite_de_acceso_en_horas(),
           configuracion.limite_de_datos_nuevos(),
           configuracion.limite_de_datos_diarios()))


def analizar_entregas_de(pedido):
    prospectos_asignados = pedido.prospectos_asignados.all()
    vendedores = Vendedor.objects.filter(prospectos__in=prospectos_asignados).distinct()

    for each in vendedores.all():
        print("%s (pk=%s)" % (each.full_name(), each.pk))
        print_restricciones(each)
        prospectos = prospectos_asignados.filter(vendedor=each).order_by('fecha_creacion')
        for key, group in groupby(prospectos.all(), lambda each_prospecto: (each_prospecto.fecha_creacion.year,
                                                                            each_prospecto.fecha_creacion.month,
                                                                            each_prospecto.fecha_creacion.day,)):

            asignaciones = [p.fecha_de_asignacion_a_vendedor().strftime('%m/%d %H:%M') for p in group]
            print("Fecha %s/%s/%s: %s" % (key[0], key[1], key[2], len(asignaciones)))
            print("Ultimas asignaciones: " + " - ".join(asignaciones))
