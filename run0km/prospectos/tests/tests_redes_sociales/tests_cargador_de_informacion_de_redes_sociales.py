#-*- coding: utf-8 -*-
import json
from testing.base import BaseFixturedTest
from prospectos.utils.informacion_de_redes_sociales import CargadorDeInformacionDeRedesSociales
from prospectos.models import LogDeErrorDeInformacionDeRedesSociales


class CargadorDeInformacionDeRedesSocialesTest(BaseFixturedTest):
    def _error_request_para(self, prospecto):
        service_error_request = [{"Referencia": "%s" % prospecto.pk,
                                  "Error": "Timeout expired. "
                                           "The timeout period elapsed prior to completion of the operation or "
                                           "the server is not responding.",
                                  }]
        return service_error_request

    def test_todo_ok(self):
        cargador = CargadorDeInformacionDeRedesSociales()
        p1 = self.fixture['p_1']
        example_request = self.example_request()
        example_request[0]['Referencia'] = str(p1.id)
        errors = cargador.cargar_informacion_de_redes_sociales(example_request)
        self.assertEqual(p1.informacion_de_redes_sociales.count(), 8)
        info_cel = p1.informacion_de_redes_sociales.get(tipo='Celular')
        self.assertEqual(info_cel.valor, json.dumps(example_request[0]['Result']['Canales'][0]['Canal']))
        info_tel = p1.informacion_de_redes_sociales.get(tipo='Telefono')
        self.assertEqual(info_tel.valor, json.dumps(example_request[0]['Result']['Canales'][1]['Canal']))
        info_dir = p1.informacion_de_redes_sociales.get(tipo='Direccion')
        self.assertEqual(info_dir.valor, json.dumps(example_request[0]['Result']['Canales'][2]['Canal']))
        info_mail = p1.informacion_de_redes_sociales.get(tipo='Email')
        self.assertEqual(info_mail.valor, json.dumps(example_request[0]['Result']['Canales'][3]['Canal']))
        info_nom = p1.informacion_de_redes_sociales.get(tipo='Nombre completo')
        self.assertEqual(info_nom.valor, 'Nicolas Stajnsznader')
        info_doc = p1.informacion_de_redes_sociales.get(tipo='Identificación')
        self.assertEqual(info_doc.valor, 'DNI: 24043616')
        info_foto = p1.informacion_de_redes_sociales.get(tipo='Foto')
        self.assertEqual(info_foto.valor, self.foto_url())
        info_vehiculos = p1.informacion_de_redes_sociales.get(tipo='Vehiculos')
        self.assertEqual(info_vehiculos.valor, 'Chevrolet Cruze, volkswagen ')

    def test_cargador_de_informacion_no_falla_si_el_modelo_posee_caracteres_especiales(self):
        cargador = CargadorDeInformacionDeRedesSociales()
        p1 = self.fixture['p_1']
        example_request = self.example_request()
        example_request[0]['Referencia'] = str(p1.id)
        example_request[0]['Result']['Vehiculos'][0]['Modelo'] = 'Citroën C4 Lounge'
        example_request[0]['Result']['Vehiculos'][0]['Marca'] = 'CITROEN'
        errors = cargador.cargar_informacion_de_redes_sociales(example_request)
        info_vehiculos = p1.informacion_de_redes_sociales.get(tipo='Vehiculos')
        self.assertEqual(info_vehiculos.valor, 'CITROEN Citroën C4 Lounge, volkswagen ')

    def test_respuesta_con_error(self):
        cargador = CargadorDeInformacionDeRedesSociales()
        prospecto = self.fixture['p_1']
        redes_request = self._error_request_para(prospecto)
        cargador.cargar_informacion_de_redes_sociales(redes_request)
        logs = LogDeErrorDeInformacionDeRedesSociales.objects.all()
        self.assertEqual(logs.count(), 1)
        log = logs[0]
        self.assertEqual(log.tipo, 'Error en el servicio')
        self.assertEqual(log.descripcion, 'Servicio reporta error de proceso.')
        self.assertEqual(log.response, json.dumps(redes_request[0]))

    def test_respuesta_sin_datos(self):
        cargador = CargadorDeInformacionDeRedesSociales()
        p1 = self.fixture['p_1']
        cargador.cargar_informacion_de_redes_sociales([{"Referencia": "11"}, ])
        logs = LogDeErrorDeInformacionDeRedesSociales.objects.all()
        self.assertEqual(logs.count(), 0)

    def test_formato_de_datos_erroneo(self):
        # Que se logguee toda la respuesta si el formato de los datos es erroneo (y se responda el motivo)
        # Pensar varios....
        pass

    def test_carga_de_datos_con_problema_por_prospecto(self):
        # Que si hay errores en la carga de los datos, se loggue el error de cada prospecto, pero que el resto se
        # cargue bien. La respuesta debe contener los erroneos y la cantidad de correctos?.
        pass

    def empty_example_request(self):
        return [{'Referencia': '', 'Result': {}}, ]

    def foto_url(self):
        return 'https://d2ojpxxtu63wzl.cloudfront.net/static/898101b295b4eda99bd71860ee2a420b_bdd264173e24d2067810aec660c2c3e70d9966aa7b5d3e0a86e8f741e2f41f96'

    def example_request(self):
        return [
    {
        "Referencia": "11",
        "Result": {
            "Razon": "Nicolas Stajnsznader",
            "Canales": [
                {
                    "Tipo": "Celular",
                    "Canal": {
                        "Prioridad": 0,
                        "Tipo": "Celular",
                        "Medio": {
                            "Razon": "TELECOM PERSONAL S.A."
                        },
                        "Celular": {
                            "CodPais": "54",
                            "CodArea": "11",
                            "Numero": "1164462329",
                            "Normalizado": True,
                            "NormalizadoOk": True,
                            "NormalizadoMovil": True,
                            "NormalizadoFecha": "2015-09-23T18:21:26.89",
                            "NormalizadoSpam": True,
                            "Tipo": "Celular",
                            "Localidad": {
                                "Nombre": "AMBA",
                                "Provincia": {
                                    "Nombre": "BUENOS AIRES",
                                    "Pais": {
                                        "Nombre": "ARGENTINA"
                                    }
                                }
                            },
                            "Provincia": {
                                "Nombre": "BUENOS AIRES",
                                "Pais": {
                                    "Nombre": "ARGENTINA"
                                }
                            }
                        }
                    }
                },
                {
                    "Tipo": "Telefono",
                    "Canal": {
                        "Prioridad": 0,
                        "Tipo": "Telefono",
                        "Medio": {
                            "Razon": "TELECOM ARGENTINA STET FRANCE TELECOM S.A."
                        },
                        "Telefono": {
                            "CodPais": "54",
                            "CodArea": "11",
                            "Numero": "1147712231",
                            "Normalizado": True,
                            "NormalizadoOk": True,
                            "NormalizadoMovil": False,
                            "NormalizadoFecha": "2015-09-15T19:58:51.383",
                            "NormalizadoSpam": False,
                            "Tipo": "Telefono",
                            "Localidad": {
                                "Nombre": "AMBA",
                                "Provincia": {
                                    "Nombre": "BUENOS AIRES",
                                    "Pais": {
                                        "Nombre": "ARGENTINA"
                                    }
                                }
                            },
                            "Provincia": {
                                "Nombre": "BUENOS AIRES",
                                "Pais": {
                                    "Nombre": "ARGENTINA"
                                }
                            }
                        }
                    }
                },
                {
                    "Tipo": "Direccion",
                    "Canal": {
                        "Prioridad": 0,
                        "Tipo": "Direccion",
                        "Medio": {
                            "Razon": "PERSONAL"
                        },
                        "Direccion": {
                            "Calle": "Jose Antonio Maure 2301 7 B",
                            "CP": "C1426CUQ",
                            "Tipo": "Direccion",
                            "Localidad": {
                                "Nombre": "Ciudad Autonoma Buenos Aires",
                                "Provincia": {
                                    "Nombre": "CAPITAL FEDERAL",
                                    "Pais": {
                                        "Nombre": "ARGENTINA"
                                    }
                                }
                            },
                            "Pais": {
                                "Nombre": "ARGENTINA"
                            },
                            "Provincia": {
                                "Nombre": "CAPITAL FEDERAL",
                                "Pais": {
                                    "Nombre": "ARGENTINA"
                                }
                            }
                        }
                    }
                },
                {
                    'Canal': {
                        'Prioridad': 0,
                        'Tipo': 'Email',
                        'Email': {
                            'Tipo': 'Email',
                            'Direccion': '<EMAIL>'
                        }
                    },
                    'Origen': {
                        'Razon': 'Cablevision S.A.'
                    },
                    'Tipo': 'Email'
                }
            ],
            "Fisica": {
                "Nombre": "Nicolas",
                "Apellido": "Stajnsznader"
            },
            "Identificaciones": [
                {
                    "Valor": "24043616",
                    "Tipo": {
                        "Nombre": "DNI"
                    }
                }
            ],
            "FotoUrl": self.foto_url(),
            'Vehiculos': [
                {
                    'Marca': 'Chevrolet',
                    'Modelo': 'Cruze'
                },
                {
                    'Marca': 'volkswagen',
                    'Modelo': ''
                }
            ]
        }
    }
]


