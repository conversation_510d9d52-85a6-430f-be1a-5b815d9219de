from django.core.exceptions import ValidationError
from django.utils import timezone

from prospectos.models import DatosDeArchivadoParaProspecto
from prospectos.models import Prospecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model
from vendedores.gestion_de_ventas import GestorDeVenta


class GestorDeProspectoTest(BaseFixturedTest):
    def setUp(self):
        super(GestorDeProspectoTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self.prospecto.responsable = self.fixture['sup_1']
        self.repartidor = RepartidorDeProspectos.nuevo()

    def test_finalizar_prospecto_de_otro_vendedor_debe_lanzar_excepcion(self):
        otro_vendedor = self.fixture['vend_2']
        gestor = GestorDeProspecto.nuevo_para(rol=otro_vendedor)
        self.assertRaises(ValidationError, gestor.finalizar_prospecto, prospecto=self.prospecto, comentario=None)

    def test_finalizar_prospecto_con_motivo_finaliza_con_exito(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self.prospecto.obtener_vendedor())
        gestor.finalizar_prospecto(prospecto=self.prospecto, comentario="El comprador desaparecio")
        self.prospecto.refresh_from_db()
        self.assertEqual(self.prospecto.estado, Prospecto.FINALIZADO)
        self.assertTrue(self.prospecto.finalizado)
        self.assertIsNone(self.prospecto.finalizacion.motivo)
        self.assertEqual(self.prospecto.finalizacion.comentario, "El comprador desaparecio")
        self.assertEqual(self.prospecto.finalizacion.vendedor, self.prospecto.vendedor)

    def test_archivar_prospectos_asocia_archivado_a_cada_uno(self):
        responsable = self.prospecto.obtener_responsable()
        prospectos = [self.fixture['p_2'], self.fixture['p_3']]
        self._asociar_responsable_a_prospectos(prospectos=prospectos,
                                               responsable=responsable)
        prospectos_a_cargo = responsable.prospectos_a_cargo
        cantidad = prospectos_a_cargo.count()
        gestor = GestorDeProspecto.nuevo_para(rol=responsable)
        gestor.archivar_prospectos(prospectos=prospectos_a_cargo)
        self.assertTrue(responsable.prospectos_a_cargo.exists())
        self.assertEqual(DatosDeArchivadoParaProspecto.objects.count(), cantidad)
        self._assert_archivados(prospectos)

    def test_archivar_prospectos_que_no_son_del_supervisor_lanza_excepcion(self):
        responsable = self.prospecto.obtener_responsable()
        prospecto_de_otro_supervisor = self.fixture['p_4']
        self._asociar_responsable_a_prospectos(prospectos=[prospecto_de_otro_supervisor],
                                               responsable=self.fixture['sup_2'])
        gestor = GestorDeProspecto.nuevo_para(rol=responsable)
        self.assertRaises(ValidationError, gestor.archivar_prospectos,
                          prospectos=Prospecto.objects.filter(id__in=[prospecto_de_otro_supervisor.id]))
        self.assertEqual(DatosDeArchivadoParaProspecto.objects.count(), 0)
        prospecto_de_otro_supervisor = reload_model(prospecto_de_otro_supervisor)
        self.assertFalse(prospecto_de_otro_supervisor.esta_archivado())

    def test_rechazar_prospecto_en_proceso_lo_pasa_a_estado_nuevo(self):
        prospectos = Prospecto.objects.filter(id=self.prospecto.id)
        prospecto = prospectos[0]
        self._asociar_responsable_a_prospectos(prospectos=[prospecto],
                                               responsable=self.prospecto.obtener_responsable())
        prospecto = reload_model(prospecto)
        gestor = GestorDeProspecto.nuevo_para(rol=prospecto.obtener_responsable())
        self._pasar_prospecto_a_en_proceso(prospecto=prospecto, gestor=gestor)
        self.assertTrue(prospecto.en_proceso)
        gestor.rechazar_prospectos(prospectos=prospectos)
        prospecto = reload_model(prospecto)
        self.assertTrue(prospecto.es_nuevo())

    def test_cargar_una_venta_con_llamado_programado_deberia_deberia_borrar_llamado_programado(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self.prospecto.obtener_responsable())
        hoy = timezone.now()
        gestor.programar_nuevo_llamado_para(self.prospecto, fecha=hoy)
        self.assertTrue(self.prospecto.tiene_llamado())
        venta = gestor.cargar_venta(
            prospecto=self.prospecto, marca='ford', modelo='ecosport',
            fecha_de_realizacion=hoy, numero_de_contrato='1', precio=100000)

        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.vendido)
        self.assertFalse(prospecto.tiene_llamado())
        self.assertTrue(venta.aprobada())

    def test_cancelar_venta_debe_cambiar_de_estado_de_venta_y_pasar_prospecto_a_en_proceso(self):
        # Dado
        venta = self._cargar_venta(self.prospecto)
        self._desaprobar_venta(venta)
        gestor = GestorDeProspecto.nuevo_para(rol=self.prospecto.obtener_responsable())

        # Cuando
        gestor.cancelar_venta(prospecto=self.prospecto,
                              motivo_de_cancelacion='Comprador en el veraz',
                              estado_a_pasar=Prospecto.EN_PROCESO)

        # Entonces
        venta = reload_model(venta)
        self.assertTrue(venta.cancelada())
        self._assert_cambio_de_estado_a_en_proceso_de(self.prospecto)

    def _assert_archivados(self, prospectos):
        hoy = timezone.localtime(timezone.now()).date()
        for prospecto in prospectos:
            prospecto = reload_model(prospecto)
            self.assertTrue(prospecto.esta_archivado())
            self.assertEqual(prospecto.archivado.fecha, hoy)
            self.assertFalse(prospecto.tiene_vendedor())

    def _assert_cambio_de_estado_a_en_proceso_de(self, prospecto):
        self.prospecto.refresh_from_db()
        self.assertTrue(prospecto.en_proceso)
        comentario = self.prospecto.comentarios.last()
        texto_comentario_esperado = 'El prospecto fue puesto en proceso por %s' % prospecto.responsable.full_name()
        self.assertEqual(comentario.comentario, texto_comentario_esperado)
        self.assertEqual(comentario.vendedor, prospecto.responsable)

    def _asociar_responsable_a_prospectos(self, prospectos, responsable):
        prospectos_queryset = Prospecto.objects.filter(id__in=[prospecto.id for prospecto in prospectos])
        self.repartidor.asignar_prospectos_a_responsable(supervisor=responsable, prospectos=prospectos_queryset)

    def _pasar_prospecto_a_en_proceso(self, prospecto, gestor):
        gestor.comentar_prospecto(prospecto=prospecto, fecha=timezone.now(), texto='Hola', es_automatico=False)

    def _cargar_venta(self, prospecto):
        gestor = GestorDeProspecto.nuevo_para(rol=prospecto.obtener_vendedor())
        venta = gestor.cargar_venta(prospecto=prospecto, marca='ford', modelo='ecosport',
                                    fecha_de_realizacion=timezone.now(), numero_de_contrato='1',
                                    precio=100000, debe_sincronizar=False)
        return venta

    def _desaprobar_venta(self, venta):
        gestor_de_ventas = GestorDeVenta()
        gestor_de_ventas.desaprobar_venta(rol=venta.prospecto.obtener_responsable(), venta=venta)
