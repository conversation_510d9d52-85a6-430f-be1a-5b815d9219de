from django.core.exceptions import ValidationError

from prospectos.models import Venta
from prospectos.tests.gestion.test_todo_create_factory import ToDoCreateFactoryTest


class PrecioDeVentaTest(ToDoCreateFactoryTest):

    def test_al_cargar_una_venta_con_precio_alfanumerico_notifica_error(self):
        self.assertRaisesRegex(ValidationError, 'Solo se permiten numeros, puntos o comas.', Venta.nueva,
                                prospecto=self.prospecto_sin_venta, marca='Ford', vendedor=self.vendedor,
                                modelo='fiesta', fecha_de_realizacion=self.fecha_de_hoy, precio='75000r',
                                numero_de_contrato=1313)

    def test_al_cargar_una_venta_con_precio_menor_a_6_digitos_notifica_error(self):
        self.assertRaisesRegex(ValidationError, 'El precio de la venta debe ser de al menos 6 digitos.', Venta.nueva,
                                prospecto=self.prospecto_sin_venta, marca='Ford', vendedor=self.vendedor,
                                modelo='fiesta', fecha_de_realizacion=self.fecha_de_hoy, precio='75000',
                                numero_de_contrato=1313)

    def test_al_cargar_una_venta_con_formato_incorrecto_notifica_error(self):
        self.assertRaisesRegex(ValidationError, 'Solo se permiten numeros, puntos o comas.',Venta.nueva,
                                prospecto=self.prospecto_sin_venta, marca='Ford', vendedor=self.vendedor,
                                modelo='fiesta', fecha_de_realizacion=self.fecha_de_hoy, precio='.1234.5',
                                numero_de_contrato=1313)

    def test_al_cargar_una_venta_con_precio_valido_guarda_exitosamente(self):
        try:
            venta = Venta.nueva(prospecto=self.prospecto_sin_venta, marca='Ford', vendedor=self.vendedor,
                                modelo='fiesta', fecha_de_realizacion=self.fecha_de_hoy, precio='7500078',
                                numero_de_contrato=1313)
        except Exception:
            self.fail("Error inesperado al crear una venta con datos validos!")
        self.assertEqual(venta.precio, '7500078')