# coding=utf-8
from django.utils import timezone

from prospectos.models import Marca
from prospectos.models.gestor.criterio_de_prospecto_repetido import CriterioDeProspectosRepetidos
from testing.base import BaseFixturedTest


class CriterioDeProspectosRepetidosTest(BaseFixturedTest):
    def setUp(self):
        super(CriterioDeProspectosRepetidosTest, self).setUp()
        self.vendedor_uno = self.fixture['vend_1']
        self.prospecto_uno = self._crear_prospecto_uno_asignado_a(self.vendedor_uno)
        self.vendedor_dos = self.fixture['vend_2']

    def test_prospecto_sin_telefono_pero_con_email_y_marca_iguales_son_considerados_repetidos(self):
        # Dado
        telefono = ''
        email = '<EMAIL>'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospecto_sin_email_pero_con_telefono_y_marca_iguales_son_considerados_repetidos(self):
        # Dado
        email = ''
        telefono = '48397610'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(), telefono=telefono,
            email=email, nombre_de_marca=nombre_de_marca
        )
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospecto_sin_email_con_telefono_normalizados_iguales_y_misma_marca_son_considerados_repetidos(self):
        # Dado
        email = ''
        telefono = '48397610'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono='11' + telefono,
            email=email, nombre_de_marca=nombre_de_marca
        )
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono='011' + telefono, email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospecto_sin_email_con_telefono_normalizados_iguales_y_con_alias_de_la_marca_son_considerados_repetidos(
            self):
        # Dado
        email = ''
        telefono = '48397610'
        nombre_de_marca = 'ford'
        nombre_del_alias_de_la_marca = 'La mas vendida'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono='11' + telefono,
            email=email, nombre_de_marca=nombre_de_marca
        )
        original.obtener_marca().agregar_alias(nombre_del_alias_de_la_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono='011' + telefono, email=email, nombre_de_marca=nombre_del_alias_de_la_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospecto_sin_email_pero_con_telefono_prefijo_y_marca_iguales_son_considerados_repetidos(self):
        # Dado
        email = ''
        prefijo = '02346'
        telefono = '48397610'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, prefijo=prefijo, email=email, nombre_de_marca=nombre_de_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=prefijo, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospectos_con_emails_distintos_y_igual_telefono_y_marca_son_considerados_repetidos(self):
        # Dado
        telefono = '48397610'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email='<EMAIL>', nombre_de_marca=nombre_de_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email='<EMAIL>', nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospectos_con_emails_y_marca_iguales_y_telefonos_distintos_son_considerados_repetidos(self):
        # Dado
        email = '<EMAIL>'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono='15141514', email=email, nombre_de_marca=nombre_de_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono='48397610',  email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospectos_con_mails_y_telefonos_iguales_pero_marca_diferente_no_son_considerados_repetidos(self):
        # Dado
        email = '<EMAIL>'
        telefono = '15141514'
        self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca='ford')
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono,  email=email, nombre_de_marca='fiat')

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[])

    def test_prospectos_con_mails_telefonos_y_marca_iguales_son_considerados_repetidos(self):
        # Dado
        email = '<EMAIL>'
        telefono = '15141514'
        nombre_de_marca = 'ford'
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_original_con_marca_no_son_considerado_repetidos_con_aquellos_de_marca_blanca(self):
        # Dado
        email = '<EMAIL>'
        telefono = '15141514'
        self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca='ford')
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca='')

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[])

    def test_original_con_marca_blanca_no_son_considerado_repetidos_aquellos_de_otras_marcas(self):
        # Dado
        email = '<EMAIL>'
        telefono = '15141514'
        self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca='')
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca='ford')

        # Cuando
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[])

    def test_original_con_marca_blanca_son_considerado_repetidos_aquellos_con_marca_blanca(self):
        # Dado
        email = '<EMAIL>'
        telefono = '15141514'
        marca = Marca.blanca()
        original = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca='')
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca=marca.codigo())

        # Cuando
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[original])

    def test_prospectos_con_categorias_de_campania_diferentes_no_son_considerados_repetidos(self):
        # Dado
        telefono = ''
        email = '<EMAIL>'
        nombre_de_marca = 'ford'
        campania_web = self.creador_de_contexto.crear_campania(nombre='campaña web', categoria=self.fixture['cat_w'])
        categoria_sms = self.fixture['cat_s']
        self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(),
            telefono=telefono, email=email, nombre_de_marca=nombre_de_marca, campania=campania_web)
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()

        # Cuando
        potenciales_repetidos = criterio_de_repetidos.aplicar(
            prefijo=None, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca,
            categoria_de_campania=categoria_sms)

        # Entonces
        self._assert_prospectos_repetidos(potenciales_repetidos, repetidos_esperados=[])

    def _crear_prospecto_uno_asignado_a(self, vendedor, email='', nombre_de_marca=''):
        hace_dias = timezone.now()
        return self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=vendedor, supervisor=vendedor.responsable(), fecha_de_asignacion_a_vendedor=hace_dias,
            fecha=hace_dias, email=email, telefono='48764498', es_telefono_movil=False, nombre_de_marca=nombre_de_marca)

    def _assert_prospectos_repetidos(self, repetidos_obtenidos, repetidos_esperados):
        self.assertEqual(set(repetidos_obtenidos), set(repetidos_esperados))
