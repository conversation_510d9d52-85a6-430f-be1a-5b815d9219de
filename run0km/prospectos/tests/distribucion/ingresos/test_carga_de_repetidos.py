from django.conf import settings
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from prospectos.tests.distribucion.ingresos.control_del_tiempo import ControladorDelTiempo
from prospectos.tests.distribucion.ingresos.test_carga import BaseCargaDeProspectosTest
from prospectos.tests.distribucion.validador import ValidadorDeProspecto
from testing.test_utils import reload_model


@override_settings(GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS=1000)
class CargaDeProspectosRepetidosTest(BaseCargaDeProspectosTest):

    def setUp(self):
        super(CargaDeProspectosRepetidosTest, self).setUp()
        self.vendedor_uno = self.fixture['vend_1']
        self._validador_de_prospecto = ValidadorDeProspecto.new_for(self)

    def test_mail_duplicado_genera_un_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        resultado = self.cargador.cargar_prospecto(
            generales, {'email': '<EMAIL>', 'vendedor': self.vendedor_uno.username()}, {}, 'S')
        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'email': '<EMAIL>'}, {}, 'S')

        # Entonces
        self.assertTrue(resultado_duplicado.fue_agregado_a_grupo_de_repetidos())
        prospecto_original = reload_model(prospecto_original)
        self.assertTrue(prospecto_original.tiene_repetidos())
        self._assert_grupo_de_repetidos_con_original_y_repetido(prospecto_original, resultado_duplicado.prospecto())

    def test_mail_duplicado_con_campania_no_pedida_no_genera_repetido(self):
        # Dado
        resultado = self.cargador.cargar_prospecto(
            {'campania': self.generica_S},
            {'email': '<EMAIL>', 'vendedor': self.vendedor_uno.username()}, {}, self.generica_S.origen)
        prospecto_original = resultado.prospecto()

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo({
            'campania': self.generica_S}, {'email': '<EMAIL>'}, {}, self.generica_S.origen)

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())
        self.assertFalse(resultado_duplicado.fue_agregado_a_grupo_de_repetidos())
        self.assertFalse(resultado_duplicado.prospecto().tiene_repetidos())

    def test_telefono_y_prefijo_duplicados_genera_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        resultado = self.cargador.cargar_prospecto(
            generales, {'prefijo': '011', 'telefono': '335577', 'vendedor': self.vendedor_uno.username()}, {}, 'S')
        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'prefijo': '11', 'telefono': '335577'}, {}, 'S')

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertTrue(prospecto_original.tiene_repetidos())
        self.assertTrue(resultado_duplicado.fue_agregado_a_grupo_de_repetidos())
        self._assert_grupo_de_repetidos_con_original_y_repetido(prospecto_original, resultado_duplicado.prospecto())

    def test_telefono_duplicado_pero_distinto_prefijo_no_genera_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        resultado = self.cargador.cargar_prospecto(
            generales, {'prefijo': '2346', 'telefono': '335577', 'vendedor': self.vendedor_uno.username()}, {}, 'S')

        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'prefijo': '', 'telefono': '335577'}, {}, 'S')

        # Entonces
        self.assertFalse(resultado_duplicado.fue_repetido())
        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())

        prospecto_duplicado = reload_model(resultado_duplicado.prospecto())
        self.assertFalse(prospecto_duplicado.tiene_repetidos())

    def test_email_duplicado_pero_distinto_telefono_genera_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        resultado = self.cargador.cargar_prospecto(
            generales,
            {'email': '<EMAIL>', 'telefono': '48957689', 'vendedor': self.vendedor_uno.username()}, {}, 'S')
        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'email': '<EMAIL>', 'telefono': '98764563'}, {}, 'S')

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertTrue(prospecto_original.tiene_repetidos())
        self.assertTrue(resultado_duplicado.fue_agregado_a_grupo_de_repetidos())
        self._assert_grupo_de_repetidos_con_original_y_repetido(prospecto_original, resultado_duplicado.prospecto())

    def test_telefono_duplicado_pero_distinto_mail_genera_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        resultado = self.cargador.cargar_prospecto(
            generales,
            {'email': '<EMAIL>', 'telefono': '48957689', 'vendedor': self.vendedor_uno.username()}, {}, 'S')
        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'email': '<EMAIL>', 'telefono': '48957689'}, {}, 'S')

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertTrue(prospecto_original.tiene_repetidos())
        self.assertTrue(resultado_duplicado.fue_agregado_a_grupo_de_repetidos())
        self._assert_grupo_de_repetidos_con_original_y_repetido(prospecto_original, resultado_duplicado.prospecto())

    def test_repetido_ingresado_dentro_del_gap_es_mergeado_con_el_original(self):
        # Dado
        generales = {}
        telefono = '48957689'
        with freeze_time(timezone.now()) as tiempo_frizado:
            resultado = self.cargador.cargar_prospecto(
                generales, {'email': '', 'telefono': telefono, 'vendedor': self.vendedor_uno.username(),
                            'campania': self.generica_S.obtener_nombre()}, {},
                codigo_de_tipo_de_origen=self.generica_S.obtener_tipo_de_origen().codigo)
            prospecto_original = resultado.prospecto()
            self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)
            email = '<EMAIL>'
            ip = '0.0.0.0'
            campo_extra = 'un campo extra'

            # Cuando
            ControladorDelTiempo.nuevo().avanzar_tiempo_la_mitad_del_gap(tiempo_frizado)
            resultado_duplicado = self.cargador.cargar_prospecto(
                generales, {'email': email, 'telefono': telefono, 'campania': self.generica_S.obtener_nombre()},
                [{'nombre': 'ip', 'valor': ip}, {'nombre': 'campo_extra', 'valor': campo_extra}],
                codigo_de_tipo_de_origen=self.generica_S.obtener_tipo_de_origen().codigo
            )

        # Entonces
        self.assertTrue(resultado_duplicado.fue_mergeado())
        self.assertEqual(prospecto_original, resultado_duplicado.prospecto())
        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())

        self._validador_de_prospecto.assert_prospecto_con(
            prospecto_original, telefono=telefono, email=email, campania=self.generica_S,
            ip=ip, extras_esperados={'campo_extra': campo_extra})

    def test_repetido_ingresado_no_es_mergeado_cuando_la_categoria_de_campania_es_diferente_al_del_original(self):
        # Dado
        generales = {}
        telefono = '48957689'
        with freeze_time(timezone.now()) as tiempo_frizado:
            resultado = self.cargador.cargar_prospecto(
                generales, {'email': '', 'telefono': telefono, 'vendedor': self.vendedor_uno.username(),
                            'campania': self.generica_S.obtener_nombre()}, {},
                codigo_de_tipo_de_origen=self.generica_S.obtener_tipo_de_origen().codigo)
            prospecto_original = resultado.prospecto()
            self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)
            self._crear_pedido_que_acepte_campania(self.generica_W, self.vendedor_uno)
            email = '<EMAIL>'
            ip = '0.0.0.0'
            campo_extra = 'un campo extra'

            # Cuando
            ControladorDelTiempo.nuevo().avanzar_tiempo_la_mitad_del_gap(tiempo_frizado)
            resultado_duplicado = self.cargador.cargar_prospecto(
                generales, {'email': email, 'telefono': telefono, 'campania': self.generica_W.obtener_nombre()},
                [{'nombre': 'ip', 'valor': ip}, {'nombre': 'campo_extra', 'valor': campo_extra}],
                codigo_de_tipo_de_origen=self.generica_W.obtener_tipo_de_origen().codigo
            )

        # Entonces
        self.assertFalse(resultado_duplicado.fue_mergeado())

        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())
        prospecto_duplicado = reload_model(resultado_duplicado.prospecto())
        self.assertFalse(prospecto_duplicado.tiene_repetidos())

    def test_creacion_con_datos_repetidos_pero_fuera_de_la_fecha_limite_no_crea_prospecto_repetido(self):
        # Dado
        generales = {'campania': self.generica_S}
        hace_8_dias = timezone.now() - timezone.timedelta(days=8)
        resultado = self.cargador.cargar_prospecto(
            generales,
            {'email': '<EMAIL>', 'telefono': '48957689', 'vendedor': self.vendedor_uno.username()}, {}, 'S')
        prospecto_original = resultado.prospecto()
        prospecto_original = self.modificador_de_contexto.modificar_fecha_de_creacion_de_prospecto(
            prospecto_original, hace_8_dias)
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            generales, {'email': '<EMAIL>', 'telefono': '48957689'}, {}, 'S')

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())
        self.assertFalse(resultado_duplicado.fue_repetido())
        prospecto_duplicado = reload_model(resultado_duplicado.prospecto())
        self.assertFalse(prospecto_duplicado.tiene_repetidos())

    def test_con_datos_repetidos_pero_con_categoria_de_campania_diferente_no_crea_prospecto_repetido(self):
        # Dado
        resultado = self.cargador.cargar_prospecto(
            {'campania': self.generica_S},
            {'email': '<EMAIL>', 'telefono': '48957689', 'vendedor': self.vendedor_uno.username()}, {},
            self.generica_S.origen)
        prospecto_original = resultado.prospecto()
        self._crear_pedido_que_acepte(prospecto_original, self.vendedor_uno)
        self._crear_pedido_que_acepte_campania(self.generica_W, self.vendedor_uno)

        # Cuando
        resultado_duplicado = self._cargar_prospecto_luego_de_gap_de_mergeo(
            {'campania': self.generica_W}, {'email': '<EMAIL>', 'telefono': '48957689'}, {},
            self.generica_W.origen)

        # Entonces
        prospecto_original = reload_model(prospecto_original)
        self.assertFalse(prospecto_original.tiene_repetidos())
        self.assertFalse(resultado_duplicado.fue_repetido())
        prospecto_duplicado = reload_model(resultado_duplicado.prospecto())
        self.assertFalse(prospecto_duplicado.tiene_repetidos())

    def _crear_pedido_que_acepte(self, prospecto, vendedor):
        self._crear_pedido_que_acepte_campania(campania=prospecto.campania, vendedor=vendedor)

    def _crear_pedido_que_acepte_campania(self, campania, vendedor):
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            vendedor=vendedor, supervisor=vendedor.responsable(),
            campania=campania, finalizado=False)

    def _cargar_prospecto_luego_de_gap_de_mergeo(self, *args, **kwargs):
        fecha_de_ingreso_post_gap = timezone.now() + timezone.timedelta(
            milliseconds=settings.GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS + 100)
        with freeze_time(fecha_de_ingreso_post_gap):
            return self.cargador.cargar_prospecto(*args, **kwargs)

    def _assert_grupo_de_repetidos_con_original_y_repetido(self, prospecto_original, prospecto_repetido):
        self.assertTrue(
            set(prospecto_original.grupo_de_repetidos().prospectos().all()),
            {prospecto_original, prospecto_repetido}
        )
