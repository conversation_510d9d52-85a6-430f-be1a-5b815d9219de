class NotificadorDeResultadoDeEntregaSimulado:

    def __init__(self):
        super().__init__()
        self._entregas_realizadas = []
        self._entregas_no_realizadas = []

    @classmethod
    def nuevo(cls):
        return cls()

    def entrega_no_realizada(self, vendedor, mensaje):
        self._entregas_no_realizadas.append({'vendedor': vendedor, 'message': mensaje})

    def entrega_realizada(self, vendedor):
        self._entregas_realizadas.append(vendedor)

    def notificaciones_notificaciones_de_entregas_realizadas(self):
        return self._entregas_no_realizadas

    def notificaciones_de_entregas_no_realizadas(self):
        return self._entregas_no_realizadas

    def ha_sido_notificado_de_entrega_no_realizada(self, vendedor, mensaje):
        return {'vendedor': vendedor, 'message': mensaje} in self._entregas_no_realizadas

    def ha_sido_notificado_de_entrega_realizada_a(self, vendedor):
        return vendedor in self._entregas_realizadas

    def cantidad_de_notificaciones_de_entregas_realizadas_a(self, vendedor):
        return self._entregas_realizadas.count(vendedor)