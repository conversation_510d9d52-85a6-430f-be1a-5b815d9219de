# coding=utf-8
from django.utils import timezone

from prospectos.models import FiltroDePedido
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.gestor.constructor_de_prospectos import ConstructorDeProspectos
from testing.base import BaseFixturedTest
from testing.factories import CampoExtraFactory


class CriteriosDeSatisfaccionDePedidosDeProspectosTest(BaseFixturedTest):
    """
        La clase no es de mi autoría, divide el archivo en un paquete.

        Refactor pendiente para mejorar legibilidad de los escenarios de tests
    """

    def setUp(self):
        super(CriteriosDeSatisfaccionDePedidosDeProspectosTest, self).setUp()
        f = self.fixture
        self.campania_de_calidad_mail = f['camp_2']
        self.campania_de_calidad_sms = f['camp_1']
        self.supervisor = f['sup_1']
        self.calidades_sms = [f['tipo_s'], ]
        self.vendedor = f['vend_1']
        self.prospecto_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=f['vend_1'],
                                                                                telefono='4789-9874',
                                                                                nombre='Un Nombre',
                                                                                nombre_de_marca='Ford',
                                                                                provincia='provincia_uno',
                                                                                localidad='localidad_uno')
        self.prospecto_dos = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=f['vend_1'],
                                                                                telefono='5555',
                                                                                nombre='Otro Nombre',
                                                                                nombre_de_marca='Audi',
                                                                                provincia='provincia_dos',
                                                                                localidad='localidad_dos'
                                                                                )
        self.prospecto_tres = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=f['vend_1'],
                                                                                 telefono='5789-9874',
                                                                                 nombre='Un Nombre',
                                                                                 provincia='provincia_tres',
                                                                                 localidad='localidad_tres'
                                                                                 )
        self.admin_de_pedidos = AdministradorDePedidos()
        CampoExtraFactory(prospecto=self.prospecto_uno, nombre='extra', valor='prefijo sufijo')
        CampoExtraFactory(prospecto=self.prospecto_dos, nombre='extra', valor='prefijo sufijo')
        CampoExtraFactory(prospecto=self.prospecto_tres, nombre='extra', valor='cualquier sufijo')

    def test_pedido_con_filtro_por_provincia_y_localidad_aplica_a_prospecto_que_solo_cumple_localidad(self):
        pedido3 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='provincia',
                                                                selector=FiltroDePedido.CONTIENE, valor='KJHGFGH')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='localidad',
                                                                selector=FiltroDePedido.CONTIENE, valor='localidad_uno')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_uno])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_dos,
                                                                                     self.prospecto_tres])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno)],
            pedidos_contenidos=[pedido3])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_no_contenidos=[pedido3])

    def test_pedido_con_filtro_por_provincia_y_localidad_aplica_a_prospecto_que_solo_cumple_provincia(self):
        pedido3 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='provincia',
                                                                selector=FiltroDePedido.CONTIENE, valor='provincia_uno')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='localidad',
                                                                selector=FiltroDePedido.CONTIENE, valor='KHGHBJUEW')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_uno])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_dos,
                                                                                     self.prospecto_tres])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno)],
            pedidos_contenidos=[pedido3])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_no_contenidos=[pedido3])

    def test_con_filtro_inclusion_por_telefono_no_aplica_a_prospecto_si_tiene_calidad_distinta(self):
        p4 = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor,
                                                                telefono='6789-9874',
                                                                nombre='Cualquier Nombre',
                                                                campania=self.campania_de_calidad_mail)
        CampoExtraFactory(prospecto=p4, nombre='extra', valor='prefijo sufijo')
        pedido3 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='telefono',
                                                                selector=FiltroDePedido.CONTIENE, valor='89')
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido3, prospectos=[p4])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(p4)],
            pedidos_no_contenidos=[pedido3])

    def test_con_filtro_exclusion_por_campo_extra_no_aplica_a_prospecto_si_tiene_calidad_distinta(self):
        p4 = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor,
                                                                telefono='6789-9874',
                                                                nombre='Cualquier Nombre',
                                                                campania=self.campania_de_calidad_mail)
        CampoExtraFactory(prospecto=p4, nombre='extra', valor='prefijo sufijo')
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_exclusion_para(pedido=pedido1, campo='extra',
                                                                selector=FiltroDePedido.PREFIJO, valor='89')
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[p4])

        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(p4)],
            pedidos_no_contenidos=[pedido1])

    def test_con_filtro_inclusion_por_nombre_no_aplica_a_prospecto_si_tiene_calidad_distinta(self):
        p4 = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor,
                                                                telefono='6789-9874',
                                                                nombre='Cualquier Nombre',
                                                                campania=self.campania_de_calidad_mail)

        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido1, campo='nombre',
                                                                selector=FiltroDePedido.CONTIENE, valor='Cualquier')
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[p4])

        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(p4)],
            pedidos_no_contenidos=[pedido1])

    def test_con_filtro_inclusion_por_nombre_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido1, campo='nombre',
                                                                selector=FiltroDePedido.CONTIENE, valor='un n')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_uno,
                                                                                  self.prospecto_tres])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_dos])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_contenidos=[pedido1])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos)],
            pedidos_no_contenidos=[pedido1])

    def test_con_filtro_inclusion_por_localidad_desde_ip_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        f = self.fixture
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido1, campo='localidad_desde_ip',
                                                                selector=FiltroDePedido.CONTIENE, valor='loc_ip')
        prospecto_exitoso = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=f['vend_1'],
                                                                               telefono='6789-9874',
                                                                               nombre='Un Nombre',
                                                                               nombre_de_marca='Ford')
        constructor = ConstructorDeProspectos.nuevo()
        constructor.completar_datos_geolocalizacion_desde_ip(
            prospecto=prospecto_exitoso, provincia='prov_ip', localidad='loc_ip', latitud=34.0, longitud=58.0)
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido1, prospectos=[prospecto_exitoso])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_dos,
                                                                                     self.prospecto_uno,
                                                                                     self.prospecto_tres
                                                                                     ])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(prospecto_exitoso)],
            pedidos_contenidos=[pedido1])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)
                             ],
            pedidos_no_contenidos=[pedido1])

    def test_con_filtro_exclusion_por_campo_extra_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_exclusion_para(pedido=pedido1, campo='extra',
                                                                selector=FiltroDePedido.PREFIJO, valor='cualquier')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_uno,
                                                                                  self.prospecto_dos])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_tres])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos)],
            pedidos_contenidos=[pedido1])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_no_contenidos=[pedido1])

    def test_con_filtro_inclusion_por_campo_extra_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        pedido2 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido2, campo='extra',
                                                                selector=FiltroDePedido.PREFIJO, valor='cualquier')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido2, prospectos=[self.prospecto_tres])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido2, prospectos=[self.prospecto_uno,
                                                                                     self.prospecto_dos])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres), ],
            pedidos_contenidos=[pedido2])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos)],
            pedidos_no_contenidos=[pedido2])

    def test_con_filtro_inclusion_por_telefono_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        pedido3 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido3, campo='telefono',
                                                                selector=FiltroDePedido.CONTIENE, valor='89')
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_uno,
                                                                                  self.prospecto_tres])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido3, prospectos=[self.prospecto_dos])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_contenidos=[pedido3])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos)],
            pedidos_no_contenidos=[pedido3])

    def test_sin_filtro_filtra_por_inclusion_de_campania_y_aplica_a_prospectos_que_satisfacen_la_campania(self):
        p4 = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor,
                                                                telefono='6789-9874',
                                                                nombre='Cualquier Nombre',
                                                                campania=self.campania_de_calidad_mail)
        pedido4 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido4,
                                                      prospectos=[self.prospecto_uno, self.prospecto_dos,
                                                                  self.prospecto_tres])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido4, prospectos=[p4])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_contenidos=[pedido4])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(p4)],
            pedidos_no_contenidos=[pedido4])

    def test_si_no_tiene_credito_a_consumir_no_aplica_a_nadie(self):
        pedido5 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=14,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido5,
                                                         prospectos=[self.prospecto_uno, self.prospecto_dos,
                                                                     self.prospecto_tres])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres), ],
            pedidos_no_contenidos=[pedido5])

    def test_con_filtros_inclusion_varias_marcas_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        """
            Los filtros pedidos deben tener logica AND, salvo cuando son de inclusion y tienen el mismo campo.
            En ese caso, ejecutan una logica OR entre si.
        """
        # se crean prospectos con marca blanca en vez de ford.
        pedido6 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido6, campo='marca',
                                                                selector=FiltroDePedido.CONTIENE, valor='Ford')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido=pedido6, campo='marca',
                                                                selector=FiltroDePedido.CONTIENE, valor='Audi')

        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido6,
                                                      prospectos=[self.prospecto_uno, self.prospecto_dos])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido6, prospectos=[self.prospecto_tres])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos)],
            pedidos_contenidos=[pedido6])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_no_contenidos=[pedido6])

    def test_con_filtros_inclusion_por_modelo_aplica_solo_a_prospectos_que_satisfacen_los_modelos(self):
        # se crean prospectos con marca blanca en vez de ford.
        prospecto_ford_focus = self._crear_prospecto_ford_focus()
        prospecto_ford_fiesta = self._crear_prospecto_ford_fiesta()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                   credito=10,
                                                                                   yapa=5, consumido=2,
                                                                                   fecha=timezone.now().date(),
                                                                                   calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='marca', selector=FiltroDePedido.CONTIENE, valor='Ford')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='modelo', selector=FiltroDePedido.CONTIENE, valor='focus')

        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido,
                                                      prospectos=[prospecto_ford_focus])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido, prospectos=[
            self.prospecto_uno, self.prospecto_dos, self.prospecto_tres, prospecto_ford_fiesta])
        self._assert_pedidos_contenidos_en(
            pedidos_totales=[
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(prospecto_ford_focus)],
            pedidos_contenidos=[pedido])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(prospecto_ford_fiesta)],
            pedidos_no_contenidos=[pedido])

    def test_con_filtros_exclusion_varios_telefonos_aplica_solo_a_prospectos_que_satisfacen_los_filtros(self):
        """
            Los filtros pedidos deben tener logica AND, salvo cuando son de exclusion y tienen el mismo campo.
            En ese caso, ejecutan una logica OR entre si.
        """
        pedido6 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_exclusion_para(pedido=pedido6, campo='telefono',
                                                                selector=FiltroDePedido.CONTIENE, valor='89')
        self.creador_de_contexto.crear_filtro_de_exclusion_para(pedido=pedido6, campo='telefono',
                                                                selector=FiltroDePedido.CONTIENE, valor='5')
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido6, prospectos=[self.prospecto_uno,
                                                                                     self.prospecto_dos,
                                                                                     self.prospecto_tres])
        self._assert_pedidos_no_contenidos_en(
            pedidos_totales=[self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_uno),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_dos),
                             self.admin_de_pedidos._obtener_pedidos_que_aplican(self.prospecto_tres)],
            pedidos_no_contenidos=[pedido6])

    def test_propecto_puede_satisfacer_pedido_con_multiples_calidades(self):
        origen_ok = self.campania_de_calidad_sms.categoria.tipo_de_origen
        origen_nok = self.campania_de_calidad_mail.categoria.tipo_de_origen
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=[origen_ok, origen_nok])
        self._assert_prospectos_que_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_uno])
        pedido1.cambiar_calidades_por(calidades=[origen_nok, ])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido=pedido1, prospectos=[self.prospecto_uno])

    def test_no_aplican_pedidos_de_supervisores_deshabilitados(self):
        pedido1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        pedido2 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor,
                                                                                    credito=10,
                                                                                    yapa=5, consumido=2,
                                                                                    fecha=timezone.now().date(),
                                                                                    calidades=self.calidades_sms)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor,
                                                                       telefono='111',
                                                                       campania=self.campania_de_calidad_sms)
        pedidos = self.admin_de_pedidos._obtener_pedidos_que_aplican(prospecto)
        self._assert_pedidos_contenidos_en(pedidos_totales=[pedidos], pedidos_contenidos=[pedido1, pedido2])
        self.supervisor.deshabilitar()
        pedidos = self.admin_de_pedidos._obtener_pedidos_que_aplican(prospecto)
        self._assert_pedidos_no_contenidos_en(pedidos_totales=[pedidos], pedidos_no_contenidos=[pedido1, pedido2])

    def test_prospecto_con_marca_sin_modelo_no_satisface_pedido_con_filtro_por_modelo(self):
        prospecto_sin_modelo = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor, telefono='123456789', nombre='Prospecto Sin Modelo', nombre_de_marca='Ford')

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, credito=10, yapa=5, consumido=2, fecha=timezone.now().date(), calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='modelo', selector=FiltroDePedido.CONTIENE, valor='Focus')

        self._assert_prospectos_que_no_satisfacen_pedido(pedido, prospectos=[prospecto_sin_modelo])

    def test_pedido_(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, 'provincia', FiltroDePedido.CONTIENE, 'capital federal')

        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor, supervisor=self.supervisor, campania=self.campania_de_calidad_sms,
            provincia='Chubut', nombre_de_marca='Renault')
        self._assert_prospectos_que_no_satisfacen_pedido(pedido, prospectos=[prospecto])

    def test_prospecto_con_marca_ford_excluyendo_modelo_focus_deberia_satisfacer_pedido(self):
        prospecto_ford_focus = self._crear_prospecto_ford_focus()
        prospecto_ford_fiesta = self._crear_prospecto_ford_fiesta()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, credito=10, yapa=5, consumido=2, fecha=timezone.now().date(),
            calidades=self.calidades_sms)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='marca', selector=FiltroDePedido.CONTIENE, valor='Ford')
        self.creador_de_contexto.crear_filtro_de_exclusion_para(
            pedido, campo='modelo', selector=FiltroDePedido.CONTIENE, valor='Focus')

        # Verificar que el prospecto satisface el pedido
        self._assert_prospectos_que_satisfacen_pedido(pedido, prospectos=[self.prospecto_uno, prospecto_ford_fiesta])
        self._assert_prospectos_que_no_satisfacen_pedido(pedido, prospectos=[prospecto_ford_focus])

    def _assert_pedidos_contenidos_en(self, pedidos_totales, pedidos_contenidos):
        for pedido_contenido in pedidos_contenidos:
            for pedidos in pedidos_totales:
                self.assertIn(pedido_contenido, pedidos)

    def _assert_pedidos_no_contenidos_en(self, pedidos_totales, pedidos_no_contenidos):
        for pedido_no_contenido in pedidos_no_contenidos:
            for pedidos in pedidos_totales:
                self.assertNotIn(pedido_no_contenido, pedidos)

    def _assert_prospectos_que_satisfacen_pedido(self, pedido, prospectos):
        for prospecto in prospectos:
            self.assertTrue(
                self.admin_de_pedidos._prospecto_puede_satisfacer_pedido(pedido=pedido, prospecto=prospecto),
                msg='Prospecto %s no satisface al pedido %s' % (prospecto, pedido)
            )

    def _assert_prospectos_que_no_satisfacen_pedido(self, pedido, prospectos):
        for prospecto in prospectos:
            self.assertFalse(self.admin_de_pedidos._prospecto_puede_satisfacer_pedido(pedido=pedido,
                                                                                      prospecto=prospecto))

    def _crear_prospecto_ford_fiesta(self):
        return self._crear_prospecto_ford(telefono='98784161', nombre='Mi ford fiesta', nombre_de_modelo='Fiesta')

    def _crear_prospecto_ford_focus(self):
        return self._crear_prospecto_ford(telefono='152498451', nombre='Mi ford focus', nombre_de_modelo='Focus')

    def _crear_prospecto_ford(self, nombre, telefono, nombre_de_modelo):
        ford = self._ford()
        prospectos = self.creador_de_contexto.crear_prospectos_nuevos(
            cantidad=1,
            telefono=telefono,
            nombre=nombre,
            marca=ford.codigo())

        prospecto = prospectos[0]
        modelo = self.creador_de_contexto.crear_modelo(nombre_de_modelo, ford)
        prospecto.reemplazar_modelos([modelo])
        return prospecto

    def _ford(self):
        return self.creador_de_contexto.crear_marca("Ford")

    #TODO Crear un test que tenga un prospecto con marca y no tiene modelo, y un pedido que filtre por modelo no tiene que entrar