from prospectos.models import FiltroDePedido
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from testing.creador_de_contexto import CreadorDeContexto
from testing.base import BaseFixturedTest


class ExclusionEInclusionDeCampaniaTest(BaseFixturedTest):
    def setUp(self):
        super(ExclusionEInclusionDeCampaniaTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        self.campania_dos = self.fixture['camp_2']
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.supervisor, fixture=self.fixture)

    def test_pedido_que_excluye_campania_no_deberia_ser_seleccionado_para_dicha_campania(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=15000, yapa=0,
                                                                          campania=self.campania_uno,
                                                                          excluye_campanias=True)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_que_incluye_campania_deberia_ser_seleccionado_para_dicha_campania(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=15000, yapa=0,
                                                                          campania=self.campania_uno,
                                                                          excluye_campanias=False)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)

    def test_pedido_que_incluye_campania_no_deberia_ser_seleccionado_para_otra_campania(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=15000, yapa=0,
                                                                          campania=self.campania_dos,
                                                                          excluye_campanias=False)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)


class SeleccionPorMarcaYCampaniaTest(BaseFixturedTest):

    def setUp(self):
        super(SeleccionPorMarcaYCampaniaTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        self.campania_dos = self.fixture['camp_2']
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.supervisor, fixture=self.fixture)

    def test_pedido_sin_credito_no_deberia_ser_seleccionado(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=0, yapa=0)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_sin_el_origen_de_la_campania_no_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=5000)
        pedido_con_creditos.cambiar_calidades_por(calidades=[])
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_categorias_diferente_de_las_de_la_campania_no_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=5000,
                                                                                                campania=self.campania_uno)
        pedido_con_creditos.categorias.clear()
        pedido_con_creditos.categorias.add(self.fixture['cat_m'])
        pedido_con_creditos.save()
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_para_otra_compania_no_deberia_ser_seleccionado(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=5000,
                                                                          campania=self.campania_uno)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_dos)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_para_campania_con_credito_deberia_ser_seleccionado(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=5000,
                                                                          campania=self.campania_uno)
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)

    def test_pedido_sin_categorias_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        pedido_con_creditos.categorias.clear()
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)

    def test_pedido_sin_campanias_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        pedido_con_creditos.campanias.clear()
        pedido_con_creditos.save()

        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)

    def test_pedido_con_filtro_de_inclusion_no_deberia_ser_tomados_si_no_satisfacen_filtros(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido_con_creditos,
                                            campo='modelo',
                                            selector=FiltroDePedido.PREFIJO,
                                            valor='fiesta')
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(nombre_de_marca='Peugeot',
                                                                                  campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_con_filtro_que_excluye_marca_no_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        FiltroDePedido.nuevo_para_exclusion(pedido=pedido_con_creditos,
                                            campo='marca',
                                            selector=FiltroDePedido.PREFIJO,
                                            valor='peugeot')
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(nombre_de_marca='Peugeot',
                                                                                  campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_con_filtro_que_excluye_marca_y_incluye_modelo_no_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        FiltroDePedido.nuevo_para_exclusion(pedido=pedido_con_creditos,
                                            campo='marca',
                                            selector=FiltroDePedido.PREFIJO,
                                            valor='peugeot')
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido_con_creditos,
                                            campo='modelo',
                                            selector=FiltroDePedido.PREFIJO,
                                            valor='308')
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(nombre_de_marca='Peugeot',
                                                                                  campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 0)

    def test_pedido_con_filtro_que_incluye_marca_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido_con_creditos,
                                            campo='marca',
                                            selector=FiltroDePedido.PREFIJO,
                                            valor='peugeot')
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(nombre_de_marca='Peugeot',
                                                                                  campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)

    def test_pedido_con_filtro_que_incluye_dos_marcas_deberia_ser_seleccionado(self):
        pedido_con_creditos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            credito=5000,
            campania=self.campania_uno)
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido_con_creditos,
                                            campo='marca',
                                            selector=FiltroDePedido.CONTIENE,
                                            valor='vw')
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido_con_creditos,
                                            campo='marca',
                                            selector=FiltroDePedido.CONTIENE,
                                            valor='volkswagen')
        pedidos_seleccionados = AdministradorDePedidos().pedidos_que_aplican_para(
            nombre_de_marca='volkswagen', campania=self.campania_uno)
        self.assertEqual(len(pedidos_seleccionados), 1)
        self.assertEqual(pedidos_seleccionados[0], pedido_con_creditos)