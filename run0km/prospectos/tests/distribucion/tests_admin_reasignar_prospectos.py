# coding=utf-8
from prospectos.models import Prospecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseLoggedAdminTest
from testing.factories import ProspectosFactory


class AdminReasignarTest(BaseLoggedAdminTest):

    def setUp(self):
        super(AdminReasignarTest, self).setUp()
        self.asignar_url = '/admin/prospectos/prospecto/'
        self.vendedor = self.fixture['vend_1']
        self.supervisor = self.fixture['sup_1']
        self.supervisor_dos = self.fixture['sup_2']
        self.p1 = ProspectosFactory(campania=self.fixture['camp_1'], responsable=self.supervisor)
        self.p2 = ProspectosFactory(campania=self.fixture['camp_1'], responsable=self.supervisor,
                                    vendedor=self.vendedor, nombre='p2')
        self.p3 = ProspectosFactory(campania=self.fixture['camp_1'], responsable=self.supervisor_dos)
        self.pids = [self.p1.id, self.p2.id, self.p3.id]

    def test_erroneos(self):

        # No se selecciono Responsable, y el vendedor tiene Supervisor
        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable='',
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response,
                            'Debe indicar un vendedor que este a cargo del Supervisor Responsable seleccionado.')
        a_vend_1 = Prospecto.objects.filter(id__in=self.pids, vendedor=self.vendedor)
        self.assertEqual(1, a_vend_1.count())

        # Se selecciono Responsable distinto al Supervisor del Vendedor
        data = dict(action='reasignar_prospectos', _selected_action=self.pids,apply='Reasignar',
                    responsable=self.supervisor_dos.id,
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response,
                            'Debe indicar un vendedor que este a cargo del Supervisor Responsable seleccionado.')
        a_vend_1 = Prospecto.objects.filter(id__in=self.pids, vendedor=self.vendedor)
        self.assertEqual(1, a_vend_1.count())

    def test_asigna_solo_responsable(self):
        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable=self.supervisor.id,
                    vendedor='')
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertRedirects(response, self.asignar_url, status_code=302, target_status_code=200, msg_prefix='')
        self.assertContains(response, 'Se han puesto 3 prospectos a cargo del Supervisor %s.' % self.supervisor)
        self.assertContains(response, 'Se han puesto 3 prospectos sin Vendedor a cargo.')
        reasignados = Prospecto.objects.filter(id__in=self.pids, responsable=self.supervisor,
                                               vendedor__isnull=True)
        self.assertEqual(len(self.pids), reasignados.count())

    def test_asigna_responsable_y_vendedor(self):
        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable=self.supervisor.id,
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertRedirects(response, self.asignar_url, status_code=302, target_status_code=200, msg_prefix='')
        self.assertContains(response, 'Se han puesto 3 prospectos a cargo del Supervisor %s.' % self.supervisor)
        self.assertContains(response, 'Se reasignaron 3 prospectos al Vendedor %s.' % self.vendedor)
        reasignados = Prospecto.objects.filter(id__in=self.pids,
                                               responsable=self.supervisor,
                                               vendedor=self.vendedor.id)
        self.assertEqual(len(self.pids), reasignados.count())

    def test_reasignar_prospectos_luego_de_haber_archivado_elimina_archivado_asociado_al_prospecto(self):
        gestor = GestorDeProspecto.nuevo_para(self.supervisor)
        prospectos = Prospecto.objects.filter(nombre='p2', vendedor=self.vendedor)
        gestor.archivar_prospectos(prospectos=prospectos)
        prospecto = Prospecto.objects.get(nombre='p2')
        self.assertTrue(prospecto.esta_archivado())
        data = dict(action='reasignar_prospectos', _selected_action=[self.p2.id], apply='Reasignar',
                    responsable=self.supervisor.id,
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertRedirects(response, self.asignar_url, status_code=302, target_status_code=200, msg_prefix='')
        reasignados = Prospecto.objects.filter(id__in=self.pids,
                                               responsable=self.supervisor,
                                               vendedor=self.vendedor.id)
        self.assertEqual(1, reasignados.count())
        prospecto = Prospecto.objects.get(nombre='p2')
        self.assertFalse(prospecto.esta_archivado())

    def test_asigna_sin_vendedor_ni_responsable(self):
        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable='',
                    vendedor='')
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertRedirects(response, self.asignar_url, status_code=302, target_status_code=200, msg_prefix='')
        self.assertContains(response, 'Se han puesto 3 prospectos sin Supervisor Ni Vendedor a cargo.')
        reasignados = Prospecto.objects.filter(id__in=self.pids, responsable__isnull=True, vendedor__isnull=True)
        self.assertEqual(len(self.pids), reasignados.count())

    def test_no_asigna_a_vendedores_o_supervisores_deshabilitados(self):
        data = dict(action='reasignar_prospectos', _selected_action=self.pids)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.supervisor.user.get_full_name())
        self.assertContains(response, self.vendedor.user.get_full_name())

        self.vendedor.deshabilitar()
        self.supervisor.deshabilitar()
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertNotContains(response, self.vendedor.user.get_full_name())
        self.assertNotContains(response, self.supervisor.user.get_full_name())

        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable=self.supervisor.id,
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertContains(response,
                            'Seleccione una opción válida. La opción seleccionada no es una de las disponibles.')
        self.assertEqual(response.status_code, 200)

        self.vendedor.habilitar()
        self.supervisor.habilitar()
        data = dict(action='reasignar_prospectos', _selected_action=self.pids, apply='Reasignar',
                    responsable=self.supervisor.id,
                    vendedor=self.vendedor.id)
        response = self.client.post(self.asignar_url, data, follow=True)
        self.assertRedirects(response, self.asignar_url, status_code=302, target_status_code=200, msg_prefix='')
        reasignados = Prospecto.objects.filter(id__in=self.pids, responsable=self.supervisor, vendedor=self.vendedor)
        self.assertEqual(len(self.pids), reasignados.count())

