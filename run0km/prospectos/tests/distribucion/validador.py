from core.tests.validators.base import Validator
from prospectos.models import Prospecto
from testing.test_utils import reload_model


class ValidadorDeProspecto(Validator):
    def assert_prospecto_con(self, prospecto, telefono='', email='', ip='', campania=None, extras_esperados=None):
        extras_esperados = extras_esperados or {}
        self.assertEqual(prospecto.obtener_telefono(), telefono)
        self.assertEqual(prospecto.obtener_email(), email)
        self.assertEqual(prospecto.obtener_ip(), ip)
        self.assertEqual(prospecto.obtener_campania(), campania or prospecto.obtener_campania())
        self.assert_tiene_campos_extras(prospecto, extras_esperados)

    def assert_tiene_campos_extras(self, prospecto, extras_esperados):
        campos_extra = prospecto.campos_extra.all()
        for nombre, valor in list(extras_esperados.items()):
            tiene_campo_extra = any([campo.nombre == nombre and campo.valor == valor for campo in campos_extra])
            self.assertTrue(
                tiene_campo_extra, msg='No existe el campo extra %s:%s en %s' % (nombre, valor, campos_extra))

    def assert_no_fue_registrado_consumo_para(self, pedido, prospectos):
        pedido_recargado = reload_model(pedido)
        prospectos_queryset = Prospecto.objects.con_ids([p.pk for p in prospectos])
        ids = prospectos_queryset.ids()
        self.assertFalse(pedido_recargado.prospectos_asignados.con_ids(ids).exists())
        self.assertEqual(pedido_recargado.consumido, 0)

    def assert_fue_registrado_consumo_para(self, pedido, prospectos):
        pedido_recargado = reload_model(pedido)
        self.assertEqual(set(prospectos), set(pedido_recargado.prospectos_asignados.all()))
        consumo = self._consumo_de(prospectos)
        self.assertEqual(pedido_recargado.consumido, consumo)

    def _consumo_de(self, prospectos):
        prospectos_queryset = Prospecto.objects.con_ids([p.pk for p in prospectos])
        consumo = prospectos_queryset.valor_total()
        return consumo
