# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
from django.test import TestCase
from django.utils.timezone import now

from testing.factories import ProspectosFactory, CampaniasFactory, CategoriasDeCampaniaFactory, UsersFactory, \
    VendedoresFactory, ConcesionariasFactory
from campanias.models import TipoDeOrigen
from prospectos.models import Prospecto
from prospectos.utils.asignacion_de_prospectos import GeneradorDeOrdenesDeProspecto


class Request(object):
    GET = {'filter_estado': ''}

    def __init__(self, estado='N'):
        self.GET['filter_estado'] = estado


class OrdenamientoPorCriterioDeDesignacionTest(TestCase):
    def setUp(self):
        super(OrdenamientoPorCriterioDeDesignacionTest, self).setUp()
        concesionaria = ConcesionariasFactory(nombre='conce_4', dia_inicio_periodos=28, dia_fin_periodos=20)
        usr_1 = UsersFactory(username='sup1', first_name='sup1', last_name='sup1', password='pw', email='<EMAIL>')
        sup_1 = VendedoresFactory(user=usr_1, cargo='Supervisor', alerta_diaria=False, alerta_a_supervisor=False,
                                  concesionaria=concesionaria)

        tipo_s, created = TipoDeOrigen.objects.get_or_create(nombre='SMS', codigo='S')
        tipo_w, created = TipoDeOrigen.objects.get_or_create(nombre='Web', codigo='W')
        cat_s = CategoriasDeCampaniaFactory(tipo_de_origen=tipo_s, valor='1')
        cat_w = CategoriasDeCampaniaFactory(tipo_de_origen=tipo_w, valor='1')
        self.c1 = CampaniasFactory(nombre='Campania 1', categoria=cat_s)
        self.c2 = CampaniasFactory(nombre='Campania 2', categoria=cat_s)
        self.c3 = CampaniasFactory(nombre='Campania 3', categoria=cat_s)
        self.c4 = CampaniasFactory(nombre='Campania 4', categoria=cat_w)

        self.p1_1 = crear_prospecto(campania=self.c1, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-7),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p1_2 = crear_prospecto(campania=self.c1, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-6),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p1_3 = crear_prospecto(campania=self.c1, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p1_4 = ProspectosFactory(campania=self.c1)

        self.p2_1 = crear_prospecto(campania=self.c2, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-10),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p2_2 = crear_prospecto(campania=self.c2, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-4),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p2_3 = crear_prospecto(campania=self.c2, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-3),
                                    vendedor=sup_1, concesionaria=concesionaria)

        self.p3_1 = crear_prospecto(campania=self.c3, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-9),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p3_2 = crear_prospecto(campania=self.c3, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-8),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p3_3 = crear_prospecto(campania=self.c3, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-2),
                                    vendedor=sup_1, concesionaria=concesionaria)

        self.p4_1 = crear_prospecto(campania=self.c4, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-13),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p4_2 = crear_prospecto(campania=self.c4, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-12),
                                    vendedor=sup_1, concesionaria=concesionaria)
        self.p4_3 = crear_prospecto(campania=self.c4, fecha_de_asignacion_a_vendedor=now() + timedelta(days=-11),
                                    vendedor=sup_1, concesionaria=concesionaria)

    def test_ordenados_por_cualquier_campania(self):
        request = Request('A')
        prospectos = Prospecto.objects.filter(campania__categoria__tipo_de_origen__codigo='S')
        generador = GeneradorDeOrdenesDeProspecto()
        ids = generador.ids_ordenadas_por_criterio_de_desasignacion(prospectos, request)
        segmento = prospectos.filter(id__in=ids[:4])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=segmento[0].campania).count(), 4)
        segmento = prospectos.filter(id__in=ids[4:7])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=segmento[0].campania).count(), 3)
        segmento = prospectos.filter(id__in=ids[7:10])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=segmento[0].campania).count(), 3)

    def test_ordenar_por_campania_mas_vieja_y_fecha_de_asignacion_a_vendedor(self):
        request = Request()
        prospectos = Prospecto.objects.filter(campania__categoria__tipo_de_origen__codigo='S')
        generador = GeneradorDeOrdenesDeProspecto()
        ids = generador.ids_ordenadas_por_criterio_de_desasignacion(prospectos, request)
        segmento = prospectos.filter(id__in=ids[:3])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=self.c2).count(), 3)
        segmento = prospectos.filter(id__in=ids[3:6])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=self.c3).count(), 3)
        segmento = prospectos.filter(id__in=ids[6:9])
        self.assertTrue(segmento[0].fecha_de_asignacion_a_vendedor() < segmento[1].fecha_de_asignacion_a_vendedor())
        self.assertTrue(segmento[1].fecha_de_asignacion_a_vendedor() < segmento[2].fecha_de_asignacion_a_vendedor())
        self.assertEqual(segmento.filter(campania=self.c1).count(), 3)

    def test_ordenar_por_campania_mas_vieja_todos(self):
        request = Request()
        prospectos = Prospecto.objects.all()
        generador = GeneradorDeOrdenesDeProspecto()
        ids = generador.ids_ordenadas_por_criterio_de_desasignacion(prospectos, request)
        segmento = prospectos.filter(id__in=ids[:3])
        self.assertEqual(segmento.filter(campania=self.c4).count(), 3)
        segmento = prospectos.filter(id__in=ids[3:6])
        self.assertEqual(segmento.filter(campania=self.c2).count(), 3)
        segmento = prospectos.filter(id__in=ids[6:9])
        self.assertEqual(segmento.filter(campania=self.c3).count(), 3)
        segmento = prospectos.filter(id__in=ids[9:12])
        self.assertEqual(segmento.filter(campania=self.c1).count(), 3)

    def test_ordenar_para_desasignar_uniformemente(self):
        concesionaria = ConcesionariasFactory(nombre='conce_9', dia_inicio_periodos=28, dia_fin_periodos=20)
        usr_X = UsersFactory(username='supX', first_name='sup1', last_name='sup1', password='pw', email='<EMAIL>')
        sup_X = VendedoresFactory(user=usr_X, cargo='Supervisor', alerta_diaria=False, alerta_a_supervisor=False,
                                  concesionaria=concesionaria)

        u1 = UsersFactory(username='vend1')
        VendedoresFactory(user=u1, supervisor=sup_X)
        u2 = UsersFactory(username='vend2')
        VendedoresFactory(user=u2, supervisor=sup_X)
        u3 = UsersFactory(username='vend3')
        VendedoresFactory(user=u3, supervisor=sup_X)

        self.p1_4 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=1),
                                    vendedor=u1.vendedor, concesionaria=concesionaria)
        self.p1_5 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=2),
                                    vendedor=u1.vendedor, concesionaria=concesionaria)
        self.p1_6 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=3),
                                    vendedor=u1.vendedor, concesionaria=concesionaria)
        self.p1_7 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=4),
                                    vendedor=u2.vendedor, concesionaria=concesionaria)
        self.p1_8 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=5),
                                    vendedor=u2.vendedor, concesionaria=concesionaria)
        self.p1_9 = crear_prospecto(campania=self.c2,
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=6),
                                    vendedor=u2.vendedor, concesionaria=concesionaria)
        self.p1_10 = crear_prospecto(campania=self.c2,
                                     fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=7),
                                     vendedor=u3.vendedor, concesionaria=concesionaria)
        self.p1_11 = crear_prospecto(campania=self.c2,
                                     fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=8),
                                     vendedor=u3.vendedor, concesionaria=concesionaria)
        self.p1_12 = crear_prospecto(campania=self.c2,
                                     fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5, minutes=9),
                                     vendedor=u3.vendedor, concesionaria=concesionaria)

        request = Request(estado='P')
        request.GET['cantidad'] = '15'
        prospectos = Prospecto.objects.all()
        generador = GeneradorDeOrdenesDeProspecto()
        ids = generador.ids_ordenadas_por_criterio_de_desasignacion(prospectos, request)
        # Los primeros 4 prospectos son de la campaña 1
        # Los siguientes 12, de la 2. Y se ordenan intercalando los vendedores
        i = 3
        self.assertFalse(Prospecto.objects.get(id=ids[i + 1]).vendedor == Prospecto.objects.get(id=ids[i + 2]).vendedor)
        self.assertFalse(Prospecto.objects.get(id=ids[i + 1]).vendedor == Prospecto.objects.get(id=ids[i + 3]).vendedor)
        self.assertFalse(Prospecto.objects.get(id=ids[i + 1]).vendedor == Prospecto.objects.get(id=ids[i + 4]).vendedor)
        self.assertFalse(Prospecto.objects.get(id=ids[i + 2]).vendedor == Prospecto.objects.get(id=ids[i + 3]).vendedor)
        self.assertFalse(Prospecto.objects.get(id=ids[i + 2]).vendedor == Prospecto.objects.get(id=ids[i + 4]).vendedor)
        self.assertFalse(Prospecto.objects.get(id=ids[i + 3]).vendedor == Prospecto.objects.get(id=ids[i + 4]).vendedor)

        self.assertEqual(Prospecto.objects.get(id=ids[i + 1]).vendedor, Prospecto.objects.get(id=ids[i + 5]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 2]).vendedor, Prospecto.objects.get(id=ids[i + 6]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 3]).vendedor, Prospecto.objects.get(id=ids[i + 7]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 4]).vendedor, Prospecto.objects.get(id=ids[i + 8]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 5]).vendedor, Prospecto.objects.get(id=ids[i + 9]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 6]).vendedor, Prospecto.objects.get(id=ids[i + 10]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 7]).vendedor, Prospecto.objects.get(id=ids[i + 11]).vendedor)
        self.assertEqual(Prospecto.objects.get(id=ids[i + 8]).vendedor, Prospecto.objects.get(id=ids[i + 12]).vendedor)


def crear_prospecto(campania, concesionaria, fecha_de_asignacion_a_vendedor=now(), vendedor=None):
    prospecto = ProspectosFactory(vendedor=vendedor, campania=campania,
                                  fecha_de_asignacion_a_vendedor=fecha_de_asignacion_a_vendedor,
                                  concesionaria=concesionaria)
    return prospecto