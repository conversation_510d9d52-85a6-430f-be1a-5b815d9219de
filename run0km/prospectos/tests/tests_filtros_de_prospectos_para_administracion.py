from datetime import timedelta, datetime

from django.conf import settings
from django.http.request import QueryDict
from django.test import override_settings
from django.utils import timezone
from django.utils.timezone import make_aware, get_current_timezone

from prospectos.models import Prospecto, Marca
from prospectos.utils.filtros import FiltroAdministracion
from testing.base import BaseLoggedSupervisorTest


class FiltroAdministracionTest(BaseLoggedSupervisorTest):
    def _crear_marca(self, nombre):
        return Marca.obtener_or_crear_con_nombre(nombre)

    def _crear_y_asignar_marca(self, prospecto, nombre_de_marca):
        marca = self._crear_marca(nombre_de_marca)
        prospecto.cambiar_marca_por(marca=marca)
        return marca

    def _configurar_provincia_a(self, prospecto, nombre):
        prospecto.provincia = nombre
        prospecto.save()

    def _configurar_prefijo_a(self, prospecto_uno, prefijo):
        prospecto_uno.prefijo = prefijo
        prospecto_uno.save()

    def test_filtrar_provincias(self):
        prospectos = Prospecto.objects.all()
        prospecto_uno = self.fixture['p_1']
        self._configurar_provincia_a(prospecto_uno, nombre='Provincia 1')
        prospecto_dos = self.fixture['p_2']
        self._configurar_provincia_a(prospecto_dos, nombre='Provincia 1')
        prospecto_tres = self.fixture['p_3']
        self._configurar_provincia_a(prospecto_tres, nombre='Provincia 1')
        self._configurar_provincia_a(self.fixture['p_4'], nombre='Provincia 2')
        self._configurar_provincia_a(self.fixture['p_5'], nombre='Provincia 2')
        self._configurar_provincia_a(self.fixture['p_6'], nombre='Provincia 3')

        sin_prov = prospectos.count() - 6

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_provincia(QueryDict(''))
        self.assertEqual(prospectos.count(), filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_provincia(QueryDict('provincias=Provincia+1'))
        filtrados = filtrador.prospectos_filtrados()
        self.assertEqual(3, filtrados.count())
        self.assertIn(prospecto_uno, filtrados)
        self.assertIn(prospecto_dos, filtrados)
        self.assertIn(prospecto_tres, filtrados)

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_provincia(QueryDict('provincias=Provincia+1&provincias=Provincia+2'))
        self.assertEqual(5, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_provincia(QueryDict('provincias=sin-dato'))
        self.assertEqual(sin_prov, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_provincia(QueryDict('provincias=sin-dato&provincias=Provincia+3'))
        self.assertEqual(sin_prov + 1, filtrador.prospectos_filtrados().count())

    def test_filtrar_marcas(self):
        prospectos = Prospecto.objects.all()
        marca_uno = self._crear_y_asignar_marca(self.fixture['p_1'], 'marca 1')
        self._crear_y_asignar_marca(self.fixture['p_2'], 'marca 1')
        self._crear_y_asignar_marca(self.fixture['p_3'], 'marca 1')
        marca_dos = self._crear_y_asignar_marca(self.fixture['p_4'], 'marca 2')
        self._crear_y_asignar_marca(self.fixture['p_5'], 'marca 2')
        marca_tres = self._crear_y_asignar_marca(self.fixture['p_6'], 'marca 3')

        sin_marca = prospectos.count() - 6

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_marca(QueryDict(''))
        self.assertEqual(prospectos.count(), filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_marca(QueryDict('marcas=%s' % marca_uno.pk))
        self.assertEqual(3, filtrador.prospectos_filtrados().count())
        self.assertIn(self.fixture['p_1'], filtrador.prospectos_filtrados())
        self.assertIn(self.fixture['p_2'], filtrador.prospectos_filtrados())
        self.assertIn(self.fixture['p_3'], filtrador.prospectos_filtrados())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_marca(QueryDict('marcas=%s&marcas=%s' % (marca_uno.pk, marca_dos.pk)))
        self.assertEqual(5, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_marca(QueryDict('marcas=%s' % Marca.blanca().pk))
        self.assertEqual(sin_marca, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_marca(QueryDict('marcas=%s&marcas=%s' % (Marca.blanca().pk, marca_tres.pk)))
        self.assertEqual(sin_marca + 1, filtrador.prospectos_filtrados().count())

    def test_filtrar_prefijos(self):
        prospectos = Prospecto.objects.all()
        prospecto_uno = self.fixture['p_1']
        self._configurar_prefijo_a(prospecto_uno, prefijo='123')
        prospecto_dos = self.fixture['p_2']
        self._configurar_prefijo_a(prospecto_dos, prefijo='123')
        prospecto_tres = self.fixture['p_3']
        self._configurar_prefijo_a(prospecto_tres, prefijo='123')
        self._configurar_prefijo_a(self.fixture['p_4'], prefijo='234')
        self._configurar_prefijo_a(self.fixture['p_5'], prefijo='234')
        self._configurar_prefijo_a(self.fixture['p_6'], prefijo='3 4 5')

        sin_prefijo = prospectos.count() - 6

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_prefijo(QueryDict(''))
        self.assertEqual(prospectos.count(), filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_prefijo(QueryDict('prefijos=00123'))
        self.assertEqual(3, filtrador.prospectos_filtrados().count())
        self.assertIn(prospecto_uno, filtrador.prospectos_filtrados())
        self.assertIn(prospecto_dos, filtrador.prospectos_filtrados())
        self.assertIn(prospecto_tres, filtrador.prospectos_filtrados())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_prefijo(QueryDict('prefijos=00123&prefijos=00234'))
        self.assertEqual(5, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_prefijo(QueryDict('prefijos=sin-dato'))
        self.assertEqual(sin_prefijo, filtrador.prospectos_filtrados().count())

        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=self.user)
        filtrador.filtrar_por_prefijo(QueryDict('prefijos=sin-dato&prefijos=00345'))
        self.assertEqual(sin_prefijo + 1, filtrador.prospectos_filtrados().count())

    def test_filtrar_prospectos_asignados_dias_en_reposo(self):
        """
            Simula que tiene 1 prospecto creado hace 5 dias, 1 hace 6, 2 hace 10 y 1 hace 11 dias.
            Los restantes creados hoy
        """
        prospectos = Prospecto.objects.all()
        prospectos = prospectos.filter(estado='N')
        cantidad_menor_a_5 = prospectos.count() - 5

        self._cambiar_fecha_creacion(prospectos, [([0], -5), ([1], -6), ([2, 3], -10), ([4], -11)])

        self._assert_filtro_por_dias_en_resposo_para(prospectos=prospectos, valor_filtro='menor-a-5',
                                                     cantidad_esperada=cantidad_menor_a_5, usuario=self.user)
        self._assert_filtro_por_dias_en_resposo_para(prospectos=prospectos, valor_filtro='entre-5-y-10',
                                                     cantidad_esperada=4, usuario=self.user)
        self._assert_filtro_por_dias_en_resposo_para(prospectos=prospectos, valor_filtro='mas-de-10',
                                                     cantidad_esperada=1, usuario=self.user)

    def _cambiar_fecha_creacion(self, prospectos, especificacion_de_cambios):
        """
            Modifica las fechas de creacin segun lo estipulado en <especificacion_de_cambios>
        :param prospectos: Lista de Prospectos
        :param especificacion_de_cambios: lista de tuplas. Cada tupla indica los indices de los prospectos y
        la cantidad de dias que se le sumara a la fecha de creacion actual.
        """
        lista_de_prospectos = prospectos.all()
        for spec in especificacion_de_cambios:
            indices = spec[0]
            cantidad_dias = spec[1]
            for indice in indices:
                prospecto = lista_de_prospectos[indice]
                self._cambiar_fecha_creacion_prospecto(prospecto, cantidad_dias)

    def _cambiar_fecha_creacion_prospecto(self, prospecto, cantidad_dias):
        hoy = timezone.localtime(timezone.now())
        hoy = make_aware(datetime(hoy.year, hoy.month, hoy.day), get_current_timezone())
        fecha = hoy + timedelta(days=cantidad_dias)
        prospecto.fecha_creacion = fecha
        prospecto.save()

    def _assert_filtro_por_dias_en_resposo_para(self, prospectos, valor_filtro, cantidad_esperada, usuario):
        filtrador = FiltroAdministracion(filtrados=prospectos, usuario=usuario)
        filtrador.filtrar_por_estado(QueryDict('filtro_estado=N&dias-en-reposo=%s' % valor_filtro), usuario.vendedor)
        self.assertEqual(filtrador.prospectos_filtrados().count(), cantidad_esperada)


@override_settings(DIAS_ANTES_DE_QUE_NO_SE_PUEDA_RECHAZAR=4)
class FiltroPorAccionesTest(BaseLoggedSupervisorTest):

    def setUp(self):
        super(FiltroPorAccionesTest, self).setUp()
        self.prospectos = Prospecto.objects.all()

    def test_filtrar_por_rechazo_devuelve_solo_prospectos_rechazables_por_fecha(self):
        prospectos_asignados = self.prospectos.filter(responsable__isnull=False, vendedor__isnull=False)
        self._cambiar_fecha_de_asignacion_a_supervisor(prospecto=prospectos_asignados[0],
                                                       cantidad_dias=-(
                                                                   settings.DIAS_ANTES_DE_QUE_NO_SE_PUEDA_RECHAZAR + 1))
        filtrador = FiltroAdministracion(filtrados=prospectos_asignados, usuario=self.user)
        filtrador.filtrar_por_accion(accion='rechazar')
        prospectos_filtrados = filtrador.prospectos_filtrados()
        self.assertEqual(prospectos_filtrados.count(), prospectos_asignados.count() - 1)

    def test_filtrar_por_reasignar_devuelve_todos_los_prospectos_aplicables(self):
        filtrador = FiltroAdministracion(filtrados=Prospecto.objects.all(), usuario=self.user)
        filtrador.filtrar_por_accion(accion='reasignar')
        prospectos_filtrados = filtrador.prospectos_filtrados()
        self.assertEqual(prospectos_filtrados.count(), self.prospectos.count())

    def test_filtrar_por_exportar_devuelve_todos_los_prospectos_aplicables(self):
        filtrador = FiltroAdministracion(filtrados=Prospecto.objects.all(), usuario=self.user)
        filtrador.filtrar_por_accion(accion='exportar')
        prospectos_filtrados = filtrador.prospectos_filtrados()
        self.assertEqual(prospectos_filtrados.count(), self.prospectos.count())

    def _cambiar_fecha_de_asignacion_a_supervisor(self, prospecto, cantidad_dias):
        hoy = timezone.localtime(timezone.now())
        hoy = make_aware(datetime(hoy.year, hoy.month, hoy.day), get_current_timezone())
        fecha = hoy + timedelta(days=cantidad_dias)
        asignacion = prospecto.asignacion
        asignacion.cambiar_fecha_para_supervisor(fecha)
        asignacion.save()
