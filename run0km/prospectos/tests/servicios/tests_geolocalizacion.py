from prospectos.models.gestor.constructor_de_prospectos import ConstructorDeProspectos
from testing.base import BaseFixturedTest


class GeolocalizacionTest(BaseFixturedTest):
    def setUp(self):
        super(GeolocalizacionTest, self).setUp()
        self.constructor = ConstructorDeProspectos.nuevo()
        self.prospecto = self.fixture['p_1']
        self._completar_ip(prospecto=self.prospecto, ip='***************')

    def _completar_ip(self, prospecto, ip):
        geolocalizacion = prospecto.obtener_geolocalizacion()
        geolocalizacion.ip = ip
        geolocalizacion.save()

    def test_completar_datos_de_geolocalizacion_para_prospecto_genera_modelo_de_geolocalizacion_asociado_completo(self):
        provincia_desde_ip = 'pro_test'
        localidad_desde_ip = 'loc_test'
        latitud_desde_ip = 34.8
        longitud_desde_ip = 34.8
        self.constructor.completar_datos_geolocalizacion_desde_ip(
            prospecto=self.prospecto, provincia=provincia_desde_ip,
            localidad=localidad_desde_ip, latitud=latitud_desde_ip,
            longitud=longitud_desde_ip)
        self.prospecto.refresh_from_db()
        self._assert_geolocalizacion(prospecto=self.prospecto, provincia_desde_ip=provincia_desde_ip,
                                     localidad_desde_ip=localidad_desde_ip, latitud_desde_ip=latitud_desde_ip,
                                     longitud_desde_ip=longitud_desde_ip)

    def _assert_geolocalizacion(self, prospecto,
                                provincia_desde_ip, localidad_desde_ip, latitud_desde_ip, longitud_desde_ip):
        geolocalizacion = prospecto.obtener_geolocalizacion()
        self.assertEqual(geolocalizacion.obtener_provincia(), provincia_desde_ip)
        self.assertEqual(geolocalizacion.obtener_localidad(), localidad_desde_ip)
        self.assertEqual(geolocalizacion.obtener_latitud(), latitud_desde_ip)
        self.assertEqual(geolocalizacion.obtener_longitud(), longitud_desde_ip)
        self.assertEqual(geolocalizacion.prospecto, prospecto)


