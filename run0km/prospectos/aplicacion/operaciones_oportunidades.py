# coding=utf-8
import json

from django.conf import settings
from django.urls import reverse

from layers.domain import DomainOperation
from objetivos.models import Objetivo, PeriodoPersistido
from occ.views.configuracion_de_cuenta import ConfiguracionDeCuenta
from prospectos.models import Venta
from reportes.controladores import ReportesController
from reportes.models import TipoDeReporte, \
    FrecuenciaDeProgramacion, TipoDeStaff
from vendedores.gestor import GestorDeVendedores


class OperacionesDeOportunidadesFactory(object):
    def nuevo_para(self, nombre, parameters):
        for operacion_class in OperacionDelivery.__subclasses__():
            if operacion_class.nombre() == nombre:
                return operacion_class(parameters=parameters)
        raise ValueError('El nombre %s no coincide con el de ninguna operacion' % nombre)


class OperacionDelivery(DomainOperation):
    """El proposito de esta clase es traducir al castellano el protocolo de ApplicationOperation

    En caso de exito el object del Result tiene un diccionario con el siguiente formato
        mensaje: una string detallando el resultado exitoso de la operacion
        datos: un objeto plano (convertible a JSON) con los datos extra que se necesiten
    En caso de error, el primer error del Result es una string.
    """

    @classmethod
    def nombre(cls):
        return cls.name()

    def objeto_para_resultado_exitoso(self, mensaje, datos=None):
        return {
            'mensaje': mensaje,
            'datos': datos
        }


class CrearNuevaProgramacionParaSupervisor(OperacionDelivery):
    @classmethod
    def name(cls):
        return 'programarReporte'

    def __init__(self, parameters=None):
        super(CrearNuevaProgramacionParaSupervisor, self).__init__()
        self._parameters = parameters

    def separador_de_mail(self):
        return ';'

    def mails_string_a_modelos(self, mails_string):
        from reportes.models import Mail
        return Mail.mails_de_string(mails_string, self.separador_de_mail())

    def tiene_reporte_hecho(self):
        supervisor = self._parameters['supervisor']
        return supervisor.programaciones.semanales().exists()

    def execute(self):
        result = super(CrearNuevaProgramacionParaSupervisor, self).execute()

        if not self._parameters['supervisor']:
            result.add_error("No está definido el supervisor al cual crearle el reporte")
            return result
        if not self._parameters['supervisor'].email():
            result.add_error("No está definido el mail del supervisor (al cual enviarle el reporte)")
        if self.tiene_reporte_hecho():
            result.add_error("El sueprvisor ya tiene un reporte creado")
        if result.has_errors():
            return result

        try:
            programacion = self._nueva_programacion()
        except Exception as exc:
            result.add_error("La programación no se pudo crear ({0})".format(str(exc)))
            return result

        result.set_object(self.objeto_para_resultado_exitoso(
            mensaje="Se ha programado un reporte {0}".format(programacion.frecuencia().descripcion())
        ))

        return result

    # --- private methods ---

    def _nueva_programacion(self):
        supervisor = self._parameters['supervisor']
        controlador = ReportesController.nuevo_para(supervisor.user)
        mails = self.mails_string_a_modelos(supervisor.email())
        nueva_programacion = controlador.nueva_programacion_de_reportes_para(
            tipos_de_reportes=(TipoDeReporte.tipos_de_reporte_validos()),
            mails=mails,
            tipo_de_staff=self._tipo_de_staff(),
            equipos=[],
            staff_a_cargo=(supervisor.staff_a_cargo()),
            tipo_de_frecuencia=self._tipo_de_frecuencia(),
            user=supervisor.user,
        )
        return nueva_programacion

    def _tipo_de_frecuencia(self):
        return FrecuenciaDeProgramacion.SEMANAL

    def _tipo_de_staff(self):
        return TipoDeStaff.VENDEDORES


class ConfigurarTiendaParaSupervisor(OperacionDelivery):
    @classmethod
    def name(cls):
        return 'configurarAutoTienda'

    def __init__(self, parameters):
        super(ConfigurarTiendaParaSupervisor, self).__init__()
        self._parameters = parameters

    def execute(self):
        result = super(ConfigurarTiendaParaSupervisor, self).execute()
        if result.has_errors():
            return result
        self._configurar(result)
        return result

    def _configurar(self, result):
        valor = self._parameters.get('valor', 'false')
        try:
            habilitado = json.loads(valor)
        except ValueError:
            result.add_error('%s no es una opción válida' % valor)
            return

        supervisor = self._parameters.get('supervisor')
        gestor = GestorDeVendedores.nuevo()
        gestor.configurar_auto_tienda(supervisor, habilitado=habilitado)
        if habilitado:
            result.set_object(self.objeto_para_resultado_exitoso(
                mensaje='La configuración se ha realizado de forma exitosa'
            ))
        else:
            result.set_object(self.objeto_para_resultado_exitoso(
                mensaje="Se ha quitado la opción de forma exitosa"
            ))


class ConfigurarMotoAutosParaSupervisor(OperacionDelivery):
    @classmethod
    def name(cls):
        return 'configurarMotoAutos'

    def __init__(self, parameters):
        super(ConfigurarMotoAutosParaSupervisor, self).__init__()
        self._parameters = parameters

    def execute(self):
        result = super(ConfigurarMotoAutosParaSupervisor, self).execute()
        if result.has_errors():
            return result
        self._configurar(result)
        return result

    def _configurar(self, result):
        valor = self._parameters.get('valor', 'false')
        try:
            habilitado = json.loads(valor)
        except ValueError:
            result.add_error('%s no es una opción válida' % valor)
            return

        supervisor = self._parameters.get('supervisor')
        gestor = GestorDeVendedores.nuevo()
        gestor.configurar_moto_autos(supervisor, habilitado=habilitado)
        if habilitado:
            result.set_object(self.objeto_para_resultado_exitoso(
                mensaje="La configuración se ha realizado de forma exitosa"
            ))
        else:
            result.set_object(self.objeto_para_resultado_exitoso(
                mensaje="Se ha quitado la opción de forma exitosa"
            ))


class IniciarRankingDeVendedores(OperacionDelivery):
    @classmethod
    def name(cls):
        return 'configurarActivarRankingDeVendedores'

    def __init__(self, parameters):
        super(IniciarRankingDeVendedores, self).__init__()
        self._parameters = parameters

    def explicacion(self):
        return "Se iniciará una competencia entre los vendedores, con los mejores competidores en las posiciones" \
               " más altas del ranking."

    def execute(self):
        result = super(IniciarRankingDeVendedores, self).execute()
        supervisor = self._parameters.get('supervisor')
        supervisor.habilitar_ranking()
        result.set_object(self.objeto_para_resultado_exitoso(
            mensaje="Se inició la competencia entre los vendedores exitosamente.",
        ))
        return result


class ConfigurarCuentaDeGmail(OperacionDelivery):
    @classmethod
    def name(cls):
        return 'configurarCuentaDeGmail'

    def __init__(self, parameters):
        super(ConfigurarCuentaDeGmail, self).__init__()
        self._parameters = parameters

    def explicacion(self):
        return "Se iniciará una competencia entre los vendedores, con los mejores competidores en las posiciones" \
               " más altas del ranking."

    def execute(self):
        result = super(ConfigurarCuentaDeGmail, self).execute()
        configuracion_de_cuenta = ConfiguracionDeCuenta.nuevo_para(supervisor=self._parameters.get('supervisor'))
        result.set_object(self.objeto_para_resultado_exitoso(
            mensaje="Abriendo pestaña a Gmail..",
            datos=configuracion_de_cuenta.cuenta_propia_url()
        ))
        return result


class AsignarObjetivosAVendedores(OperacionDelivery):
    """Modelo la operacion de asignar objetivos a vendedores."""

    @classmethod
    def name(cls):
        return 'asignarObjetivos'

    def __init__(self, parameters):
        super(AsignarObjetivosAVendedores, self).__init__()
        self._parameters = parameters

    def explicacion(self):
        return "Se asignará el promedio de ventas del historial de cada vendedor"

    def tiene_vendedores_para_asignar_objetivos(self):
        periodo = self._periodo_para_mes_en_curso()
        vendedores_sin_objetivos_para_mes_en_curso = self._vendedores_sin_objetivos_en(periodo)
        return len(vendedores_sin_objetivos_para_mes_en_curso) > 0

    def execute(self):
        result = super(AsignarObjetivosAVendedores, self).execute()

        periodo = self._periodo_para_mes_en_curso()
        vendedores_sin_objetivos_para_mes_en_curso = self._vendedores_sin_objetivos_en(periodo)
        objetivos = self._asignar_objetivos_para(vendedores_sin_objetivos_para_mes_en_curso)
        objetivos_aplanados = self._aplanar_objetivos(objetivos)

        datos = {
            'objetivos': objetivos_aplanados,
            'url_administrar_objetivos': reverse('objetivos'),
        }

        result.set_object(self.objeto_para_resultado_exitoso(
            mensaje="Se les asignaron los objetivos a los vendedores que no los tenían definidos",
            datos=datos
        ))
        return result

    # --- metodos privados ---

    def _periodo_para_mes_en_curso(self):
        concesionaria = self._supervisor().obtener_concesionaria()
        return concesionaria.periodo_actual()

    def _supervisor(self):
        return self._parameters['supervisor']

    def _vendedores_sin_objetivos_en(self, periodo):
        vendedores = self._supervisor().vendedores_a_cargo()

        vendedores_con_objetivos = self._vendedores_con_objetivos(periodo, vendedores)
        vendedores_sin_objetivos = self._diferencia_entre(vendedores, vendedores_con_objetivos)

        return vendedores_sin_objetivos

    def _diferencia_entre(self, vendedores, vendedores_con_objetivos):
        return [vendedor for vendedor in vendedores if vendedor.pk not in vendedores_con_objetivos]

    def _vendedores_con_objetivos(self, periodo, vendedores):
        objetivos_definidos = Objetivo.objects.para_vendedores(vendedores).en_periodo(periodo)
        vendedores_con_objetivos = objetivos_definidos.vendedores_ids()
        return vendedores_con_objetivos

    def _asignar_objetivos_para(self, vendedores):
        periodo_actual = self._periodo_para_mes_en_curso()
        periodo_persistido = PeriodoPersistido.persistir(periodo_actual)
        objetivos = []
        unidad = Objetivo.VENTA
        for vendedor in vendedores:
            cantidad = self._cantidad_objetivo_de_ventas_para(vendedor)
            objetivos.append(
                Objetivo(
                    valor=cantidad,
                    unidad=unidad,
                    periodo=periodo_persistido,
                    vendedor=vendedor)
            )
        modelos = Objetivo.objects.bulk_create(objetivos)
        return modelos

    def _minima_cantidad_objetivo_de_ventas(self):
        return settings.MINIMA_CANTIDAD_OBJETIVO_VENTAS_ASIGNABLE_EN_OPORTUNIDADES

    def _cantidad_objetivo_de_ventas_para(self, vendedor):
        minimo = self._minima_cantidad_objetivo_de_ventas()
        promedio = self._promedio_ventas_anteriores(vendedor)
        return max([promedio, minimo])

    def _promedio_ventas_anteriores(self, vendedor):
        meses = settings.CANTIDAD_DE_MESES_PARA_CALCULAR_PROMEDIO_DE_VENTAS_DE_VENDEDORES
        total_de_ventas = Venta.objects.de_vendedor(vendedor).realizadas_en_los_ultimos_meses(meses=meses).count()
        promedio_de_ventas = total_de_ventas / meses
        return promedio_de_ventas

    def _aplanar_objetivos(self, objetivos):
        objetivos_aplanados = []
        for objetivo in objetivos:
            objetivos_aplanados.append({
                'nombre_completo': objetivo.vendedor.full_name(),
                'unidad': objetivo.unidad,
                'cantidad': objetivo.valor,
                'cantidad_en_unidades': objetivo.valor_en_unidad()
            })
        return objetivos_aplanados
