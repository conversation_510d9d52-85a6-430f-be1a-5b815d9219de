# coding=utf-8
from django.core.exceptions import ValidationError

from prospectos.models import Prospecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from vendedores.models import Vendedor, PermisoTransferencia
from vendedores.permissions.permissions import CanTransferProspectPermission
from vendedores.forms import TransferirProspectoForm

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authentication import SessionAuthentication


class TransferirProspectoView(APIView):
    permission_classes = [CanTransferProspectPermission]
    authentication_classes = [SessionAuthentication]

    def post(self, request):
        form = TransferirProspectoForm(request.data)
        if form.is_valid():
            prospecto_id = form.cleaned_data['prospecto_id']
            vendedor_destinatario_id = form.cleaned_data['vendedor_destinatario_id']
            vendedor_actual = request.user.vendedor

            if not vendedor_actual.concesionaria.configuracion_de_servicios().puede_transferir_prospectos():
                return Response({'success': False, 'message': 'Actualmente no tiene permiso de transferir prospecto.'},
                                status=status.HTTP_403_FORBIDDEN)

            try:
                prospecto = Prospecto.objects.get(id=prospecto_id)
                vendedor_destinatario = Vendedor.objects.get(id=vendedor_destinatario_id)
                gestor = GestorDeProspecto.nuevo_para(vendedor_actual)
                #TODO agregar tests unitarios
                gestor.transferir_prospecto_entre_vendedores(prospecto, vendedor_destinatario)
                return Response({'success': True, 'message': 'Prospecto transferido exitosamente.'},
                                status=status.HTTP_200_OK)
            except Prospecto.DoesNotExist:
                return Response({'success': False, 'message': 'Prospecto no encontrado.'},
                                status=status.HTTP_404_NOT_FOUND)
            except Vendedor.DoesNotExist:
                return Response({'success': False, 'message': 'Vendedor destinatario no encontrado.'},
                                status=status.HTTP_404_NOT_FOUND)
            except ValidationError as e:
                return Response({"message": str(e)}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        return Response({'success': False, 'message': 'Formulario inválido.'}, status=status.HTTP_400_BAD_REQUEST)