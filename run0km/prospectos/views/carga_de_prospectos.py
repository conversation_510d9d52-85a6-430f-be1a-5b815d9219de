# coding=utf-8
import json

from django.contrib import messages
from django.core.exceptions import ValidationError, PermissionDenied
from django.http import JsonResponse
from django.shortcuts import render
from django.views.generic import View

from campanias.models import Campania, TipoDeOrigen
from layers.application.commands.ingreso_de_prospectos.ingreso_desde_web import IngresoDeProspectoDesdeWebComando
from prospectos.models import Modelo
from prospectos.models.exceptions import ProspectoRechazadoException, LoteDeProspectosRechazadoException
from prospectos.services.pedir_prospecto_service import PedirProspectoService
from prospectos.views.forms import CargaDeProspectoForm
from vendedores.decorators import vendedor_o_supervisor_requerido, class_view_decorator


@class_view_decorator(vendedor_o_supervisor_requerido)
class CargaDeProspectosView(View):
    template_name = 'prospectos/cargar_prospecto.html'
    CAMPO_TELEFONO_EXTRA = 'Teléfono Extra'
    CAMPO_MAIL_EXTRA = 'Email Extra'

    def __init__(self, **kwargs):
        self.vendedor = None
        super(CargaDeProspectosView, self).__init__(**kwargs)

    @classmethod
    def mensaje_prospecto_ya_cargado(cls):
        return 'El Prospecto ya ha sido cargado al Sistema.'

    def dispatch(self, request, *args, **kwargs):
        self.vendedor = request.user.vendedor
        if not self.vendedor.es_vendedor_con_pedidos_activos():
            raise PermissionDenied
        return super(CargaDeProspectosView, self).dispatch(request, *args, **kwargs)

    def get(self, request):
        contexto = self._contexto_para_respuesta_de_get()

        return render(request, self.template_name, contexto)

    def post(self, request):
        prospecto_form = CargaDeProspectoForm(request.POST, vendedor=self.vendedor)
        prospecto_form.definir_choices_de_modelos()
        if prospecto_form.is_valid():
            return self._crear_campania_y_prospecto_y_dar_una_respuesta_a_partir_de(
                prospecto_form=prospecto_form,
                campania_creada=json.loads(request.POST['campania_creada']),
                request=request)
        return self._respuesta_formulario_invalido(prospecto_form=prospecto_form,
                                                   campania_creada=request.POST['campania_creada'], request=request)

    def _crear_campania_y_prospecto_y_dar_una_respuesta_a_partir_de(self, prospecto_form, campania_creada, request):
        try:
            self._ejecutar_comando(datos_de_prospecto=prospecto_form.cleaned_data, datos_campos_extras=request.POST)
        except ValidationError as exc:
            return self._respuesta_con_error(prospecto_form=prospecto_form, request=request,
                                             campania_creada=campania_creada, mensajes_de_error=exc.messages)
        except ProspectoRechazadoException:
            return self._respuesta_con_error(prospecto_form=prospecto_form, request=request,
                                             campania_creada=campania_creada,
                                             mensajes_de_error=[self.mensaje_prospecto_ya_cargado()])
        except LoteDeProspectosRechazadoException as exc:
            return self._respuesta_con_error(prospecto_form=prospecto_form, request=request,
                                             campania_creada=campania_creada,
                                             mensajes_de_error=[str(exc)])
        return self._respuesta_exitosa(request)

    def _ejecutar_comando(self, datos_de_prospecto, datos_campos_extras):
        campos_extras, telefonos, emails = self._campos_extras_emails_y_telefonos(datos_campos_extras)

        comando = IngresoDeProspectoDesdeWebComando(
            datos_del_prospecto=datos_de_prospecto, campos_extras=campos_extras, telefonos=telefonos,
            emails=emails, vendedor=self.vendedor)
        comando.execute()

    def _respuesta_exitosa(self, request):
        messages.add_message(request, messages.SUCCESS, self.mensaje_prospecto_creado_exitosamente())

        contexto = self._contexto_para_respuesta_exitosa()

        return render(request, self.template_name, contexto)

    def _respuesta_con_error(self, prospecto_form, campania_creada, request, mensajes_de_error):
        for mensaje in mensajes_de_error:
            messages.add_message(request, messages.ERROR, mensaje)
        contexto = self._contexto_para_respuesta_sin_exito(prospecto_form,
                                                           campania_creada=campania_creada,
                                                           datos=request.POST)
        return render(request, self.template_name, contexto)

    def _respuesta_formulario_invalido(self, prospecto_form, campania_creada, request):
        for mensaje_error in prospecto_form.non_field_errors():
            messages.add_message(request, messages.ERROR, mensaje_error)
        contexto = self._contexto_para_respuesta_sin_exito(prospecto_form,
                                                           campania_creada=campania_creada,
                                                           datos=request.POST)
        return render(request, self.template_name, contexto)

    def _contexto_para_respuesta_de_get(self):
        form = CargaDeProspectoForm(vendedor=self.vendedor, definir_marca_por_defecto=True)
        muestra_boton_pedir_prospecto = PedirProspectoService.cache_muestra_boton_pedir_prospecto(
            self.vendedor.user)
        return self._contexto(form,
                              self.vendedor,
                              self._campanias_seleccionables(),
                              campania_seleccionada='',
                              campania_creada=False,
                              campos_extras={},
                              modelos=self._modelos_con_marca(),
                              recalcular_select_de_modelos=True,
                              muestra_boton_pedir_prospecto=muestra_boton_pedir_prospecto)

    def _contexto_para_respuesta_exitosa(self):
        return self._contexto_para_respuesta_de_get()

    def _contexto_para_respuesta_sin_exito(self, prospecto_form, campania_creada, datos):
        prospecto_form.definir_choices_de_modelos()
        campos_extra, telefonos, emails = self._campos_extras_emails_y_telefonos(datos)
        muestra_boton_pedir_prospecto = PedirProspectoService.cache_muestra_boton_pedir_prospecto(
            self.vendedor.user)
        return self._contexto(prospecto_form,
                              self.vendedor,
                              self._campanias_seleccionables(),
                              campania_seleccionada=prospecto_form.cleaned_data.get('nombre_de_campania', ''),
                              campania_creada=campania_creada,
                              campos_extras=campos_extra,
                              modelos=self._modelos_con_marca(),
                              recalcular_select_de_modelos=False,
                              muestra_boton_pedir_prospecto=muestra_boton_pedir_prospecto)

    def _contexto(self, form, vendedor, campanias_seleccionables, campania_seleccionada, campania_creada, campos_extras,
                  modelos, recalcular_select_de_modelos,muestra_boton_pedir_prospecto):
        return {'form': form,
                'vendedor': vendedor,
                'campanias_seleccionables': json.dumps(campanias_seleccionables),
                'campania_seleccionada': json.dumps(campania_seleccionada),
                'campania_creada': json.dumps(campania_creada),
                'campos_extras': campos_extras,
                'modelos': json.dumps(modelos),
                'recalcular_select_de_modelos': json.dumps(recalcular_select_de_modelos),
                'muestra_boton_pedir_prospecto': muestra_boton_pedir_prospecto
                }

    def _campos_extras_emails_y_telefonos(self, datos):
        """Devuelvo un diccionario de campos extra y una lista de telefonos extraidos de <datos>"""
        campos_extras = {}
        telefonos = []
        emails = []
        cantidad = int(datos['cantidad_de_campos_extra_agregados'])
        for indice in range(0, cantidad):
            nombre, valor = self._sintetizar_campo_extra(indice, datos)
            self._procesar_campo_extra(campos_extras, nombre, telefonos, emails, valor)

        return campos_extras, telefonos, emails

    def _procesar_campo_extra(self, campos_extras, nombre, telefonos, emails, valor):
        if nombre is None:
            return  # Esto sucede cuando el campo extra esta mal armado
        campo_es_telefono = self.CAMPO_TELEFONO_EXTRA in nombre
        campo_es_email = self.CAMPO_MAIL_EXTRA in nombre
        if campo_es_telefono:
            telefonos.append(valor)
        elif campo_es_email:
            emails.append(valor)
        else:
            campos_extras[nombre] = valor

    def _sintetizar_campo_extra(self, indice, datos_de_origen):
        nombre_campo = 'nombre_campo_extra-%d' % (indice + 1)
        valor_campo = 'valor_campo_extra-%d' % (indice + 1)
        nombre_esta_bien_armado = nombre_campo in datos_de_origen and datos_de_origen[nombre_campo]
        valor_esta_bien_armado = valor_campo in datos_de_origen and datos_de_origen[valor_campo]
        campo_esta_bien_armado = nombre_esta_bien_armado and valor_esta_bien_armado
        if not campo_esta_bien_armado:
            return None, None
        nombre_de_campo_procesado = datos_de_origen[nombre_campo]
        valor_de_campo_procesado = datos_de_origen[valor_campo]
        return nombre_de_campo_procesado, valor_de_campo_procesado

    @classmethod
    def mensaje_prospecto_creado_exitosamente(cls):
        return 'El prospecto ha sido cargado exitosamente'

    def _campanias_seleccionables(self):
        return list(Campania.objects.campanias_externas_de_concesionaria(
            concesionaria=self.vendedor.obtener_concesionaria()).values_list('nombre', flat=True))
        # calidad = request.GET.get('calidad', 'Todas')
        # if calidad == 'Todas':
        #     return list(Campania.objects.campanias_de_concesionaria(
        #                 concesionaria=self.vendedor.obtener_concesionaria()).values_list('nombre', flat=True))
        # else:
        #     return list(
        #         Campania.objects.campanias_de_concesionaria(concesionaria=self.vendedor.obtener_concesionaria()).filter(
        #             categoria__tipo_de_origen__nombre=calidad).values_list('nombre', flat=True))

    def _modelos_con_marca(self):
        """Devuelve una lista de modelos cada uno con su marca"""
        modelos = Modelo.objects.normalizados().select_related_marca()
        modelos_for_context = []
        for modelo in modelos:
            modelos_for_context.append(
                {
                    'id': modelo.pk,
                    'marca': modelo.marca().nombre(),
                    'nombre': modelo.nombre(),
                }
            )
        return modelos_for_context


@class_view_decorator(vendedor_o_supervisor_requerido)
class CalidadParaCampaniaView(View):
    def get(self, request):
        concesionaria = request.user.vendedor.obtener_concesionaria()
        nombre_campania = request.GET['campania_nombre']
        try:
            campania = Campania.objects.get(nombre=nombre_campania, concesionaria=concesionaria)
            return JsonResponse({'calidad': campania.categoria.tipo_de_origen.pk,
                                 'calidad_numerica': campania.categoria.calidad})
        except Campania.DoesNotExist:
            return JsonResponse({'calidad': ''})


class RefrescarCampaniasView(View):
    _TODAS = ''

    def __init__(self, **kwargs):
        self.vendedor = None
        super(RefrescarCampaniasView, self).__init__(**kwargs)

    def get(self, request):
        if not request.user.is_vendedor():
            raise PermissionDenied
        self.vendedor = request.user.vendedor
        calidad_pk = request.GET.get('calidad_pk', self._TODAS)
        campanias = self._campanias_seleccionables(calidad_pk)
        return JsonResponse({'status': True, 'campanias_seleccionables': list(campanias)})

    def _campanias_seleccionables(self, calidad_pk):
        concesionaria = self.vendedor.obtener_concesionaria()
        if calidad_pk == self._TODAS:
            campanias_filtradas = Campania.objects.campanias_externas_de_concesionaria(concesionaria)
        else:
            calidad = TipoDeOrigen.objects.get(pk=calidad_pk)
            campanias_filtradas = Campania.objects.campanias_externas_de_concesionaria_y_calidad(
                concesionaria=concesionaria, calidad=calidad)
        return campanias_filtradas.values_list('nombre', flat=True)
