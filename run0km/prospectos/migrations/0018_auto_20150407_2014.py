# -*- coding: utf-8 -*-


from django.db import models, migrations


def ajustar_factores_existentes(apps, _):
        pedido_model_klass = apps.get_model("prospectos", "PedidoDeProspecto")
        pedidos = pedido_model_klass.objects.all()
        for pedido in pedidos:
            pedido.factor_de_distribucion = pedido.factor_de_distribucion * 10
            pedido.save()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0017_pedidodeprospecto_factor_de_distribucion'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pedidodeprospecto',
            name='factor_de_distribucion',
            field=models.PositiveIntegerField(default=10),
            preserve_default=True,
        ),
        migrations.RunPython(ajustar_factores_existentes),
    ]
