# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-03-16 15:33


from django.db import migrations


def modificar_exclusion_de_campania(apps, schema_editor):
    PedidoDeProspecto = apps.get_model("prospectos", "PedidoDeProspecto")
    PedidoDeProspecto.objects.all().update(_excluye_campanias=False)


def undo_modificar_exclusion_de_campania(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0082_venta_motivo_de_cancelacion'),
    ]

    operations = [
        migrations.RunPython(modificar_exclusion_de_campania, undo_modificar_exclusion_de_campania)
    ]

