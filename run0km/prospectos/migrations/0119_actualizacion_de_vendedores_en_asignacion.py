# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-09-24 14:28


from django.db import migrations


def migrar_asignaciones_de_vendedores(apps, schema_editor):
    Vendedor = apps.get_model("vendedores", "Vendedor")
    Prospecto = apps.get_model("prospectos", "Prospecto")
    AsignacionDeProspecto = apps.get_model("prospectos", "AsignacionDeProspecto")

    for vendedor in Vendedor.objects.all():
        prospectos = Prospecto.objects.filter(vendedor=vendedor)
        ids = list(prospectos.values_list('id', flat=True))
        asignaciones_de_prospectos = AsignacionDeProspecto.objects.filter(prospecto__in=ids, vendedor=None)
        asignaciones_de_prospectos.update(vendedor=vendedor)
        print('Actualizado %d prospectos de vendedor id %s', len(ids), vendedor.pk)


def undo_migrar_asignaciones_de_vendedores(apps, schema_editor):
    AsignacionDeProspecto = apps.get_model("prospectos", "AsignacionDeProspecto")
    asignaciones_de_prospectos = AsignacionDeProspecto.objects.all()
    asignaciones_de_prospectos.update(vendedor=None)


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0118_asignaciondeprospecto_vendedor'),
    ]

    operations = [
        migrations.RunPython(migrar_asignaciones_de_vendedores, undo_migrar_asignaciones_de_vendedores)
    ]
