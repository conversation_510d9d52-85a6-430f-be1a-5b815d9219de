# -*- coding: utf-8 -*-


from django.db import models, migrations


def contabilizar_ventas_pasadas(apps, _):
        aprobadas_model_klass = apps.get_model("vendedores", "VentasAprobadasPorMes")
        ventas_model_klass = apps.get_model("prospectos", "Venta")
        ventas = ventas_model_klass.objects.all()
        aprobadas = {}
        for venta in ventas:
            anio = venta.fecha.year
            mes = venta.fecha.month
            if not venta.vendedor in aprobadas:
                aprobadas[venta.vendedor] = {}
            if not anio in aprobadas[venta.vendedor]:
                aprobadas[venta.vendedor][anio] = {}
            if not mes in aprobadas[venta.vendedor][anio]:
                aprobadas[venta.vendedor][anio][mes] = 0
            aprobadas[venta.vendedor][anio][mes] += 1

        for (vendedor, cantidades_anio) in list(aprobadas.items()):
            for (anio, cantidades_mes) in list(cantidades_anio.items()):
                for (mes, cantidad) in list(cantidades_mes.items()):
                    aprobadas = aprobadas_model_klass(vendedor=vendedor, anio=anio, mes=mes, cantidad=cantidad)
                    aprobadas.save()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0019_venta_aprobada'),
        ('vendedores', '0006_auto_20150331_0115'),
    ]

    operations = [
        migrations.RunPython(contabilizar_ventas_pasadas),
    ]
