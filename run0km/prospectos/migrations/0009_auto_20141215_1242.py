# -*- coding: utf-8 -*-


from django.db import models, migrations
import prospectos.models
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0008_merge'),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON>mp<PERSON>',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('mes', models.PositiveSmallIntegerField(default=prospectos.models.mes_actual, choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')])),
                ('anio', models.PositiveIntegerField(default=prospectos.models.anio_actual, verbose_name=b'a\xc3\xb1o', validators=[django.core.validators.MinValueValidator(1900)])),
                ('monto', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('proveedor', models.ForeignKey(related_name='compras', to='prospectos.Proveedor')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name='compra',
            unique_together=set([('proveedor', 'mes', 'anio')]),
        ),
    ]
