# crear las marcas y los aliases a mano


from django.conf import settings
from django.db import migrations

from prospectos.utils.normalizador_de_marcas import NormalizadorDeMarcas


def crear_marcas_normalizadas_con_aliases(apps, schema_editor):
    _crear_desde_mapa_de_marcas(apps)
    if not settings.ES_AMBIENTE_DE_TESTING:
        normalizador = NormalizadorDeMarcas()
        normalizador.obtener_marcas_y_normalizarlas()


def _crear_desde_mapa_de_marcas(apps):
    Marca = apps.get_model("prospectos", "Marca")
    Alias = apps.get_model("prospectos", "Alias")
    aliases = []
    for nombre_de_marca, nombres_de_aliases in list(settings.MAPA_DE_MARCAS.items()):
        marca = _crear_marca(Marca, nombre_de_marca)
        aliases_de_la_marca = crear_aliases(Alias, [nombre_de_marca] + nombres_de_aliases, marca)
        aliases.extend(aliases_de_la_marca)
    Alias.objects.bulk_create(aliases)


def crear_aliases(alias_klass, nombres, marca):
    aliases = [alias_klass(_nombre=nombre, _marca=marca) for nombre in nombres]
    return aliases


def _crear_marca(marca_klass, nombre_de_marca):
    marca, created = marca_klass.objects.get_or_create(_codigo=nombre_de_marca.lower())
    if created:
        marca._nombre = nombre_de_marca
        marca._esta_normalizada = True
        marca._esta_habilitada = True
        marca.save()
    return marca


def undo_crear_marcas_normalizadas_con_aliases(apps, schema_editor):
    Marca = apps.get_model("prospectos", "Marca")
    marcas = Marca.objects.filter(_esta_normalizada=True)
    marcas.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0105_crear_marca_blanca'),
    ]

    operations = [
        migrations.RunPython(crear_marcas_normalizadas_con_aliases, undo_crear_marcas_normalizadas_con_aliases)
    ]