# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-11-13 18:17


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0095_auto_20171009_1457'),
    ]

    operations = [
        migrations.CreateModel(
            name='FotoDeModelo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_miniatura', models.URLField(blank=True, null=True)),
                ('_imagen', models.URLField(blank=True, null=True, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Marca',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_identificador', models.IntegerField(unique=True)),
                ('_nombre', models.Char<PERSON>ield(max_length=32)),
                ('_codigo', models.CharField(max_length=32)),
                ('_logo', models.URLField(blank=True, null=True)),
                ('_esta_habilitada', models.BooleanField(default=False)),
                ('_esta_normalizada', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Modelo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_identificador', models.IntegerField(unique=True)),
                ('_nombre', models.CharField(max_length=64)),
                ('_codigo', models.CharField(max_length=64)),
                ('_codigo_externo', models.CharField(blank=True, max_length=64, null=True)),
                ('_colores_disponibles', models.CharField(blank=True, max_length=256, null=True)),
                ('_equipamiento', models.TextField(blank=True, null=True)),
                ('_foto', models.URLField(blank=True, null=True)),
                ('_esta_normalizado', models.BooleanField(default=False)),
                ('_esta_habilitado', models.BooleanField(default=False)),
                ('_marca', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='_modelos', to='prospectos.Marca')),
            ],
        ),
        migrations.AddField(
            model_name='fotodemodelo',
            name='_modelo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='_otras_fotos', to='prospectos.Modelo'),
        ),
        migrations.AddField(
            model_name='prospecto',
            name='nuevas_marcas',
            field=models.ManyToManyField(blank=True, related_name='_prospectos', to='prospectos.Marca'),
        ),
        migrations.AddField(
            model_name='prospecto',
            name='nuevos_modelos',
            field=models.ManyToManyField(blank=True, related_name='_prospectos', to='prospectos.Modelo'),
        ),
    ]
