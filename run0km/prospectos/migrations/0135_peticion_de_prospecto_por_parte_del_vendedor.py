# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-12-13 13:27
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import prospectos.models.base


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0134_prospectoasignadodesdeasignacioninicial'),
    ]



    operations = [
        migrations.CreateModel(
            name='PeticionDeProspectoPorParteDelVendedor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_fecha_de_alta', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('_fecha_de_resolucion', models.DateTimeField(null=True)),
                ('_estado', models.CharField(choices=[('P', 'Pendiente'), ('E', 'Entre<PERSON>'), ('N', 'No entregado')], default='P', max_length=1)),
                ('_detalle', models.CharField(blank=True, default='', max_length=255)),
                ('_vendedor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='_peticiones_de_prospecto', to='vendedores.Vendedor')),
            ],
        ),
    ]
