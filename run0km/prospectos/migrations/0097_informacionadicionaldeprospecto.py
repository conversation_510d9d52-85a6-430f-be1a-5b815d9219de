# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-11-22 21:35


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0096_auto_20171113_1517'),
    ]

    operations = [
        migrations.CreateModel(
            name='InformacionAdicionalDeProspecto',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_ocupacion', models.TextField(blank=True, max_length=100)),
                ('_fecha_de_nacimiento', models.DateField(blank=True, null=True)),
                ('_sexo', models.TextField(blank=True, choices=[(b'Masculino', b'Masculino'), (b'Femenino', b'Femenino'), (b'Otro', b'Otro')], max_length=10)),
                ('_estado_civil', models.TextField(blank=True, choices=[(b'Soltero', b'Soltero'), (b'Comprometido', b'Comprometido'), (b'Casado', b'Casado'), (b'Divorciado', b'Divorciado'), (b'Viudo', b'Viudo'), (b'Otro', b'Otro')], max_length=11)),
                ('_documento', models.TextField(blank=True, max_length=25)),
                ('_empresa', models.TextField(blank=True, max_length=50)),
                ('_cargo', models.TextField(blank=True, max_length=40)),
                ('_cantidad_de_integrantes_familiares', models.IntegerField(blank=True, null=True)),
                ('_auto', models.TextField(blank=True, max_length=300)),
                ('_cliente', models.TextField(blank=True, max_length=150)),
                ('_hobby', models.TextField(blank=True, max_length=250)),
                ('_producto', models.TextField(blank=True, max_length=100)),
                ('_inicio_de_horario_de_contacto', models.TimeField(blank=True, null=True)),
                ('_fin_de_horario_de_contacto', models.TimeField(blank=True, null=True)),
                ('_dias_de_contacto', models.TextField(blank=True, max_length=8)),
                ('_clasificacion', models.TextField(blank=True, max_length=100)),
                ('_motivo', models.TextField(blank=True, max_length=100)),
                ('_submotivo', models.TextField(blank=True, max_length=100)),
                ('_valor_movil', models.IntegerField(blank=True, null=True)),
                ('_valor_cuota', models.FloatField(blank=True, null=True)),
                ('_color', models.TextField(blank=True, max_length=30)),
                ('_precio_venta_con_iva', models.IntegerField(blank=True, null=True)),
                ('_precio_de_lista', models.IntegerField(blank=True, null=True)),
                ('_fecha_de_venta', models.DateField(blank=True, null=True)),
                ('_prospecto', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='_informacion_adicional', to='prospectos.Prospecto')),
            ],
        ),
    ]
