    <div class="paginado">
         <input type="hidden" id="filtro_estado" value="{{ filtro_estado }}"/>
         <input type="hidden" id="filtro_llamado" value="{{ filtro_llamado }}"/>
        <div id="paginador" {% if cantidad_paginas < 2 %}style="display:none;"{% endif %}>
         <input type="hidden" id="max_pag_tot" value="{{ cantidad_paginas }}"/>
         <input type="text" id="num_pag" value="1"/>
         <p class="paginas">de <span id="max_pag">{{ cantidad_paginas }}</span></p>
         <a class="anterior" href="javascript:void(0)" onclick="pagAnterior();"></a>
         <a class="siguiente" href="javascript:void(0)" onclick="pagSiguiente();"></a>
        </div>
         <p>Total de Registros: <span id="cant_reg">{{ cantidad_total }}</span></p>
    </div>
