{% extends "resumen/resumen-layout.html" %}
{% load permisos %}
{% load static from staticfiles %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript">
        var resumen_data_url = "{% url "resumen_data" %}";
        var maneja_equipos = true;
        var supervisores = {{json_supervisores|safe}};
        var campanias_por_supervisor = {{json_campanias|safe}};
        var nombres_de_campanias = {{ json_nombres_campanias|safe }};
        $(InicializarFiltrosDeFechas);
        $(inicializarSelectorSupervisor);
        $(inicializarSelectorEquipo);
        $(function () {
            $("#fecha_desde").datepicker({dateFormat: "yy-mm-dd"});
            $("#fecha_hasta").datepicker({dateFormat: "yy-mm-dd"});
            filtrarDataResumen();
        });
    </script>
{% endblock %}

{% block contenido-resumen %}
    <div id="resumen-de-prospectos" class="panel">
        <div class="cuerpo collapse show" id="cuerpo-resumen-de-prospectos">
            <div class="container">
                <form id="form_filtro" method="GET" action="{% url "prospectos" %}">
                    <div class="row">
                        <div class="col-5">
                            <div class="form-group row">
                                <label class="supervisor col-form-label col-form-label-sm col-3"
                                   for="supervisor">Supervisor</label>
                                <div class="col-9">
                                    <select name="supervisor" class="form-control form-control-sm"
                                        id="supervisor">
                                    <option value="todos_los_supervisores">Todos</option>
                                    {% for supervisor in supervisores %}
                                        <option value="{{ supervisor.id }}">{{ supervisor.full_name }}</option>
                                    {% endfor %}
                                </select>
                                </div>
                            </div>
                        </div>
                        {% if user.role.puede_acceder_a_los_proveedores_de_datos %}
                        <div class="col-5">
                         <div class="form-group row">
                                <label for="distribuidor"
                                       class="col-form-label col-form-label-sm col-3">Proveedor</label>
                                <div class="col-9">
                                    <select name="distribuidor" class="form-control form-control-sm"
                                        id="distribuidor">
                                        <option value="">Todos</option>
                                        {% for distribuidor in distribuidores %}
                                            <option value="{{ distribuidor.id }}">{{ distribuidor.name }}</option>
                                        {% endfor %}
                                </select>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="row">
                        <div class="col-5">
                            <div class="form-group row">
                                <label for="equipo"
                                       class="col-form-label col-form-label-sm col-3">Equipo</label>
                                <div class="col-9">
                                    <select class="agencia form-control form-control-sm" name="equipo"
                                            id="equipo">
                                        <option value="">Todos</option>
                                        {% for equipo in equipos %}
                                            <option value="{{ equipo.id }}">{{ equipo.nombre }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="tipo_origen"
                                       class="col-form-label col-form-label-sm col-3">Calidad</label>
                                <div class="col-9">
                                    <select class="tipo_origen form-control form-control-sm" name="tipo_origen"
                                            id="tipo_origen">
                                        <option value="">Todos</option>
                                        {% for tipo_de_origen in tipos_de_origen %}
                                            <option value="{{ tipo_de_origen.codigo }}">{{ tipo_de_origen.nombre }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                               <label for="tipo-de-fecha" class="col-form-label col-form-label-sm col-3">Fecha de</label>
                                <div class="col-9">
                                    <select class="agencia form-control form-control-sm" name="tipo-de-fecha" id="tipo-de-fecha">
                                        <option value="fecha-del-dato">Creación</option>
                                        <option value="fecha-de-asignacion-de-responsable">Ingreso al sistema</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                               <label for="marca" class="col-form-label col-form-label-sm col-3">Marca</label>
                                <div class="col-9">
                                    <select class="agencia form-control form-control-sm" name="marcas" id="marcas">
                                        <option value="">Todas las Marcas</option>
                                        {% for marca in marcas %}
                                        <option value="{{ marca.id }}">{{ marca.nombre }}</option>
                                    {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-5">
                        <div class="form-group row">
                            <label for="vendedor"
                                   class="fecha_desde col-form-label col-form-label-sm col-3">Vendedor</label>
                            <div class="col-9">
                                <select class="agencia form-control form-control-sm" name="vendedor"
                                        id="vendedor">
                                    <option value="">Todos</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="campania"
                                   class="fecha_desde col-form-label col-form-label-sm col-3">Campaña</label>
                            <div class="col-9">
                                <select class="campania form-control form-control-sm" name="campania"
                                        id="campania">
                                    <option value="">Todas</option>
                                    {% for campania in campanias %}
                                        <option value="{{ campania.id }}">{{ campania.nombre_descriptivo }}
                                            ({{ campania.nombre_origen }})
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="fecha_desde col-form-label col-form-label-sm col-3" for="fecha_desde">Entre</label>
                            <div class="col-9 controls form-inline">
                                <input type="text" name="fecha_desde" id="fecha_desde" autocomplete="off" class="form-control form-control-sm col-5"/>
                                <label class="fecha_hasta col" for="fecha_hasta">y</label>
                                <input type="text" name="fecha_hasta" id="fecha_hasta" autocomplete="off" class="form-control form-control-sm col-5"/>
                            </div>
                        </div>
                    </div>
                        <div class="col-2">
                            <input type="hidden" name="filter_estado" id="filter_estado" value=""/>
                            <input class="boton-default" type="button" value="buscar" onclick="filtrarDataResumen()"/>
                            <div id="spin_holder" class="spin-holder spinner-position-centered"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="datos-centro">
                <div class="cantidad entero">
                    <a class="ver"><p id="cant_prospectos"></p></a>
                    <div id="spinner_prospectos" class="spin-holder"></div>
                </div>
                <div class="cantidad entero">
                    <a class="ver"><p id="cant_ventas"></p></a>
                    <div id="spinner_ventas" class="spin-holder"></div>
                </div>
                <div class="cantidad ranking">
                    <p class="numerador" id="ranking_num"></p>
                    <p id="ranking_den"></p>
                    <div id="spinner_ranking" class="spin-holder"></div>
                </div>
            </div>
        </div>
    </div>
    <div id="bandeja-de-entrada" class="panel">
        <div class="titulo" data-toggle="collapse" data-target="#cuerpo-bde" aria-expanded="true"
             aria-controls="cuerpo-bde">Bandeja de entrada
        </div>
        <div class="cuerpo collapse show" id="cuerpo-bde">
            <div class="sms-llamados">
                <p>Prospectos Activos: <span id="cant_activos"></span></p>
            </div>
            <div class="sms-llamados">
                <p>Prospectos Nuevos: <span id="cant_nuevos"></span></p>
            </div>
        </div>
    </div>
{% endblock %}

