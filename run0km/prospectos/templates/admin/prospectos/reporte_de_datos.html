{% extends "admin/base_site.html" %}
{% load static from staticfiles %}
{% load prospectos_utils %}

{% block extrahead %}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/changelists.css' %}?v={{ version }}"/>
    <link rel="stylesheet" type="text/css" href="{% static 'css/runkm-admin.css' %}?v={{ version }}"/>
{% endblock %}

{% block title %}Reporte de Datos Entregados.{% endblock %}

{% block content %}
    <div id="content-main">
        <h1>Reporte de datos</h1>

        <h3>Seleccione mes y año del reporte</h3>

        <form method="GET">
            <fieldset class="module aligned ">
                {{ form.as_p }}
                <input type="submit" value="Filtrar"/>
            </fieldset>
        </form>
    </div>

    <ul class="reporte-datos">
        {% for concesionaria, supervisores in reporte_de_datos.datos.items %}
            <li class="concesionaria {% if forloop.last %}last{% endif %}">
                {{ concesionaria }}
                <ul>
                    {% for supervisor, marcas in supervisores.items %}
                        <li class="supervisor {% if forloop.last %}last{% endif %}">
                            {{ supervisor }}
                            <ul>
                                {% for marca, categorias in marcas.items %}
                                    <li class="marca {% if forloop.last %}last{% endif %}">
                                        {{ marca }}
                                        <ul>
                                            {% for categoria, datos in categorias.items %}
                                                <li class="categoria {% if forloop.last %}last{% endif %}">
                                                    {{ categoria }}
                                                    <table>
                                                        <tr>
                                                            <td>&nbsp;</td>
                                                            <td>Cantidad de Prospectos</td>
                                                            <td>Valor</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Entregados</td>
                                                            <td>{{ datos.entregados }}</td>
                                                            <td>{{ datos.entregados_valor|como_dinero }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Rechazados</td>
                                                            <td>{{ datos.rechazados }}</td>
                                                            <td>{{ datos.rechazados_valor|como_dinero }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>Total</td>
                                                            <td>{{ datos.total }}</td>
                                                            <td>{{ datos.total_valor|como_dinero }}</td>
                                                        </tr>
                                                    </table>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                {% endfor %}
                                {% with total_supervisor=reporte_de_datos.total|key_value:supervisor %}
                                    <p>Total prospectos {{ supervisor }}: {{ total_supervisor.0 }}</p>
                                    <p>Total valor {{ supervisor }}: {{ total_supervisor.1|como_dinero }}</p>
                                {% endwith %}
                            </ul>
                        </li>
                    {% endfor %}
                </ul>
                {% with total_concesionaria=reporte_de_datos.total|key_value:concesionaria %}
                    {% for categoria, total_categoria in total_concesionaria.items %}
                        <p>Total prospectos {{ concesionaria }} - {{ categoria }}: {{ total_categoria.0 }}</p>
                        <p>Total valor {{ concesionaria }} - {{ categoria }}: {{ total_categoria.1|como_dinero }}</p>
                    {% endfor %}
                {% endwith %}
            </li>
        {% endfor %}

        {% if reporte_de_datos.total|length > 0 %}
            {% with total_por_categorias=reporte_de_datos.total|key_value:"total_categorias" %}
                {% for categoria, datos_total in total_por_categorias.items %}
                    <p>Total prospectos {{ categoria }}: {{ datos_total.0 }}</p>
                    <p>Total valor {{ categoria }}: {{ datos_total.1|como_dinero }}</p>
                {% endfor %}
            {% endwith %}
        {% endif %}
    </ul>
{% endblock %}

