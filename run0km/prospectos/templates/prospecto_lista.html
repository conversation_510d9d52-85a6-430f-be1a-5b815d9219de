{% load prospectos_utils %}
{% load rubro_extras %}
<div class="prospecto">
    <div class="barra-titulo {{ prospecto|clase_por_estado }}" id="prospecto-{{ prospecto.id }}">



        <p>
            <a class="nombre-barra-titulo" id="nombre_{{ prospecto.id }}"
               onclick="gtag('event', 'ver_detalle_de_prospecto', {});"
               href="{% url 'prospecto' prospecto.id %}">{{ prospecto.nombre_completo }}</a>
            {% with chat_convertido=prospecto.chat_de_ventas_convertido %}
                {% if chat_convertido %}
                    {% with chat=chat_convertido.chat %}
                        <a href=#
                           onclick="servicioDeChatDeVentas.iniciarChat({{ chat.id }}, {% if chat.activo %}true{% else %}false{% endif %})"
                           title="Click para iniciar el Chat">
                            <img style="float: right; padding-left: 8px; margin-top: -2px; "
                                 src="{{ STATIC_URL }}img/icono-chat-chico.png">
                        </a>
                    {% endwith %}
                {% endif %}
            {% endwith %}
            <a class="nombre_alternativo"
               href="javascript:gtag('event', 'lista_cambiar_nombre_de_prospecto', {}); askExtraData('name', '{{ prospecto.id }}')"
               title="Cambiar Nombre">&nbsp</a>
        </p>
        {% if not prospecto.en_proceso %}
            <p class="finalizado">Prospecto finalizado</p>
            {% if prospecto.finalizado %}
                {% with descripcion=prospecto.finalizacion.motivo.descripcion %}
                    {% if descripcion %}
                        <p>({{ descripcion }})</p>
                    {% endif %}
                {% endwith %}
            {% endif %}
            {% if not prospecto.vendido %}
                <a onclick="askReactivarSeguimiento('{{ prospecto.id }}');" href="javascript:void(0)">
                    <img class="reactivar-seguimiento" src="{{ STATIC_URL }}img/find.png"
                         title="Reactivar seguimiento"/>
                </a>
            {% endif %}
        {% endif %}
        {% if user.vendedor|tiene_siguiente_repetido:prospecto %}
            <a class="enlace-a-prospectos-repetidos"
               href="javascript:redireccionarASiguienteRepetido('{{ prospecto.id }}')" title="Prospectos Repetidos">&nbsp</a>
            <div style="clear: both;"></div>
        {% endif %}
    </div>
    <div class="datos">

        {% if not prospecto.vendedor.id == user.vendedor.id %}
            <div class="cuadro-dato">
                <p><span>Vendedor:</span></p>

                <p class="dato">{{ prospecto.vendedor.user.get_full_name }}</p>
            </div>
        {% endif %}

        {% if prospecto.campania %}
            <div class="cuadro-dato">
                <p><span>Campaña:</span></p>
                {% if prospecto.campania.detalle %}
                    <p>
                        <a href="{% url 'campania' prospecto.campania.id %}"
                           onclick="gtag('event', 'lista_campaña', {});">
                            {{ prospecto.campania.nombre_descriptivo }}
                        </a>
                    </p>
                {% else %}
                    <p class="dato">{{ prospecto.campania.nombre_descriptivo }}</p>
                {% endif %}
                <span class="stars"><span
                        style="width: {% valor_calidad_campania prospecto.campania %}px;"></span></span>
            </div>
        {% endif %}
        <div class="cuadro-dato">
            <p>
                <span>Fecha:</span>
            </p>

            <p class="dato">{{ prospecto.fecha|default:"N/D" }}</p>
        </div>
        <div class="cuadro-dato">
            <p><span>Mensaje:</span></p>

            <p class="dato-largo">{{ prospecto.mensaje|default:"N/D" }}</p>
        </div>
        {% if not prospecto.vendedor.tiene_version_limitada %}
            <div class="cuadro-dato">
                <p><span>Teléfonos:</span></p>

                <p class="datos-extras" id="telefono_{{ prospecto.id }}">{{ prospecto|telefonos_extra|safe }}</p>
                {% if prospecto.telefono %}
                    <p id="tel_{{ prospecto.id }}"
                       class="datos-extras">
                          <span title="{% if prospecto.telefono_activo %}Eliminar{% else %}Reestablecer{% endif %}"
                                  {% if prospecto.telefono_activo %}
                                onclick="gtag('event', 'lista_eliminar_telefono', {}); ToggleInfo('tel','{{ prospecto.id }}');"
                                  {% else %}
                                onclick="gtag('event', 'lista_reestablecer_telefono', {}); ToggleInfo('tel','{{ prospecto.id }}');"
                                  {% endif %}
                                {% if not prospecto.telefono_activo %}class="desactivado"{% endif %}>
                            {{ prospecto.telefono }}
                          </span>
                        <span class="boton-servicio-llamado hidden" data-prospecto-id="{{ prospecto.id }}"
                              title="Llamar por VoIP"></span>
                    </p>
                {% endif %}
                <a href="javascript:gtag('event', 'lista_agregar_telefono', {}); askExtraData('tel', '{{ prospecto.id }}')">Agregar</a>

                <div id="spin_tel_{{ prospecto.id }}" class='spin-holder'></div>
            </div>
        {% endif %}
        {% if not rubro|tiene_excluido_el_campo:"_email" %}
        <div class="cuadro-dato">
            <p><span>E-mails:</span></p>

            <p class="datos-extras" id="email_{{ prospecto.id }}">{{ prospecto|emails_extra|safe }}</p>
            {% if prospecto.email %}
                <p id="mail_{{ prospecto.id }}"
                   title="{% if prospecto.email_activo %}Eliminar{% else %}Reestablecer{% endif %}"
                   class="datos-extras{% if not prospecto.email_activo %} desactivado{% endif %}"
                        {% if prospecto.email_activo %}
                   onclick="gtag('event', 'lista_eliminar_mail', {}); ToggleInfo('mail','{{ prospecto.id }}');"
                        {% else %}
                   onclick="gtag('event', 'lista_reestablecer_mail', {}); ToggleInfo('mail','{{ prospecto.id }}');"
                        {% endif %}
                >
              <span>
                {{ prospecto.email }}
              </span>
                </p>
            {% endif %}
            <a href="javascript:gtag('event', 'lista_agregar_mail', {}); askExtraData('mail', '{{ prospecto.id }}')">Agregar</a>

            <div id="spin_mail_{{ prospecto.id }}" class='spin-holder'></div>
        </div>
        {% endif %}
        {% if not rubro|tiene_excluido_el_campo:"_provincia" %}
            {% if prospecto.provincia %}
                <div class="cuadro-dato">
                    <p><span>Provincia:</span></p>

                    <p class="dato">{{ prospecto.obtener_provincia }}</p>
                </div>
            {% endif %}
        {% endif %}
        {% if not rubro|tiene_excluido_el_campo:"_localidad" %}
            {% if prospecto.localidad %}
                <div class="cuadro-dato">
                    <p><span>Localidad:</span></p>

                    <p class="dato">{{ prospecto.obtener_localidad }}</p>
                </div>
            {% endif %}
        {% endif %}

        <div class="cuadro-dato">
            <p><span>{{ rubro.etiqueta_marca.title }}:</span></p>
            <p id="marcas-para-prospecto-{{ prospecto.id }}" class="dato">
                {{ prospecto.obtener_marca.nombre }}
            </p>
            {% if rubro.puede_gestionar_marcas %}
            <a class="btn-agregar-marca-modelo" title="Agregar o Borrar {{ rubro.etiqueta_marca.title }}"
               href="javascript:gtag('event', 'lista_editar_marca', {}); abrirModalParaMarcasOModelos('{{ prospecto.id }}', 'marca', '{{ rubro.etiqueta_marca.title }}')"></a>
            {% endif %}
        </div>

        {% if rubro.tiene_modelo %}
        <div class="cuadro-dato">
            <p><span>{{ rubro.etiqueta_modelo.title }}:</span></p>
            <p id="modelos-para-prospecto-{{ prospecto.id }}" class="dato">{{ prospecto.modelos_como_string }}</p>
            {% if rubro.puede_gestionar_modelos %}
            <a class="btn-agregar-marca-modelo" title="Agregar o Borrar {{ rubro.etiqueta_modelo.title }}"
               href="javascript:gtag('event', 'lista_editar_modelo', {}); abrirModalParaMarcasOModelos('{{ prospecto.id }}', 'modelo', '{{ rubro.etiqueta_modelo.title }}')"></a>
            {% endif %}
            <div id="spin_propuestas_{{ prospecto.id }}" class='spin-holder'></div>
        </div>
        {% endif %}

        <div class="cuadro-dato">
            <p><span>Próximo contacto:</span></p>

            <p class="dato llamado" id="llamado_{{ prospecto.id }}">{{ prospecto.llamado.fecha }}
                {% if prospecto.llamado %}
                    <a class="borrar-llamado" title="Borrar Llamado" href="javascript:void(0);"
                       onclick="gtag('event', 'lista_borrar_llamada', {}); askBorrarLlamado({{ prospecto.id }});"></a>
                {% endif %}
            </p>
            <a href="javascript:void(0);"
               onclick="gtag('event', 'lista_asignar_proxima_llamada', {}); askNuevoLlamado({{ prospecto.id }});">
                Asignar Próxima llamada
            </a>

            <div id="spin_llamado_{{ prospecto.id }}" class='spin-holder'></div>
        </div>
        {% if not rubro|tiene_excluido_el_campo:"_tarjetas_de_credito" %}
        <div class="cuadro-dato">
            <p><span>Tarjetas de Crédito:</span></p>
            <p id="tarjetas-para-prospecto-{{ prospecto.id }}" class="dato-de-tarjeta-de-credito">
                {% if prospecto.tarjetas_de_credito %}
                    {% for tarjeta in prospecto.tarjetas_de_credito %}
                        <a href="javascript:void(0)">
                            <img style="float: left;" id="tarjeta-de-credito-{{ tarjeta.id }}"
                                 name="{{ tarjeta.marca.nombre }}"
                                 title="{{ tarjeta.marca.nombre }}" src="{{ tarjeta.marca.logo.url }}"
                                 alt="{{ tarjeta.marca.nombre }}" height="35px" width="35px"
                                 onclick="habilitarODeshabilitarTarjetaDeCredito({{ tarjeta.id }});"
                                 class="{% if not tarjeta.esta_habilitada %}tarjeta-deshabilitada{% endif %}">
                        </a>
                    {% endfor %}
                {% endif %}
            </p>
            <a href="javascript:agregarTarjetaDeCredito('tarjeta', '{{ prospecto.id }}')">Agregar</a>
        </div>
        <div style="display: none;">
            <div style="display: flex; justify-content: space-around; align-items: center;"
                 id="tarjetas-de-credito-dialog-para-prospecto-{{ prospecto.id }}">
                <div id="AMEX">
                    <input id="input-AMEX-{{ prospecto.id }}" name="AMEX" type="checkbox"/> <img name="AMEX"
                                                                                                 class="tarjeta-de-credito-small"
                                                                                                 src="{{ STATIC_URL }}img/amex.png">
                </div>
                <div id="VISA">
                    <input id="input-VISA-{{ prospecto.id }}" name="VISA" type="checkbox"/> <img name="VISA"
                                                                                                 class="tarjeta-de-credito-small"
                                                                                                 src="{{ STATIC_URL }}img/visa.png">
                </div>
                <div id="MAESTRO">
                    <input id="input-MAESTRO-{{ prospecto.id }}" name="MAESTRO" type="checkbox"/> <img name="MAESTRO"
                                                                                                       class="tarjeta-de-credito-small"
                                                                                                       src="{{ STATIC_URL }}img/maestro.png">
                </div>
                <div id="MASTER">
                    <input id="input-MASTER-{{ prospecto.id }}" name="MASTER" type="checkbox"/> <img name="MASTER"
                                                                                                     class="tarjeta-de-credito-small"
                                                                                                     src="{{ STATIC_URL }}img/mastercard.png">
                </div>
                <div id="DINERS">
                    <input id="input-DINERS-{{ prospecto.id }}" name="DINERS" type="checkbox"/> <img name="DINERS"
                                                                                                     class="tarjeta-de-credito-small"
                                                                                                     src="{{ STATIC_URL }}img/diners.png">
                </div>
                <div id="MERCADOPAGO">
                    <input id="input-MERCADOPAGO-{{ prospecto.id }}" name="MERCADOPAGO" type="checkbox"/> <img name="MERCADOPAGO"
                                                                                                     class="tarjeta-de-credito-small"
                                                                                                     src="{{ STATIC_URL }}img/mercadopago.png">
                </div>
            </div>
        </div>
        {% endif %}

        {% for informacion in prospecto.informacion_de_redes_sociales.all %}
            {% if informacion.fue_validada and not informacion.es_telefono_o_email %}
                {% include "redes_sociales_dato.html" %}
            {% endif %}
        {% endfor %}
{#        <div class="cuadro-dato">#}
{#        <div class='tagHandler'>#}
{#            <div id="tag-input-message">#}
                {#                            <p style="color: darkgreen; font-size: medium"><strong>Escriba la etiqueta que quiere crear:</strong></p>#}
{#                <p>#}
{#                    <strong style="color: #2d6fb6;line-height: 11px;">Etiquetas:</strong></p>#}
{#            </div>#}
{#            <ul class="tagHandlerContainer" id="tag_handler_{{ prospecto.id }}">#}
{#                ver si con style:"position: absolute;#}
{#  left: 13%;#}
{#  top: -200%;#}
{#  transform: translateY(-50%);#}
{#  width: 89%;" se puede lograr hacer que este esto en un solo cuadro #}
{#            </ul>#}
{#        </div>#}
{#        </div>#}

            <div class='tagHandler'>
            <div id="tag-input-message" style="display: none;">
                {#                            <p style="color: darkgreen; font-size: medium"><strong>Escriba la etiqueta que quiere crear:</strong></p>#}
                <p style="padding: 10px; background-color: lightgreen; color: darkgreen; font-size: small">
                    <strong>Escriba la etiqueta que quiere crear:</strong></p>
            </div>
            <ul class="tagHandlerContainer" id="tag_handler_{{ prospecto.id }}">
            </ul>
        </div>

    </div>
    <div class="prospecto-leftbar">

        {% if user.concesionaria.configuracion_de_servicios.foto_de_whatsapp_habilitada and not 'generic-user' in prospecto.avatar %}
                    <div align="center">
                        <style> .img-hover { transition: transform 0.3s ease; } .img-hover:hover { transform: scale(2.5); } </style>
                        <p  style="width: 50px; height: 50px">
                            <img class="img-hover"  src="{{prospecto.avatar}}" onerror="this.onerror=null;this.src='{{ STATIC_URL}}img/generic-user.png';" style="border-radius: 25%; object-fit: cover; width: 100%; height: 100%;">
                        </p>
                </div>
        {% endif %}
        <div class="comentario" style="font-size: 12px;">
            {% include "comentario-panel.html" %}
            <ul class="ultimos-comentarios" id="ultimos_comentarios_{{ prospecto.id }}">
                {% for comentario in comentarios|slice:":5" %}
                    <li title="{{ comentario.comentario }}" class="comentario-manual">
                        {{ comentario.comentario|truncatechars:45 }}
                    </li>
                {% endfor %}
            </ul>
            <div class="historial-de-comentarios-bar">
                    <input class="btn-historial" type="button" value="Ver Historial"
                   title="Click para ver el historial de comentarios"
                   onclick="gtag('event', 'lista_ver_historial_comentarios', {}); pedirComentarios({{ prospecto.id }});">
            </div>
        </div>
        <hr>
        <div class="botones-prospecto-flex" style="display: flex; width: 400px; flex-wrap: wrap">
            {% if prospecto.en_proceso %}
                {% if prospecto.vendedor == user.vendedor %}
                    <a class="boton-opcion-2018" href="javascript:void(0)"
                       onclick="gtag('event', 'lista_ver_mensajes', {}); abrir_conversacion({{ prospecto.id }}, 'NORMAL', '{% url 'conversacion-unificada' '0000' 'TIPO' %}');">
                        <div class='img-boton boton-chat'></div>
                        Mensajes
                    </a>
                {% endif %}
                <a class="boton-opcion-2018" onclick="gtag('event', 'lista_finalizar_seguimiento', {})" href="{% url 'finalizar_seguimiento' prospecto.id %}">
                    <div class="img-boton carpeta-archivar"></div>
                    Finalizar Seguimiento
                </a>
            {% endif %}
            {% if not prospecto.vendido %}
                <a class="boton-opcion" onclick="gtag('event', 'lista_cargar_venta', {})" href="{% url 'cargar_venta' prospecto.id %}">
                    <div class='img-boton boton-venta'></div>
                    Cargar Venta
                </a>
            {% endif %}
            {% if user.vendedor.configuracion_servicios.redes_habilitado and prospecto.tiene_informacion_de_redes_sociales and prospecto.nombre_coincide_parcialmente_con_redes_sociales %}
                <a class="boton-opcion-2018"
                   onclick="gtag('event', 'lista_ver_redes_sociales', {});"
                   href="javascript:pedirRedesSociales({{ prospecto.id }})">
                    <div class='img-boton boton-ver-redes-sociales'></div>
                    <div id="spin_redes_{{ prospecto.id }}" class='spin-holder'></div>
                    Ver Redes Sociales
                </a>
            {% endif %}
            <a class="boton-opcion-2018" href="javascript:void(0);"
               onclick="gtag('event', 'lista_enviar_propuesta', {}); pedirPropuestas('{{ prospecto.id }}', '{{ prospecto.vendedor.cargo }}');">
                <div class='img-boton envio-de-propuesta'></div>
                Enviar Propuesta
            </a>
            <a class="boton-opcion-2018" href="javascript:void(0)"
               onclick="gtag('event', 'lista_apretar_boton_agregar_tag', {}); agregarTag({{ prospecto.id }});">
                <img src="{{ STATIC_URL }}img/boton-agregar-etiqueta.png"/>
                Agregar Etiqueta
            </a>
        </div>
        {% if user.role.puede_transferir_prospectos_concesionaria %}
        <hr>
        <div class="transferencia-prospecto">
            <label>Transferir prospecto a:</label>
            <select id="vendedor_destinatario_id_{{ prospecto.id }}">
                <option value="">Seleccione un Vendedor</option>
                {% for vendedor in vendedores %}
                    <option value="{{ vendedor.id_vendedor }}">{{ vendedor.nombre }}</option>
                {% endfor %}
            </select>
            <button class="transferir-prospecto" onclick="transferirProspecto('{{ prospecto.id }}')">Transferir</button>
        </div>
        {% endif %}
    </div>

    <div class="ajustar"></div>

</div>