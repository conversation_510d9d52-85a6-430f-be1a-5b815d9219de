from datetime import time

from django import forms
from django.conf import settings
from django.forms.utils import ErrorList
from django.utils import timezone

from prospectos.models import InformacionAdicionalDeProspecto
from prospectos.utils.opciones import ModoDeSelecionDeProspectosMapper
from prospectos.views.widgets import DeliveryTextWidget, DeliveryNumberWidget, DeliverySelectWidget, \
    DeliveryDateWidget, DeliveryMultiSelectWidget


class ModelConGuionBajoForm(forms.ModelForm):
    def __init__(self, instance):
        super(ModelConGuionBajoForm, self).__init__(instance=instance)
        for field_name in list(self.fields.keys()):
            if field_name[0] == '_':
                self.fields[field_name[1:]] = self.fields[field_name]
                self.initial[field_name[1:]] = self.initial[field_name]


class InformacionAdicionalDeProspectoForm(ModelConGuionBajoForm):

    def __init__(self, info_adicional, nombres_de_campos):
        super(InformacionAdicionalDeProspectoForm, self).__init__(info_adicional)
        self._remover_campos(nombres_de_campos)

    def _remover_campos(self, nombres_de_campos):
        if nombres_de_campos:
            campos_a_remover = (set(self.fields.keys()).difference(nombres_de_campos))
            for field_name in campos_a_remover:
                try:
                    del self.fields[field_name]
                except KeyError:
                    pass

    class Meta:
        DIAS_CHOICES = [
            ('LUNES', 'Lunes'),
            ('MARTES', "Martes"),
            ('MIERCOLES', "Miercoles"),
            ('JUEVES', "Jueves"),
            ('VIERNES', "Viernes"),
            ('SABADO', "Sabado"),
            ('DOMINGO', "Domingo"),
        ]
        HORARIOS_DE_CONTACTO = [("", "---------")] +[(str(i), i) for i in range(0, 24)]
        model = InformacionAdicionalDeProspecto
        exclude = InformacionAdicionalDeProspecto.nombres_invalidos_de_carga()
        widgets = {
            '_sexo': DeliverySelectWidget(),
            '_estado_civil': DeliverySelectWidget(),
            '_fecha_de_venta': DeliveryDateWidget(select_width=90, select_height=25),
            '_fecha_de_nacimiento': DeliveryDateWidget(years=[i for i in range(1930, 2017)], select_width=90,
                                                       select_height=25),
            '_inicio_de_horario_de_contacto': DeliverySelectWidget(choices=HORARIOS_DE_CONTACTO),
            '_fin_de_horario_de_contacto': DeliverySelectWidget(choices=HORARIOS_DE_CONTACTO),
            '_dias_de_contacto': DeliveryMultiSelectWidget(choices=DIAS_CHOICES, select_width=175, select_height=135),
            '_ocupacion': DeliveryTextWidget(),
            '_documento': DeliveryTextWidget(),
            '_empresa': DeliveryTextWidget(),
            '_cargo': DeliveryTextWidget(),
            '_auto': DeliveryTextWidget(),
            '_cliente': DeliveryTextWidget(),
            '_hobby': DeliveryTextWidget(),
            '_producto': DeliveryTextWidget(),
            '_clasificacion': DeliveryTextWidget(),
            '_motivo_de_rechazo_de_compra': DeliveryTextWidget(),
            '_submotivo_de_rechazo_de_compra': DeliveryTextWidget(),
            '_color': DeliveryTextWidget(),
            '_precio_de_lista': DeliveryNumberWidget(),
            '_precio_de_venta_con_iva': DeliveryNumberWidget(),
            '_valor_de_cuota': DeliveryNumberWidget(),
            '_valor_movil': DeliveryNumberWidget(),
            '_cantidad_de_integrantes_familiares': DeliveryNumberWidget()
        }


class FiltroRangoDeFechaForm(forms.Form):
    fecha_desde = forms.DateField(
        widget=forms.DateInput(format='%Y-%m-%d', attrs={'autocomplete': 'off'}),
        input_formats=['%Y-%m-%d'], required=False, label='Desde')
    fecha_hasta = forms.DateField(
        widget=forms.DateInput(format='%Y-%m-%d', attrs={'autocomplete': 'off'}),
        input_formats=['%Y-%m-%d'], required=False, label='Hasta')

    def __init__(self, *args, **kwargs):
        super(FiltroRangoDeFechaForm, self).__init__(*args, **kwargs)
        self.initial['fecha_desde'] = timezone.now()-timezone.timedelta(days=30)
        self.initial['fecha_hasta'] = timezone.now()

    def rango_seleccionado(self):
        if not self.is_valid():
            raise ValueError('El formulario debe ser valido para poder acceder al rango')

        fecha_hasta = self.cleaned_data.get('fecha_hasta') or timezone.now()
        fecha_desde = self.cleaned_data.get('fecha_desde') or fecha_hasta - timezone.timedelta(days=30)
        fecha_hasta = timezone.make_aware(timezone.datetime.combine(fecha_hasta, time.max))
        fecha_desde = timezone.make_aware(timezone.datetime.combine(fecha_desde, time.min))
        return fecha_desde, fecha_hasta


class ProgramarLlamadoAutomaticamenteForm(forms.Form):
    # TODO: ver si tiene sentido la herencia con FiltroRangoDeFechaForm

    modo_de_seleccion = forms.MultipleChoiceField(
        choices=ModoDeSelecionDeProspectosMapper.opciones(), widget=forms.CheckboxSelectMultiple(), required=False
    )
    fecha_desde = forms.DateField(
        widget=forms.DateInput(
            format='%Y-%m-%d',
            attrs={'class': 'form-control date', 'id': 'agendar_fecha_desde', 'autocomplete': 'off'}),
        initial=timezone.now().date() - timezone.timedelta(days=7)
    )
    fecha_hasta = forms.DateField(
        widget=forms.DateInput(
            format='%Y-%m-%d',
            attrs={'class': 'form-control date', 'id': 'agendar_fecha_hasta', 'autocomplete': 'off'}),
        initial=timezone.now().date()
    )
    cantidad_maxima_de_prospectos_a_agendar = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '20'}),
        initial=20, label="Cantidad a agendar"
    )
    cantidad_de_llamados_diarios = forms.IntegerField(
        min_value=1, widget=forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '30'}),
        initial=settings.SELECTOR_HORARIOS_CANTIDAD_DE_LLAMADOS_DIARIOS, label='Cantidad de Llamados Diarios'
    )

    def __init__(self, cantidad_maxima_de_prospectos_a_agendar, *args, **kwargs):
        self.base_fields['cantidad_maxima_de_prospectos_a_agendar'].max_value = cantidad_maxima_de_prospectos_a_agendar
        super(ProgramarLlamadoAutomaticamenteForm, self).__init__(*args, **kwargs)

    def rango_seleccionado(self):
        if not self.is_valid():
            raise ValueError('El formulario debe ser valido para poder acceder al rango')

        fecha_hasta = self.cleaned_data.get('fecha_hasta') or timezone.now()
        fecha_desde = self.cleaned_data.get('fecha_desde') or fecha_hasta - timezone.timedelta(days=30)
        fecha_hasta = timezone.make_aware(timezone.datetime.combine(fecha_hasta, time.max))
        fecha_desde = timezone.make_aware(timezone.datetime.combine(fecha_desde, time.min))
        return fecha_desde, fecha_hasta

    def clean_modo_de_seleccion(self):
        nombres = self.cleaned_data.get('modo_de_seleccion')
        return ModoDeSelecionDeProspectosMapper().seleccion_para(nombres)
