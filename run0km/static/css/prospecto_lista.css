
.titulo2 {
    height: 12px;
    position: relative;
}

.titulo2 img {
    position: absolute;
    left: 20px;
}

.titulo2 div {
    width: 292px;
    position: absolute;
    left: 20px;
    top: 13px;
    text-align: center;
}

.encabezado {
    margin-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
}

.selectize-input {
    width: 300px;
    height: 30px;
    font-size: 12px;
    padding: 0;
    margin-top: 5px;
    border: 1px #ddd solid;
    color: #555;
}

.detalle-de-propuesta {
    display: inline-block;
    width: 100%;
    min-height: 120px;
    padding: 5px 10px 5px 12px;
    border: 1px #ddd solid;
    border-radius: 10px;
    box-sizing: border-box;
}

.detalle-de-propuesta p {
    display: inline-block;
    color: #2d6fb6;
    line-height: 11px;
}

.detalle-de-propuesta p span {
    font-weight: 700;
}

.dato-de-propuesta {
    padding: 5px 10px 4px 10px;
}

.dato-de-tarjeta-de-credito{
    margin-bottom: -10px;
    margin-top: -10px;

}

.descripcion-propuesta-titulo {
    display: block;
    margin-bottom: 0;
    padding: 5px 10px 5px 20px;
    font-size: 14px;
    color: #2d6fb6;
    line-height: 16px;
    text-align: left;
    font-weight: bold;
}


/*.select-box {*/
  /*cursor: pointer;*/
  /*position : relative;*/
  /*!*max-width:  20em;*!*/
  /*!*width: 100%;*!*/
/*}*/

.select,
.label {
  color: #414141;
  display: block;
  font: 400 17px/2em 'Source Sans Pro', sans-serif;
}

.select {
  width: 100%;
  position: absolute;
  top: 0;
  padding: 5px 0;
  height: 40px;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  background: none transparent;
  border: 0 none;
}

.select-box1 {
  background: #ececec;
}

.label {
  position: relative;
  padding: 5px 10px;
  cursor: pointer;
}
.open .label::after {
   content: "▲";
}
.label::after {
  content: "▼";
  font-size: 12px;
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px 15px;
  border-left: 5px solid #fff;
}