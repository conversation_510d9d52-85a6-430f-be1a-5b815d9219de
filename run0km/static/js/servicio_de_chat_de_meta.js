var ServicioDeChatDeMeta = function (listaDeChats, completarChatMetaUrl, enviarMensajeUrl) {
    this._listaDeChats = listaDeChats;
    this._completarChatMetaUrl = completarChatMetaUrl;
    this._enviarMensajeUrl = enviarMensajeUrl
}

ServicioDeChatDeMeta.prototype.configurarEnServicioDeNotificaciones = function (servicioDeNotificaciones) {
    servicioDeNotificaciones.handle(deliveryRunEvents.ABRIR_CHAT_META, function (data) {
        const chatId = data.id;
        this._abrirChat(chatId, true);
    }, this);
}

ServicioDeChatDeMeta.prototype._abrirChat = function (chatId, debeAgregarAlStorage) {
    let chatView;
    var self = this;
    if (this._listaDeChats.includesChatWithId(chatId)) {
        chatView = this._listaDeChats.chatViewWith(chatId);
    } else {
        chatView = this._listaDeChats.addChat(chatId, Chat.CHANNEL_META, true, function (chatId, texto) {
            self._enviarMensajeCallback(chatId, texto)
        }, function () {
        })
        if (debeAgregarAlStorage){
            this._listaDeChats.storageManager().addChatViewToStorage(chatView);
        }
    }
    this._obtenerMensajesDe(chatView)

}

ServicioDeChatDeMeta.prototype._enviarMensajeCallback = function (chatId, texto) {
    var self = this;
    $.ajax({
        type: "POST",
        url: self._enviarMensajeUrl,
        data: {prospecto_pk: chatId, texto: texto, tipo:'whatsapp'},
        error: function () {
            var msj_de_error = 'No se pudo enviar el mensaje.';
            notificador.notificarError(msj_de_error);
        },
        success: function (data) {
            if (!data.status) {
                notificador.notificarError(data.mensaje);
            }
        }
    });
};

ServicioDeChatDeMeta.prototype._obtenerMensajesDe = function (chatView) {
    // Esquema de json a recibir
    // const conversacion = {
    //     variables: {
    //         'nombre': 'Dani',
    //         'telefono': '1140583622'
    //     },
    //     mensajes: [
    //         {
    //             'es_separador':false,
    //             'fue_enviado': false,
    //             'texto': 'Hola',
    //             'fecha': '2025-01-01'
    //         },
    //         {
    //             'es_separador':true,
    //             'texo': '2025/01/02'
    //         },
    //         {
    //             'es_separador':false,
    //             'fue_enviado': true,
    //             'texto': 'Hola Dani',
    //             'fecha': '2025-01-02'
    //         }
    //     ],
    //     fue_leido: false
    // }
    //this._completarChatDesde(conversacion, chatView)
    $.ajax({
        url: this._completarChatMetaUrl.replace('0000', chatView.model().id()).replace('tipo', 'multimedio'),
        type: 'get',
        success: (function (data) {
            this._completarChatDesde(data, chatView)
        }).bind(this)
    })
}

ServicioDeChatDeMeta.prototype._completarChatDesde = function (conversacion, chatView) {
    chatView.removeAllMessages();
    var parameters = conversacion.variables;
    var messages = conversacion.mensajes;
    var fueLeido = conversacion.fue_leido;

    if (fueLeido) {
        chatView.markAsRead();
    }

    chatView.setParameters(parameters);
    for (var i = 0; i < messages.length; i++) {
        var message = messages[i];
        var isReconstructing = true;
        if (!message.es_separador) {
            if (message.fue_enviado) {
                chatView.addSentMessageToFeed(message, isReconstructing);
            } else {
                chatView.addReceivedMessageToFeed(message, isReconstructing);
            }
        }
    }
}

ServicioDeChatDeMeta.prototype.iniciarChats = function () {
    var activeChats = storageManagerListaDeChats.activeSalesChatsInformation(Chat.CHANNEL_META);
    var self = this;
    for (var i = 0; i < activeChats.length; i++) {
        var activeChatInformation = activeChats[i];
        var activeChatId = activeChatInformation.id;
        // try {
        //     storageManagerListaDeChats.addChatInformationFromServer(activeChatId, function (chatId, message) {
        //         self._enviarMensajeCallback(chatId, message);
        //     }, function (chatId) {
        //         self._marcarMensajeComoLeidoCallback(chatId);
        //     });
        // } catch (error) {
        //     if (error instanceof ChatAlreadyOpenedException) {
        //         // Do nothing
        //     }
        //     throw error;
        // }
        this._abrirChat(activeChatId, false)
    }
    return false;
};

