/**
 * Created by eryx on 22/06/17.
 */


DescriptorDeDistribucionDeProspectosController = function(responsables_por_concesionaria) {
    this._responsablesPorConcesionaria = responsables_por_concesionaria;
    this._elementoConcesionarias = null;
    this._elementoResponsables = null;
    // this._elementoProveedores = null;
    this._elementoSoloResponsablesActivos = null;
};

DescriptorDeDistribucionDeProspectosController.prototype.configurar = function () {
    this._configurarFormulario();
};

// DescriptorDeDistribucionDeProspectosController.prototype._copyToClipboard = function (texto) {
//     let $temp = $("<input>");
//     $("body").append($temp);
//     $temp.val(texto).select();
//     document.execCommand("copy");
//     $temp.remove();
// };

DescriptorDeDistribucionDeProspectosController.prototype._configurarFormulario = function () {
    this._configurarElements();
    this._configurarValoresInicialesEnCamposDelReporte();
    this._configurarEventoAlCambiarConcesionarias();
    this._configurarEventoSoloActivos();
};

DescriptorDeDistribucionDeProspectosController.prototype._configurarElements = function(){
    this._elementoConcesionarias = $('#id__concesionarias');
    this._elementoResponsables = $('#id__responsables');
    // this._elementoProveedores = $('#id__proveedores');
    this._elementoSoloResponsablesActivos = $('#id__solo_responsables_activos');
};

DescriptorDeDistribucionDeProspectosController.prototype._configurarValoresInicialesEnCamposDelReporte = function(){
      this._actualizarResponsablesSegunConcesionarias();
};

DescriptorDeDistribucionDeProspectosController.prototype._configurarEventoAlCambiarConcesionarias = function(){
    var self = this;
    self._elementoConcesionarias.change(function(){
        self._actualizarResponsablesSegunConcesionarias();
    });
};

DescriptorDeDistribucionDeProspectosController.prototype._configurarEventoSoloActivos = function(){
    var self = this;
    self._elementoSoloResponsablesActivos.change(function(){
        self._actualizarResponsablesSegunConcesionarias();
    });
};

DescriptorDeDistribucionDeProspectosController.prototype._actualizarResponsablesSegunConcesionarias = function(){
    var self = this;
    var selectConcesionarias = self._elementoConcesionarias[0];
    var concesionariasSeleccionadas = $(selectConcesionarias).val();
    var soloActivos = this._elementoSoloResponsablesActivos.is(':checked');
    var responsables = self._obtenerResponsablesDeLasConcesionarias(concesionariasSeleccionadas, soloActivos);
    self._actualizarResponsablesParaConcesionarias(responsables);
};

DescriptorDeDistribucionDeProspectosController.prototype._obtenerResponsablesDeLasConcesionarias = function(
    concesionarias, soloActivos) {
    var self = this;
    var responsablesDeLaConcesionaria, concesionariaSeleccionada;
    var responsables = [];
    for (var i = 0; i < concesionarias.length; i++){
        concesionariaSeleccionada = concesionarias[i];
        responsablesDeLaConcesionaria = self._responsablesDe(concesionariaSeleccionada, soloActivos);
        responsables = responsables.concat(responsablesDeLaConcesionaria);
    }
    return responsables;
};

DescriptorDeDistribucionDeProspectosController.prototype._responsablesDe = function(concesionaria, soloActivos) {
     var responsablesDeLaConcesionaria = this._responsablesPorConcesionaria[concesionaria];
     var responsables = [];
     responsablesDeLaConcesionaria.forEach(function(each) {
         if (each['es_activo'] || !soloActivos ) {
            responsables.push(each['id'])
         }
     });
     return responsables;
};

DescriptorDeDistribucionDeProspectosController.prototype._actualizarResponsablesParaConcesionarias = function(responsables_de_las_concesionarias){
    // Los responsables que sean de las concesionarias seleccionadas, seleccionarlos por defecto y mostrarlos.
    // Todos los demas deberian deseleccionarse y ocultarse.
    var self = this;
    var opciones_responsables = self._elementoResponsables.find("option");
    $.map(opciones_responsables, function(responsable){
        var responsable_no_es_de_las_concesionarias = $.inArray(parseInt(responsable.value), responsables_de_las_concesionarias) == -1;
        if(responsable_no_es_de_las_concesionarias){
            $(responsable).prop('selected', false);
            $(responsable).hide();
        }
        else {
            $(responsable).prop('selected', true);
            $(responsable).show();
        }
    });

};
