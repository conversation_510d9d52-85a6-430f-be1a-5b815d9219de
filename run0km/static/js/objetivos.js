function askObjetivo(vendedores, id_formulario, unidad, valor) {
    var dialog = $("#" + id_formulario);
    dialog.dialog({
        autoOpen: false,
        height: 270,
        width: 430,
        modal: true,
        open: function (event, ui) {
            $(".asignar-objetivo .feedback").text("");
            if (valor !== undefined && unidad !== undefined) {
                actualizarValorYUnidadDeFormDeEdicion(valor, unidad);
            }
        },
        buttons: {
            "Aceptar": {
                'text': 'Aceptar',
                'class': 'btn-aceptar',
                'click': function () {
                    $.ajax({
                        url: url_asignar_objetivo_ajax,
                        type: "POST",
                        data: $("#" + id_formulario + " form").serialize() + '&' + $.param({vendedores: vendedores}, true),
                        dataType: "json",
                        success: function (data) {
                            var $text = $(".asignar-objetivo .feedback");
                            if (data.status) {
                                var url = window.location.href;

                                if (location.search.search("m=1") == -1) {
                                    if (location.search[0] == '?') {
                                        url = url + '&m=1';
                                    } else {
                                        url = url + '?m=1';
                                    }
                                }

                                window.location.href = url;
                            } else {
                                $text.css("color", "red");
                                $text.text("Hubo un error al procesar el pedido, recuerde que no puede editar objetivos de períodos finalizados.");
                            }
                        },
                        error: function (data) {
                            var $text = $(".asignar-objetivo .feedback");
                            $text.css("color", "red");
                            $text.text("Hubo un error al procesar el pedido. Asegúrese de estar loggeado al sistema.");
                        }
                    });
                }
            },
            "Cancelar": {
                'text': 'Cancelar',
                'class': 'btn-cancelar',
                'click': function () {
                    dialog.dialog("close");
                }
            }
        }
    });
    dialog.dialog("open");
}

// Exclusivos de Asignar objetivo
function inicializarSelectorMultipleDeEquipos() {
    $("input[type=checkbox][class=check-equipo]").click(function (event) {
        var equipo = $(this).data("equipo");
        var $input = $('input[type=checkbox][data-equipo=' + equipo + ']');
        $input.prop('checked', $(this).is(':checked'));
    });
}

function verificarVendedoresYOfrecerCrearObjetivo(){
    var vendedores = obtenerVendedores();
    if (vendedores.length == 0)
        $.growl.error({
            title: 'Error',
            message: 'Debe seleccionar al menos un Vendedor al cual asignarle objetivos.'
        });

    else
        askObjetivo(vendedores, 'crear_objetivo');
}

function obtenerVendedores() {
    var vendedores = [];
    $('input[type=checkbox][class=check-vendedor]:checked').each(function () {
        vendedores.push(parseInt($(this).data('vendedor')));
    });
    return vendedores;
}

VentanaDeConfirmacionDeBorradoDeObjetivo = function(id_del_objetivo, fila_del_objetivo){
    this._id_del_objetivo = id_del_objetivo;
    this._fila_del_objetivo = fila_del_objetivo;
};

VentanaDeConfirmacionDeBorradoDeObjetivo.prototype.filaDelObjetivo = function(){
    return this._fila_del_objetivo;
};

VentanaDeConfirmacionDeBorradoDeObjetivo.prototype.idDelObjetivo = function(){
    return this._id_del_objetivo;
};

VentanaDeConfirmacionDeBorradoDeObjetivo.prototype._eliminarFilaDeObjetivo = function(data){
    notificador.notificar(data['status'], data['mensaje']);
    if (data['status'] === 'Exito'){
        $(this.filaDelObjetivo()).remove();
    }
};

VentanaDeConfirmacionDeBorradoDeObjetivo.prototype._borrarObjetivo = function(){
    var self = this;
    var jqxhr =  $.ajax({
        url: url_borrar_objetivo,
        type: 'POST',
        data: {id_objetivo: this.idDelObjetivo()},
        success: function(data){
            self._eliminarFilaDeObjetivo(data);
        },
        error: function(data){
            notificador.notificarError("Error en la comunicación. Por favor intente más tarde.");
        }
    });
};

VentanaDeConfirmacionDeBorradoDeObjetivo.prototype.render = function(){
    var self = this;
    var dialog = $("#confirmacion-de-borrado-de-objetivo");
    dialog.dialog({
        autoOpen: false,
        height: 180,
        width: 450,
        modal: true,
        buttons: {
            "Aceptar": {
                'text': 'Aceptar',
                'class': 'btn-aceptar',
                'click': function () {
                    dialog.dialog("close");
                    self._borrarObjetivo();
                }
            },
            "Cancelar": {
                'text': 'Cancelar',
                'class': 'btn-cancelar',
                'click': function () {
                    dialog.dialog("close");
                    return false
                }
            }
        }
    });
    return dialog.dialog("open");
};

