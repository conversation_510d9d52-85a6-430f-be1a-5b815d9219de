

VentanaDeEnvioDePropuestas = function(url, nombreDeDialogo, contenidoDeDialogo) {
    /*
    *  Importante: esta pegado al elemento #select-propuestas-para-prospecto.
    * */
    this._url = url;
    this._nombreDeDialogo = nombreDeDialogo;
    this._contenidoDeDialogo = contenidoDeDialogo;
    this._titulo = "Enviar Propuestas";
};

VentanaDeEnvioDePropuestas.prototype._configurar = function () {
    this._elementoPropuestas = $('#select-propuestas-para-prospecto');
};

VentanaDeEnvioDePropuestas.prototype._propuestaIdSeleccionada = function(){
    return this._elementoPropuestas.val();
};

VentanaDeEnvioDePropuestas.prototype.abrir = function() {
    var dialog = $(this._nombreDeDialogo);
    dialog.html(this._contenidoDeDialogo);
    this._configurar();
    this._crearYAbrirDialogoEn(dialog);
};

VentanaDeEnvioDePropuestas.prototype._enviar = function(successCallback) {
    var propuestaId = this._propuestaIdSeleccionada();
    if (!propuestaId){
        notificador.notificarAdvertencia("Por favor seleccione una propuesta");
        return;
    }
    var post_data = {propuestaId: propuestaId};

    var jqxhr = $.post(this._url, $.param(post_data));
    jqxhr.done(function(data){
        notificador.notificar(data.status, data.mensaje);
        if (data.status){
            successCallback();
        }
    });
    jqxhr.fail(systemUnavailable);
};

VentanaDeEnvioDePropuestas.prototype._crearYAbrirDialogoEn = function(dialog){
    var self = this;
    dialog.dialog({
        title: this._titulo,
        autoOpen: false,
        height: 420,
        width: 338,
        modal: true,
        buttons: {
             "Enviar": {
                'text': 'Enviar',
                'class': 'btn-aceptar',
                'click': function () {
                    self._enviar(function () {
                        dialog.dialog("close");
                    })
                }
            },
            "Cerrar": {
                'text': 'Cerrar',
                'class': 'btn-cancelar',
                'click': function () {
                    dialog.dialog("close");
                }
            }
        }
    });
    dialog.dialog("open");
};
