var parsearUrlCon = function (url, id, replaceToken) {
    return url.replace(replaceToken, id);
};

var CompulsaView = function (compulsaElementId, aceptarCompulsaUrl, rechazarCompulsaUrl, compulsaJson,
                             startCompulseSoundUrl) {
    /*
    evento: 'INICIAR_COMPULSA'
    message: { 'id': 1, 'client': {'name': 'juan', 'location': 'chivilcoy', 'campaign': 'ahora84' }, 'timeout': 30s }
    */
    this.compulsaElementId = compulsaElementId;
    this._$element = $('#' + this.compulsaElementId);
    this.compulsaJson = compulsaJson;
    this.aceptarCompulsaUrl = parsearUrlCon(aceptarCompulsaUrl, compulsaJson.id, '0000');
    this.rechazarCompulsaUrl = parsearUrlCon(rechazarCompulsaUrl, compulsaJson.id, '0000');
    this.counter = null;
    if (startCompulseSoundUrl !== null) {
        this.startCompulseSoundUrl = new Audio(startCompulseSoundUrl);
    } else {
        this.startCompulseSoundUrl = null;
    }
};

CompulsaView.prototype.dibujarCompulsa = function () {
    this._$element.empty().append(this.template());
    this.timerExpirationDate = new Date();
    this.timerExpirationDate.setSeconds(this.timerExpirationDate.getSeconds() + this.compulsaJson.timeout);
    this.counter = setInterval(this._startTimer.bind(this), 1);
    this._$element.find("#botonAceptar").click(this._aceptarCompulsa.bind(this));
    this._$element.find("#botonRechazar").click(this._rechazarCompulsa.bind(this));
    this._$element.dialog({
        dialogClass: 'alerta_de_chat',
        height: 530,
        width: 650,
        modal: true
    });
    this._playSound();
};

CompulsaView.prototype._startTimer = function () {
    var timeDiff = this.timerExpirationDate - new Date();

    if (timeDiff <= 0) {
        this.cerrarDialogo();
        return;
    }

    var minutes = new Date(timeDiff).getMinutes();
    var seconds = new Date(timeDiff).getSeconds();
    var milliSeconds = (new Date(timeDiff).getMilliseconds() % 1000);

    var minutesShow = minutes < 10 ? "0" + minutes : minutes;
    var secondsShow = seconds < 10 ? "0" + seconds : seconds;
    var milliSecondsShow = milliSeconds < 100 ? "0" + (milliSeconds < 10 ? "0" + milliSeconds : milliSeconds) : milliSeconds;

    this._$element.find(".alerta-chat-tiempo").text(minutesShow + ":" + secondsShow + ":" + milliSecondsShow); // watch for spelling
};

CompulsaView.prototype.cerrarDialogo = function () {
    this._$element.dialog('close');
    clearInterval(this.counter);
    this._pauseSound();
    this.counter = null;
};

CompulsaView.prototype._responderCompulsa = function (url, error) {
    this.cerrarDialogo();
    $.ajax({
        url: url,
        method: "POST",
        error: function () {
            notificador.notificarError(error);
        }
    });
};

CompulsaView.prototype._aceptarCompulsa = function () {
    var error = 'Error: no se pudo aceptar la compulsa.';
    this._responderCompulsa(this.aceptarCompulsaUrl, error);
};

CompulsaView.prototype._rechazarCompulsa = function () {
    var error = 'Error: no se pudo rechazar la compulsa.';
    this._responderCompulsa(this.rechazarCompulsaUrl, error);

};

CompulsaView.prototype._playSound = function () {
    if (this.startCompulseSoundUrl !== null) {
        this.startCompulseSoundUrl.play();
    }
};

CompulsaView.prototype._pauseSound = function () {
    if (this.startCompulseSoundUrl !== null) {
        this.startCompulseSoundUrl.pause();
    }
};

CompulsaView.prototype.canal = function () {
    return this.compulsaJson.client["channel"];
};

CompulsaView.prototype.template = function () {
    var alerta_chat = '' +
        '    <span class="alerta-titlulo-1">Alerta</span>\n' +
        '    <span class="alerta-titlulo-2">CHAT</span>\n' +
        '    <span class="alerta-titlulo-3">!</span>\n'
    var dato_caliente = '' +
        '    <span class="alerta-titlulo-1">Dato</span>\n' +
        '    <span class="alerta-titlulo-2">CALIENTE</span>\n' +
        '    <span class="alerta-titlulo-3" style="margin-left: 250px">!</span>\n'

    var alerta = this.canal() == "Formulario" ? dato_caliente : alerta_chat;

    return '<h1 class="con-alerta-titulo">\n' +
        alerta +
        '</h1>\n' +
        '<div class="cont-tiempo-respuesta">\n' +
        '    <span class="tiempo-para-responder">Tiempo para responder</span>\n' +
        '    <span class="alerta-chat-tiempo"></span>\n' +
        '</div>\n' +
        '<div class="clear"></div>\n' +
        '<div class="alerta-datos-cliente">\n' +
        '    <table>\n' +
        '        <tr>\n' +
        '            <td><span class="titulo">Campaña:</span></td>\n' +
        '            <td><span class="dato">' + this.compulsaJson.client.campaign + '</span></td>\n' +
        '        </tr>\n' +
        '        <tr>\n' +
        '            <td><span class="titulo">Nombre:</span></td>\n' +
        '            <td><span class="dato">' + this.compulsaJson.client.name + '</span></td>\n' +
        '        </tr>\n' +
        '        <tr>\n' +
        '            <td><span class="titulo">Lugar:</span></td>\n' +
        '            <td><span class="dato">' + this.compulsaJson.client.location + '</span></td>\n' +
        '        </tr>\n' +
        '    </table>\n' +
        '    <div class="clear"></div>\n' +
        '</div>\n' +
        '<div class="alerta-cont-bt-ar">\n' +
        '    <button id="botonAceptar">Aceptar</button>\n' +
        '    <button class="rechazar" id="botonRechazar">rechazar</button>\n' +
        '</div>';
};

var ServicioDeChatDeVentas = function (compulsaElementId, urlsCompulsas, enviarMensajeUrl, startCompulseSoundUrl,
                                       buscarInformacionDelChatEnElServerUrl, marcarChatComoLeidoUrl) {
    this.compulsaElementId = compulsaElementId;
    this.urlsCompulsas = urlsCompulsas;
    this.enviarMensajeUrl = enviarMensajeUrl;
    this.buscarInformacionDelChatEnElServerUrl = buscarInformacionDelChatEnElServerUrl;
    this.marcarChatComoLeidoUrl = marcarChatComoLeidoUrl;
    this.startCompulseSoundUrl = startCompulseSoundUrl;
    this._compulsaActiva = null;
};

ServicioDeChatDeVentas.prototype.configurarEnStorageManagerListaDeChats = function (storageManagerListaDeChats) {
    var self = this;
    storageManagerListaDeChats.configureServerInformationRetrieval(function (chatView) {
        $.ajax({
            url: self.buscarInformacionDelChatEnElServerUrl,
            type: 'get',
            data: {chatId: chatView.model().id()},
            success: function (data) {
                var parameters = data.variables;
                var messages = data.mensajes;
                var fueLeido = data.fue_leido;

                if (fueLeido) {
                    chatView.markAsRead();
                }

                chatView.setParameters(parameters);
                for (var i = 0; i < messages.length; i++) {
                    var message = messages[i];
                    var isReconstructing = true;
                    if (message.emisor === 'C') {
                        chatView.addReceivedMessageToFeed(message, isReconstructing);
                    } else {
                        chatView.addSentMessageToFeed(message, isReconstructing);
                    }
                }
                if (!data.activo) {
                    //chatView.eraseChat()
                    chatView.setAsInactive()
                }
            }
        });
    })
};

ServicioDeChatDeVentas.prototype.configurarEnServicioDeNotificaciones = function (servicioDeNotificaciones) {
    servicioDeNotificaciones.handle(deliveryRunEvents.COMPULSA_INICIADA, function (data) {
        this.iniciarCompulsa(data);
    }, this);

    servicioDeNotificaciones.handle(deliveryRunEvents.COMPULSA_CERRADA, function () {
        this.cerrarCompulsa();
    }, this);

    servicioDeNotificaciones.handle(deliveryRunEvents.CHAT_INICIADO, function (data) {
        var chatId = data.id;
        this.iniciarChat(chatId, true);
    }, this);

    servicioDeNotificaciones.handle(deliveryRunEvents.CHAT_EXPIRADO, function (data) {
        this.chatExpirado();
    }, this);

    servicioDeNotificaciones.handle(deliveryRunEvents.MENSAJE_DE_CHAT, function (data) {
        var chatId = data.id;
        var parametros = data.parametros;
        var mensaje = data.mensaje;
        this.recibirMensaje(chatId, parametros, mensaje);
    }, this);

    servicioDeNotificaciones.handle(deliveryRunEvents.CHAT_FINALIZADO, function (data) {
        var chatId = data.id;
        this.finalizarChat(chatId);
    }, this);
};

ServicioDeChatDeVentas.prototype.iniciarCompulsa = function (compulsaJson) {
    // Asume que solo hay una compulsa activa.
    this._compulsaActiva = new CompulsaView(
        this.compulsaElementId, this.urlsCompulsas['aceptar'], this.urlsCompulsas['rechazar'],
        compulsaJson, this.startCompulseSoundUrl);
    this._compulsaActiva.dibujarCompulsa();
};

ServicioDeChatDeVentas.prototype.cerrarCompulsa = function () {
    var msj_de_advertencia = 'Otro vendedor ha ganado la compulsa';
    notificador.notificarAdvertencia(msj_de_advertencia);
    if (this._compulsaActiva != null) {
        this._compulsaActiva.cerrarDialogo();
    }
};

ServicioDeChatDeVentas.prototype.iniciarChat = function (nuevoChatId, chatIsActive) {
    var self = this;
    try {
        if (this._compulsaActiva && this._compulsaActiva.canal() == "Formulario") {
            return;
        }
    } catch(errorCompulsa) {
        // No hay compulsa activa
    }
    try {
        var chatView = storageManagerListaDeChats.addChat(nuevoChatId, chatIsActive, function (chatId, message) {
            self._enviarMensajeCallback(chatId, message);
        });
        storageManagerListaDeChats.retrieveInformationFromServerFn(chatView);
    }
    catch (error) {
        if (error instanceof ChatAlreadyOpenedException) {
            notificador.notificarAdvertencia(error.message);
        }
    }
};

ServicioDeChatDeVentas.prototype.chatExpirado = function () {
    var mensaje = 'El cliente ha dejado el chat.';
    notificador.notificarAdvertencia(mensaje);
};

ServicioDeChatDeVentas.prototype.recibirMensaje = function (chatId, parametros, mensaje) {
    var chatView = listaDeChats.chatViewWith(chatId);
    if (chatView) {
        chatView.setParameters(parametros);
        chatView.addReceivedMessageToFeed(mensaje);
    } else {
        var self = this;
        storageManagerListaDeChats.addChatInformationFromServer(chatId, function (chatId, message) {
            self._enviarMensajeCallback(chatId, message);
        }, function (chatId) {
            self._marcarMensajeComoLeidoCallback(chatId);
        });
    }
};

ServicioDeChatDeVentas.prototype.finalizarChat = function (chatId) {
    var chatView = listaDeChats.chatViewWith(chatId);
    if (chatView) {
        chatView.setAsInactive()
    }
};

ServicioDeChatDeVentas.prototype._enviarMensajeCallback = function (chatId, message) {
    var self = this;
    $.ajax({
        type: "POST",
        url: self.enviarMensajeUrl,
        data: {chatId: chatId, texto: message},
        error: function () {
            var msj_de_error = 'No se pudo enviar el mensaje.';
            notificador.notificarError(msj_de_error);
        },
        success: function (data) {
            if (!data.status) {
                notificador.notificarError(data.message);
            }
        }
    });
};

ServicioDeChatDeVentas.prototype._marcarMensajeComoLeidoCallback = function (chatId) {
    var self = this;
    $.ajax({
        type: "POST",
        url: parsearUrlCon(self.marcarChatComoLeidoUrl, chatId, '0000'),
        data: {},
        error: function () {
        },
        success: function (data) {
        }
    });
};

ServicioDeChatDeVentas.prototype.iniciarChats = function () {
    var activeChats = storageManagerListaDeChats.activeChatsInformation();
    var self = this;
    for (var i = 0; i < activeChats.length; i++) {
        var activeChatInformation = activeChats[i];
        var activeChatId = activeChatInformation.id;
        try {
            storageManagerListaDeChats.addChatInformationFromServer(activeChatId, function (chatId, message) {
                self._enviarMensajeCallback(chatId, message);
            }, function (chatId) {
                self._marcarMensajeComoLeidoCallback(chatId);
            });
        }
        catch (error) {
            if (error instanceof ChatAlreadyOpenedException) {
                // Do nothing
            }
            throw error;
        }
    }
    return false;
};
