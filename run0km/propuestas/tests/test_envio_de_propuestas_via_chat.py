import json

from django.urls import reverse
from django.utils import timezone

from occ.models import ChatDeVentas
from propuestas.tests.tests_core import PropuestasTest


class EnvioDePropuestasAChatViewTest(PropuestasTest):
    """
        Queda pendiente para la proxima iteracion utilizar el validador de propuestas, falto
        integrar mejor el envio de via chat ya que no tenemos reificada la forma de envio.
    """

    def setUp(self):
        super(EnvioDePropuestasAChatViewTest, self).setUp()
        self.vendedor = self.fixture['vend_1']
        self.login_and_assert_correct(self.vendedor.user)
        self._borrar_marcas()

    def test_propuesta_con_contenido_chat_es_enviada(self):
        texto = 'Hello world'
        chat = self._crear_chat_para(self.vendedor)
        propuesta = self._crear_propuesta_para_chat(texto=texto)
        response = self._post_enviar_propuesta(chat.pk, propuesta.pk)
        self._assert_respuesta_exitosa(response)
        self._assert_envio_de_propuesta_a_chat(chat, propuesta)
        self._assert_registro_de_envios(cantidad=1, vendedor=self.vendedor)

    def test_propuesta_con_contenido_chat_no_es_enviado_a_chat_finalizado(self):
        texto = 'Hello world'
        chat = self._crear_chat_para(self.vendedor)
        chat.finalizar()
        propuesta = self._crear_propuesta_para_chat(texto=texto)
        response = self._post_enviar_propuesta(chat.pk, propuesta.pk)
        self._assert_respuesta_fallida(response, mensaje='Chat no existe o esta inactivo')
        self._assert_propuesta_no_enviada(chat)
        self._assert_registro_de_envios(cantidad=0, vendedor=self.vendedor)

    def test_propuesta_no_existente_responde_error(self):
        chat = self._crear_chat_para(self.vendedor)
        response = self._post_enviar_propuesta(chat.pk, '123456789')
        self._assert_respuesta_fallida(response, mensaje='Propuesta invalida')
        self._assert_propuesta_no_enviada(chat)
        self._assert_registro_de_envios(cantidad=0, vendedor=self.vendedor)

    def _crear_chat_para(self, vendedor):
        chat = ChatDeVentas.nuevo_vacio(vendedor=vendedor, token='1')
        return chat

    def _crear_propuesta_para_chat(self, texto):
        marca = self._crear_marca('1')
        plan = self._crear_plan('1')
        propuesta = self._crear_propuesta(1, self.vendedor.user, marca, plan, texto_chat=texto)
        return propuesta

    def _post_enviar_propuesta(self, chat_id, propuesta_id):
        url = reverse('enviar_propuesta_a_chat', kwargs={'pk': chat_id})
        response = self.client.post(
            path=url, data={'propuestaId': propuesta_id})
        return response

    def _assert_respuesta_exitosa(self, response):
        self._assert_respuesta(response, fue_exitosa=True, mensaje='El envio se ha realizado de forma existosa')

    def _assert_respuesta_fallida(self, response, mensaje):
        self._assert_respuesta(response, fue_exitosa=False, mensaje=mensaje)

    def _assert_respuesta(self, response, fue_exitosa, mensaje):
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        status_esperado = 'Exito' if fue_exitosa else 'Error'
        self.assertEqual(response_data['status'], status_esperado)
        self.assertEqual(response_data['mensaje'], mensaje)

    def _assert_envio_de_propuesta_a_chat(self, chat, propuesta):
        mensajes = chat.lista_de_mensajes()
        self.assertEqual(len(mensajes), 1)
        mensaje = mensajes[0]
        self.assertEqual(mensaje.obtener_texto(), propuesta.texto_chat())
        self.assertTrue(mensaje.origen().proveniente_de_propuestas())

    def _assert_propuesta_no_enviada(self, chat):
        self.assertFalse(chat.tiene_mensajes())

    def _assert_registro_de_envios(self, cantidad, vendedor):
        supervisor = vendedor.responsable()
        registros = supervisor.registros_diario_de_envio_de_propuestas
        registros = registros.para_chat(timezone.now().date())
        if cantidad == 0:
            self.assertEqual(registros.count(), 0)
        else:
            self.assertEqual(registros.count(), 1)
            registro = registros.first()
            self.assertEqual(registro.cantidad(), cantidad)
