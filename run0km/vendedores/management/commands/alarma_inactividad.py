# -*- coding: utf-8 -*-
from django.core.management.base import BaseCommand

from vendedores.utils.notificador_de_alertas import NotificadorDeAlertas


class Command(BaseCommand):
    help = 'Envia mails de alarma por inactividad'

    def handle(self, *args, **options):
        notificador = NotificadorDeAlertas()
        notificador.notificar_a_vendedores_por_inactividad()
        notificador.notificar_a_supervisores_por_inactividad_de_vendedores()
        notificador.notificar_a_supervisores_por_prospectos_no_asignados()
        notificador.notificar_alarma_a_logistica()

