from testing.base import BaseLoggedGerenteTest
from testing.factories import ProspectosFactory
from prospectos.models import Prospecto


class MigrarSupervisorTest(BaseLoggedGerenteTest):

    def setUp(self):
        super(MigrarSupervisorTest, self).setUp()
        self.sup_1 = self.fixture['sup_1']
        self.sup_2 = self.fixture['sup_2']
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'], vendedor=self.fixture['vend_1'])
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'], vendedor=self.fixture['vend_1'])
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'], vendedor=self.sup_1)
        ProspectosFactory(responsable=self.sup_1, campania=self.fixture['camp_1'], vendedor=self.sup_1)
        ProspectosFactory(responsable=self.sup_2, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=self.sup_2, campania=self.fixture['camp_1'])

    def test_migrar_prospectos_supervisor(self):
        sup_2_inicial = self.sup_2.prospectos_a_cargo.count()

        prospectos_sin_vendedor = self.sup_1.prospectos_a_cargo.sin_vendedor()
        self.assertEqual(3, prospectos_sin_vendedor.count())
        ids_prospectos_a_transferir = set(prospectos_sin_vendedor.values_list('id', flat=True))

        data = {
            'supervisor': self.sup_2.id,
            'transferir': 'prospectos'
        }

        url = '/vendedores/migrar-supervisor/%s/' % self.sup_1.id
        response = self.client.post(url, data, follow=True)
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertIn('Se migraron los prospectos del supervisor %s' % self.fixture['sup_1'],
                      contenido_de_la_respuesta)
        self.assertIn('El nuevo supervisor a cargo es %s ' % self.fixture['sup_2'],
                      contenido_de_la_respuesta)

        self.assertEqual(sup_2_inicial + len(ids_prospectos_a_transferir), self.sup_2.prospectos_a_cargo.count())
        transferidos = self.sup_2.prospectos_a_cargo.filter(id__in=ids_prospectos_a_transferir)
        self.assertEqual(transferidos.count(), len(ids_prospectos_a_transferir))

    def test_migrar_todo_supervisor(self):
        # Transferir prospectos con vendedores y equipos.
        id_prospectos_a_cargo = self.sup_1.prospectos_a_cargo.exclude(vendedor=self.sup_1).values_list('id', flat=True)
        id_prospectos_a_cargo = list(id_prospectos_a_cargo)
        id_prospectos_activos = Prospecto.objects.activos(self.sup_1.prospectos).values_list('id', flat=True)
        id_prospectos_activos = list(id_prospectos_activos)
        id_equipos = self.sup_1.equipos.values_list('id', flat=True)
        id_vendedores = self.sup_1.vendedores.values_list('id', flat=True)
        sup_2_inicial = self.sup_2.prospectos_a_cargo.count()

        data = {
            'supervisor': self.sup_2.id,
            'transferir': 'todo'
        }

        url = '/vendedores/migrar-supervisor/%s/' % self.sup_1.id
        response = self.client.post(url, data, follow=True)
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertIn('Se migraron los prospectos, vendedores y equipos del supervisor %s' % self.fixture['sup_1'],
                      contenido_de_la_respuesta)
        self.assertIn('El nuevo supervisor a cargo es %s ' % self.fixture['sup_2'],
                      contenido_de_la_respuesta)

        transferidos_a_cargo = self.sup_2.prospectos_a_cargo.filter(id__in=id_prospectos_a_cargo)
        self.assertEqual(transferidos_a_cargo.count(), len(id_prospectos_a_cargo))
        transferidos_activos = self.sup_2.prospectos_a_cargo.filter(id__in=id_prospectos_activos)
        self.assertEqual(transferidos_activos.count(), len(id_prospectos_activos))
        self.assertEqual(transferidos_activos.count(), transferidos_activos.filter(vendedor__isnull=True).count())
        num_transferidos = len(id_prospectos_a_cargo) + len(id_prospectos_activos)
        self.assertEqual(self.sup_2.prospectos_a_cargo.count(), sup_2_inicial + num_transferidos)

        equipos_transferidos = self.sup_2.equipos.filter(id__in=id_equipos)
        self.assertEqual(equipos_transferidos.count(), len(id_equipos))
        vendedores_transferidos = self.sup_2.vendedores.filter(id__in=id_vendedores)
        self.assertEqual(vendedores_transferidos.count(), len(id_vendedores))
