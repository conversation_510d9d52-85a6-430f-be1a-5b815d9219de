from django.db.models import Count
from django.utils.timezone import now
from prospectos.models import Prospecto


class DetectorDeInactividad(object):

    def tiene_vendedores_con_prospectos_sin_trabajar(self, vendedores, hace_delta_dias):
        for vendedor in vendedores:
            if vendedor.tiene_prospectos_sin_revisar_por(hace_delta_dias):
                return True
        return False

    def datos_de_vendedores_en_falta_por(self, vendedores, hace_delta_dias):
        cantidad = 0
        datos_de_vendedores = []
        for vendedor in vendedores:
            sin_revisar = vendedor.prospectos_sin_revisar_por(hace_delta_dias)
            cantidad_sin_revisar = sin_revisar.count()
            if cantidad_sin_revisar > 0 :
                cantidad += cantidad_sin_revisar
                datos_de_vendedores.append({
                    'vendedor': vendedor,
                    'cantidad': cantidad_sin_revisar,
                    'prospectos': sin_revisar
                })
        return {'cantidad': cantidad, 'vendedores': datos_de_vendedores}

    def datos_de_supervisores_con_vendedores_en_falta_por(self, supervisores, delta):
        datos_de_supervisores = []
        for supervisor in supervisores:
            vendedores_en_falta = self.datos_de_vendedores_en_falta_por(supervisor.vendedores.all(), delta)
            if vendedores_en_falta['cantidad'] > 0:
                datos_supervisor = {'supervisor': supervisor,
                                    'cantidad': vendedores_en_falta['cantidad'],
                                    'vendedores': vendedores_en_falta['vendedores']}
                datos_de_supervisores.append(datos_supervisor)
        return datos_de_supervisores

    def datos_de_supervisores_con_prospectos_sin_asignar_por(self, supervisores, delta):
        datos_de_supervisores = []
        fecha_limite = now() - delta
        for supervisor in supervisores:
            datos_supervisor = self.prospectos_sin_asignar_de_un_supervisor_desde(supervisor, fecha_limite)
            if datos_supervisor['cantidad'] > 0:
                datos_supervisor['supervisor'] = supervisor
                datos_de_supervisores.append(datos_supervisor)
        return datos_de_supervisores

    def prospectos_sin_asignar_de_un_supervisor_desde(self, supervisor, fecha_limite=None):
        if fecha_limite:
            prospectos = supervisor.prospectos_a_cargo.filter(vendedor__isnull=True, fecha_creacion__lt=fecha_limite)
        else:
            prospectos = supervisor.prospectos_a_cargo.filter(vendedor__isnull=True)
        return {'cantidad': prospectos.count(), 'prospectos': prospectos}

    def datos_de_prospectos_sin_responsable_por_mas_de(self, delta):
        hace_24 = now() - delta
        prospectos = Prospecto.objects.filter(fecha_creacion__lt=hace_24, responsable__isnull=True)
        cantidad = prospectos.count()
        datos_por_campania = []
        if cantidad > 0:
            campanias = prospectos.values('campania__nombre',
                                          'campania__categoria__tipo_de_origen__codigo',
                                          'campania__categoria__tipo_de_origen__nombre',
                                          'campania').\
                annotate(cant=Count('campania')).order_by('campania__categoria__tipo_de_origen__codigo')
            for campania in campanias:
                datos_por_campania.append({'origen': campania['campania__categoria__tipo_de_origen__nombre'],
                                           'nombre': campania['campania__nombre'],
                                           'cantidad': campania['cant']})

        return {'cantidad': cantidad, 'campanias': datos_por_campania}

    def cantidades_por_origen_y_campania(self, prospectos):
        cantidades = prospectos.values('campania__nombre',
                                       'campania__categoria__tipo_de_origen__codigo',
                                       'campania__categoria__tipo_de_origen__nombre',
                                       'campania')\
            .annotate(cantidad=Count('campania')).order_by('campania__categoria__tipo_de_origen__nombre')
        datos_cantidades = [{'origen': x['campania__categoria__tipo_de_origen__nombre'],
                             'campania': x['campania__nombre'],
                             'cantidad': x['cantidad']} for x in cantidades]
        return datos_cantidades

    def cantidades_por_origen(self, prospectos):
        cantidades = prospectos.values('campania__categoria__tipo_de_origen__codigo',
                                       'campania__categoria__tipo_de_origen__nombre')\
            .annotate(cantidad=Count('campania__categoria__tipo_de_origen__codigo'))\
            .order_by('campania__categoria__tipo_de_origen__nombre')
        datos_cantidades = [{'origen': x['campania__categoria__tipo_de_origen__nombre'],
                             'cantidad': x['cantidad']} for x in cantidades]
        return datos_cantidades

    def usuarios_sin_actividad_por(self, usuarios, delta):
        limite = now() - delta
        usuarios_sin_actividad = []
        for usuario in usuarios:
            ultima_actividad = usuario.ultimo_log_actividad()
            if not ultima_actividad:
                usuarios_sin_actividad.append({'usuario': usuario, 'fecha': '----', 'registra_actividad':False})
            elif ultima_actividad.ultima < limite:
                usuarios_sin_actividad.append({'usuario': usuario, 'fecha': ultima_actividad.ultima,
                                               'registra_actividad': True})
        return usuarios_sin_actividad