# coding=utf-8
from django import forms
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model

from propuestas.models import ConcesionariaExterna
from vendedores.models import Vendedor
from users.support import MailBienvenidaUsuario
from whatsapp.meta.models.operador import GrupoOperadores


class ConcesionariaExternaAdminForm(forms.ModelForm):
    class Meta:
        model = ConcesionariaExterna
        fields = ['_concesionarias', '_nombre', '_identificador', '_habilitada', '_destacada', '_planes']


class VendedorAdminForm(forms.ModelForm):
    supervisor = forms.ModelChoiceField(queryset=Vendedor.objects.con_cargo_supervisor(), required=False)

    _grupos_operadores_de_meta = forms.ModelMultipleChoiceField(
        queryset=GrupoOperadores.objects.none(), label='Grupo de operadores',
        required=False,
        widget=FilteredSelectMultiple('Grupos', False),
    )

    def __init__(self, *args, **kwargs):
        super(VendedorAdminForm, self).__init__(*args, **kwargs)
        self.fields['supervisor'].queryset = Vendedor.objects.con_cargo_supervisor()
        if self.instance.id is not None:
            if self.instance.es_supervisor():
                self.fields['_grupos_operadores_de_meta'].queryset = GrupoOperadores.objects.de_concesionaria(
                self.instance.obtener_concesionaria())
                self.fields['_grupos_operadores_de_meta'].initial = GrupoOperadores.objects.de_supervisor(
                self.instance)
            else:
                self.fields['_grupos_operadores_de_meta'].queryset = GrupoOperadores.objects.de_supervisor(
                    self.instance.responsable())
                self.fields['_grupos_operadores_de_meta'].initial = GrupoOperadores.objects.de_vendedor(
                    self.instance)

    def clean(self):
        eliminado = self.cleaned_data.get('eliminado')
        if self.instance.id:
            cargo = self.instance.cargo
            if 'eliminado' in self.changed_data:
                if eliminado:
                    puede_ser_eliminado, motivo = self.instance.puede_ser_eliminado()
                    if not puede_ser_eliminado:
                        raise ValidationError(motivo)
        else:
            cargo = self.cleaned_data['cargo']
        supervisor = self.cleaned_data.get('supervisor')
        concesionaria = self.cleaned_data.get('concesionaria')
        equipo = self.cleaned_data.get('equipo')
        if cargo == 'Vendedor':
            if not supervisor:
                raise ValidationError('Un Vendedor debe tener un Supervisor asignado.')
            if equipo and not equipo.supervisor == supervisor:
                raise ValidationError('Un Vendedor solo puede pertenecer a equipos de su supervisor.')
            if eliminado and equipo:
                raise ValidationError('Un Vendedor eliminado no puede puede pertenecer a un equipo.')
            if not concesionaria == supervisor.concesionaria:
                raise ValidationError('Un Vendedor debe pertenecer a la misma Concesionaria que su Supervisor.')
        else:
            if supervisor:
                raise ValidationError('Un Supervisor no puede tener un Supervisor asignado.')
            if equipo:
                raise ValidationError('Un Supervisor no puede pertenecer a un Equipo.')
            if not concesionaria:
                raise ValidationError('La concesionaria es obligatoria para los supervisores.')
            dias_para_atender_prospecto = self.cleaned_data.get('dias_para_atender_prospecto')
            if not dias_para_atender_prospecto:
                raise ValidationError('Debe indicar la cantidad de días antes que el prospecto este en rojo.')


class AddVendedorAdminForm(VendedorAdminForm):
    """
    Campos para la creacion del User al crear
    """
    email = forms.EmailField(required=True)
    first_name = forms.CharField(required=True, max_length=30, min_length=1)
    last_name = forms.CharField(required=True, max_length=30, min_length=1)
    usuario = forms.CharField(required=True, max_length=30, min_length=4)
    password = forms.CharField(widget=forms.PasswordInput)
    password2 = forms.CharField(widget=forms.PasswordInput)

    def clean_password2(self):
        cd = self.cleaned_data
        password = cd.get('password', None)
        password2 = cd.get('password2', None)
        if password and password2 and cd['password2'] != cd['password']:
            raise ValidationError('Ambas contraseñas deben coincidir.')

    def clean_usuario(self):
        usuario = self.cleaned_data.get('usuario', None)
        if usuario and get_user_model().objects.filter(username=usuario).count() > 0:
            raise ValidationError('Ya existe una cuenta con ese Usuario')
        return usuario

    def save(self, commit=True):
        cd = self.cleaned_data
        # Creo el usuario
        user = get_user_model().objects.create_user(username=cd['usuario'],
                                                    email=cd['email'],
                                                    password=cd['password'],
                                                    first_name=cd['first_name'],
                                                    last_name=cd['last_name'])
        self.instance.user = user
        nuevo_vendedor = super(AddVendedorAdminForm, self).save(commit)
        nuevo_vendedor.save()
        nuevo_vendedor.asignar_creditos_para_sms_iniciales()
        MailBienvenidaUsuario(user).enviar(self.instance.concesionaria, cd['password'])
        return nuevo_vendedor


class AlwaysChangedModelForm(forms.ModelForm):
    def has_changed(self):
        if self.instance.pk is None:
            return True
        return super(AlwaysChangedModelForm, self).has_changed()
