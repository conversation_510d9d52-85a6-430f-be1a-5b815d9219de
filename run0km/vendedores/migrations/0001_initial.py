# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('concesionarias', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LogActividad',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('anio', models.PositiveSmallIntegerField()),
                ('mes', models.PositiveSmallIntegerField()),
                ('ultima', models.DateTimeField()),
                ('cantidad', models.PositiveSmallIntegerField()),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Vendedor',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('cargo', models.CharField(default=b'Vendedor', max_length=10, choices=[(b'Vendedor', b'Vendedor'), (b'Supervisor', b'Supervisor')])),
                ('alerta_diaria', models.BooleanField(default=True, verbose_name=b'Alerta 24hs')),
                ('alerta_a_supervisor', models.BooleanField(default=True, help_text=b'Alertar al supervisor cada 4 dias', verbose_name=b'Notificar al supervisor')),
                ('factor_de_asignacion', models.IntegerField(default=1)),
                ('eliminado', models.BooleanField(default=False, verbose_name=b'Vendedor Eliminado')),
                ('concesionaria', models.ForeignKey(related_name=b'empleados', on_delete=django.db.models.deletion.SET_NULL, blank=True, to='concesionarias.Concesionaria', null=True)),
                ('supervisor', models.ForeignKey(related_name=b'vendedores', on_delete=django.db.models.deletion.SET_NULL, blank=True, to='vendedores.Vendedor', null=True)),
                ('user', models.OneToOneField(to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'vendedor',
                'verbose_name_plural': 'vendedores',
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='logactividad',
            name='vendedor',
            field=models.ForeignKey(to='vendedores.Vendedor'),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='logactividad',
            unique_together=set([('vendedor', 'anio', 'mes')]),
        ),
    ]
