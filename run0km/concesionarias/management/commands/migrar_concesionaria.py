import os
from datetime import datetime
from django.core.management import BaseCommand, CommandError, call_command
from django.core.serializers import serialize
from django.db import transaction

from concesionarias.models import Concesionaria

from vendedores.models import Vendedor


class Command(BaseCommand):
    help = 'Migra todo el staff (gere<PERSON>, supervisores y vendedores) de una concesionaria a otra'

    def add_arguments(self, parser):
        parser.add_argument('--from', type=int, dest='from_concesionaria', required=False,
                            help='ID de la concesionaria origen')
        parser.add_argument('--to', type=int, dest='to_concesionaria', required=False,
                            help='ID de la concesionaria destino')
        parser.add_argument('--dry-run', action='store_true', help='Simula la migración sin guardar cambios')
        parser.add_argument('--restore-backup', type=str, help='Ruta al archivo JSON para restaurar backup')

    def handle(self, *args, **options):
        restore_file = options.get('restore_backup')
        if restore_file:
            self._restaurar_backup_json(restore_file)
            return

        from_id = options['from_concesionaria']
        to_id = options['to_concesionaria']
        dry_run = options['dry_run']

        if from_id == to_id:
            raise CommandError('❌ No se puede migrar a la misma concesionaria.')

        try:
            origen = Concesionaria.objects.get(id=from_id)
            destino = Concesionaria.objects.get(id=to_id)
        except Concesionaria.DoesNotExist:
            raise CommandError('❌ Una de las concesionarias especificadas no existe.')

        self.stdout.write(f'🔄 Migrando staff de {origen.nombre} (ID: {from_id}) a {destino.nombre} (ID: {to_id})...')

        self._hacer_backup_json(origen, destino)

        try:
            with transaction.atomic():
                gerentes = origen.obtener_gerentes()
                supervisores = Vendedor.all_objects.filter(cargo='Supervisor', concesionaria=origen)
                vendedores = Vendedor.all_objects.filter(cargo='Vendedor', concesionaria=origen)
                campanias = origen.campanias_propias.all()
                concesionarias_externas = origen.concesionarias_externas.all()

                querysets = [
                    (gerentes, 'gerentes'),
                    (supervisores, 'supervisores'),
                    (vendedores, 'vendedores'),
                    (campanias, 'campanias')
                ]

                for queryset, nombre_modelo in querysets:
                    self._cambiar_concesionaria_a(queryset, destino, nombre_modelo, dry_run)

                if concesionarias_externas.exists():
                    self._cambiar_concesionarias_externas_a(concesionarias_externas, origen, destino, dry_run)

                if dry_run:
                    raise Exception('Simulación activada: forzando rollback')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Ocurrió un error y se revirtió la migración: {str(e)}'))
            return

        self.stdout.write(self.style.SUCCESS(
            f'✔️ Migración completada de {origen.nombre} a {destino.nombre}.'
        ))

    def _cambiar_concesionaria_a(self, queryset, nueva_concesionaria, nombre_modelo, dry_run):
        cantidad = queryset.count()
        if dry_run:
            self.stdout.write(f'✅ Simulación migrada de {cantidad} {nombre_modelo} a {nueva_concesionaria.nombre}')
        else:
            queryset.update(concesionaria=nueva_concesionaria)
            self.stdout.write(f'✅ Migrados {cantidad} {nombre_modelo} a {nueva_concesionaria.nombre}')

    def _cambiar_concesionarias_externas_a(self, concesionarias_externas, origen, destino, dry_run):
        if dry_run:
            self.stdout.write(f'✅ Simulada migración de concesionarias externas a {destino.nombre}')
        else:
            cantidad = concesionarias_externas.count()
            for concesionaria_externa in concesionarias_externas:
                concesionaria_externa._concesionarias.remove(origen)
                concesionaria_externa._concesionarias.add(destino)
                concesionaria_externa.save()
            self.stdout.write(f'✅ Migradas {cantidad} concesionarias externas a {destino.nombre}')

    def _hacer_backup_json(self, origen, destino):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_dir = '/var/www/master/deliveryrun/log/backups_migracion'
        os.makedirs(base_dir, exist_ok=True)

        gerentes = origen.obtener_gerentes().values_list('id', flat=True)
        supervisores = Vendedor.all_objects.filter(cargo='Supervisor', concesionaria=origen).values_list('id', flat=True)
        vendedores = Vendedor.all_objects.filter(cargo='Vendedor', concesionaria=origen).values_list('id', flat=True)
        campanias = origen.campanias_propias.all().values_list('id', flat=True)
        concesionarias_externas = origen.concesionarias_externas.all().values_list('id', flat=True)

        log_data = {
            'timestamp': timestamp,
            'concesionaria_origen_id': origen.id,
            'concesionaria_origen_nombre': origen.nombre,
            'concesionaria_destino_id': destino.id,
            'concesionaria_destino_nombre': destino.nombre,
            'vendedores': list(vendedores),
            'supervisores': list(supervisores),
            'gerentes': list(gerentes),
            'campanias': list(campanias),
            'concesionarias_externas': list(concesionarias_externas),
        }

        filename = f'{base_dir}/migracion_ids_{origen.id}_{timestamp}.log'
        with open(filename, 'w') as f:
            for key, value in log_data.items():
                f.write(f'{key}: {value}\n')

        self.stdout.write(self.style.WARNING(f'📝 Log de IDs guardado en: {filename}'))

    def _restaurar_backup_json(self, archivo_log):
        if not os.path.exists(archivo_log):
            raise CommandError(f'❌ El archivo no existe: {archivo_log}')

        try:
            with open(archivo_log, 'r') as f:
                lineas = f.readlines()
                datos = {}
                for linea in lineas:
                    if ':' in linea:
                        clave, valor = linea.strip().split(':', 1)
                        clave = clave.strip()
                        valor = valor.strip()
                        if clave in ['vendedores', 'supervisores', 'gerentes', 'campanias', 'concesionarias_externas']:
                            # Convierte '[1, 2, 3]' en [1, 2, 3]
                            datos[clave] = eval(valor)
                        else:
                            datos[clave] = valor

            origen_id = int(datos['concesionaria_origen_id'])
            origen = Concesionaria.objects.get(id=origen_id)

            # Restaurar concesionaria de Vendedores
            ids_vendedores = datos.get('vendedores', []) + datos.get('supervisores', [])
            if ids_vendedores:
                Vendedor.all_objects.filter(id__in=ids_vendedores).update(concesionaria=origen)
                self.stdout.write(self.style.SUCCESS(f'✔️ Restaurados {len(ids_vendedores)} vendedores/supervisores'))

            if datos.get('gerentes'):
                from gerentes.models import Gerente
                # Restaurar gerentes
                ids_gerentes = datos['gerentes']
                if ids_gerentes:
                    Gerente.objects.filter(id__in=ids_gerentes).update(concesionaria=origen)
                    self.stdout.write(self.style.SUCCESS(f'✔️ Restaurados {len(ids_gerentes)} gerentes'))

            # Restaurar campañas
            from campanias.models import Campania  # solo si no estaba ya importado
            ids_campanias = datos.get('campanias', [])
            if ids_campanias:
                Campania.objects.filter(id__in=ids_campanias).update(concesionaria=origen)
                self.stdout.write(self.style.SUCCESS(f'✔️ Restauradas {len(ids_campanias)} campañas'))

            # Restaurar relaciones con concesionarias externas
            ids_externas = datos.get('concesionarias_externas', [])
            destino_id = int(datos['concesionaria_destino_id'])
            destino = Concesionaria.objects.get(id=destino_id)
            if ids_externas:
                concesionarias_externas = Concesionaria.objects.filter(id__in=ids_externas)
                self._cambiar_concesionarias_externas_a(concesionarias_externas, destino, origen, dry_run=False)
                self.stdout.write(self.style.SUCCESS(f'✔️ Restauradas {len(ids_externas)} concesionarias externas'))

        except Exception as e:
            raise CommandError(f'❌ Error al restaurar desde log: {str(e)}')

