from django.db import models


class R<PERSON><PERSON>(models.Model):

    _nombre = models.Char<PERSON>ield(max_length=100, unique=True)
    _etiqueta_marca = models.CharField(max_length=100)
    _etiqueta_modelo = models.Char<PERSON>ield(max_length=100, blank=True)
    _tiene_modelo = models.BooleanField(default=True)
    _puede_gestionar_marcas = models.BooleanField(default=True)
    _puede_gestionar_modelos = models.BooleanField(default=True)
    _campos_del_detalle_del_prospecto_excluidos = models.ManyToManyField(to='concesionarias.CampoDetalleProspecto')

    def nombre(self):
        return self._nombre

    def etiqueta_marca(self):
        return self._etiqueta_marca

    def etiqueta_modelo(self):
        return self._etiqueta_modelo

    def tiene_modelo(self):
        return self._tiene_modelo

    def puede_gestionar_marcas(self):
        return self._puede_gestionar_marcas

    def puede_gestionar_modelos(self):
        return self._puede_gestionar_modelos

    def tiene_excluido_el_campo(self, codigo_de_campo):
        return self._campos_del_detalle_del_prospecto_excluidos.con_codigo(codigo=codigo_de_campo).exists()

    def __str__(self):
        return self._nombre
