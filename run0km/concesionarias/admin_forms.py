# -*- coding: utf-8 -*-
from django import forms
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.core.exceptions import ValidationError

from concesionarias.models import Concesionaria
from concesionarias.verificacion_de_dominio import VerificadorDeUrls
from whatsapp.meta.models.operador import GrupoOperadores


class ConcesionariaAdminForm(forms.ModelForm):
    grupo_operadores = forms.ModelMultipleChoiceField(
        queryset=GrupoOperadores.objects.all(), label='Grupo de operadores',
        required=False,
        widget=FilteredSelectMultiple('Grupos', False),
    )

    def clean(self):
        cleaned_data = super(ConcesionariaAdminForm, self).clean()
        habilitado = self._whatsapp_habilitado()
        token = cleaned_data.get('token_whatsapp', '')
        if habilitado and token == "":
            raise ValidationError('Debe indicar un Token para Whatsapp.')
        return cleaned_data

    def clean_subdominio(self):
        subdominio = self.cleaned_data['subdominio'].lower().strip()
        sitio = self.cleaned_data.get('sitio')
        if subdominio and not sitio:
            raise ValidationError('No puede definir un subdominio sin definir un Sitio.')
        return subdominio

    def _whatsapp_habilitado(self):
        # TODO: parche!!!!
        return self.data.get('configuracion_servicios-0-_whatsapp_habilitado') == 'on'

    class Meta:
        model = Concesionaria
        exclude = ['web']


class SitioAdminForm(forms.ModelForm):

    def clean_url(self):
        url = self.cleaned_data['url'].lower()
        if url:
            verificador = VerificadorDeUrls()
            if not verificador.nueva_url_es_valida_para_sitio(url, self.instance):
                raise ValidationError('La URL conflictúa con la URL de otro sitio.')
        return url
