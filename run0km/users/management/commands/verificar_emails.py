from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = 'Chequea si hay usuarios sin email, o con emails repetidos'

    def handle(self, *args, **options):
        verificar_emails()

def verificar_emails():
    sin_mail = []
    con_mail=dict()
    problem = False
    for user in get_user_model().objects.all():
        if user.email == None or user.email == '':
            sin_mail.append(user)
        else:
            if user.email in con_mail:
                con_mail[user.email].append(user)
            else:
                con_mail[user.email] = [user]

    if len(sin_mail) > 0:
        problem = True
        print("Usuarios sin email registrado:")
        for user in sin_mail:
            print("  - %s | %s" % (user.username, user.get_full_name()))
        print("Total: %d" % len(sin_mail))
        print("----------")

    for email, users in list(con_mail.items()):
        if len(users) > 1:
            problem = True
            print("Usuarios con el mismo email: %s" % email)
            for user in users:
                print("  - %s | %s" % (user.username, user.get_full_name()))

    if not problem:
        print("No hay problemas respecto a los emails.")
