# -*- coding: utf-8 -*-
from django.contrib.auth.forms import UserCreationForm as DjangoUserCreationForm, \
    UserChangeForm as DjangoUserChangeForm, AuthenticationForm as DjangoAuthenticationForm, PasswordResetForm
from django import forms
from django.contrib.auth.tokens import default_token_generator
import tldextract
from concesionarias.models import Concesionaria
from django.conf import settings
from users.admin_views import FiltroConcesionariaView

from users.models import User, PermisoDePersonificacion
from concesionarias.verificacion_de_dominio import VerificadorDeDominioPorConcesionaria


class UserCreationForm(DjangoUserCreationForm):
    """
    NOTE: Override needed because of using custom user model
        See https://docs.djangoproject.com/en/1.7/topics/auth/customizing/#custom-users-and-the-built-in-auth-forms
    """

    class Meta:
        model = User
        fields = ("username",)

    def clean_username(self):
        # NOTE: Copied from UserCreationForm
        # Since User.username is unique, this check is redundant,
        # but it sets a nicer error message than the ORM. See #13147.
        username = self.cleaned_data["username"]
        try:
            User._default_manager.get(username=username)
        except User.DoesNotExist:
            return username
        raise forms.ValidationError(
            self.error_messages['duplicate_username'],
            code='duplicate_username',
        )


class UserChangeForm(DjangoUserChangeForm):
    """
    NOTE: Override needed because of using custom user model
        See https://docs.djangoproject.com/en/1.7/topics/auth/customizing/#custom-users-and-the-built-in-auth-forms
    """

    class Meta:
        model = User
        fields = '__all__'


class AuthenticationForm(DjangoAuthenticationForm):
    def confirm_login_allowed(self, user):
        super(AuthenticationForm, self).confirm_login_allowed(user)
        self._validar_dominio_para_el_usuario_logueado(user)

    def _validar_dominio_para_el_usuario_logueado(self, user):
        """
        Verificacion de que el usuario pueda ingresar desde el dominio utilizado.
        :param user:
        :return: None
        """
        verificador = VerificadorDeDominioPorConcesionaria()
        usuario = User._default_manager.get(username=user.username)
        if not verificador.dominio_valido_para_usuario(usuario, self.request):
            raise forms.ValidationError("Usuario y contraseña incorrectos.", code='dominio_erroneo')


class CustomUserChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        json = obj.as_json()
        nombre_completo = json['nombre'] + ' ' + json['cargo']
        return nombre_completo


class PermisoDePersonificacionForm(forms.ModelForm):
    concesionaria = forms.ModelChoiceField(queryset=Concesionaria.objects.all(), required=False)
    usuario = CustomUserChoiceField(queryset=User.objects.all())
    alias = CustomUserChoiceField(queryset=User.objects.all())

    def __init__(self, *args, **kwargs):
        super(PermisoDePersonificacionForm, self).__init__(*args, **kwargs)

        if self.instance and self.instance.pk:
            concesionaria = self.instance.concesionaria()
        else:
            concesionaria = None
        self.fields['concesionaria'].initial = concesionaria
        usuarios = FiltroConcesionariaView.usuarios_de_concesionaria(concesionaria)
        self.fields['usuario'].queryset = usuarios
        self.fields['alias'].queryset = usuarios

    class Media:
        js = (
            'js/jquery.min.js',
            'js/admin_permiso_personificacion.js',  # project static folder
            'js/system_unavailable.js',
        )

    def clean(self):
        cleaned_data = super(PermisoDePersonificacionForm, self).clean()
        usuario = cleaned_data.get('usuario', None)
        alias = cleaned_data.get('alias', None)
        if usuario and alias:
            existe_permiso = PermisoDePersonificacion.objects.filter(usuario=usuario, alias=alias).exists()
            if existe_permiso:
                raise forms.ValidationError(
                    'El usuario: "' + usuario.get_full_name() + '" ya tiene un permiso de personificación para el alias: "' + alias.get_full_name() + '"')
        return cleaned_data


class CustomPasswordResetForm(PasswordResetForm):
    def save(self, domain_override=None, subject_template_name='registration/password_reset_subject.txt',
             email_template_name='registration/password_reset_email.html', use_https=False,
             token_generator=default_token_generator, from_email=None, request=None, html_email_template_name=None,
             extra_email_context=None):

        if not domain_override:
            domain_override = request.get_host()
            if not from_email:
                from_email = self._email_de_origen_para_notificaciones(domain_override)
        super(CustomPasswordResetForm, self).save(domain_override, subject_template_name, email_template_name,
                                                  use_https, token_generator, from_email, request,
                                                  html_email_template_name, extra_email_context)

    def _email_de_origen_para_notificaciones(self, dominio, asunto=''):
        """
            Parche porque no tengo la concesionaria (quitar y usar el metodo de la concesionaria)
        """
        asunto = " - %s" % asunto if asunto else ''
        if dominio:
            extracted = tldextract.extract(dominio)
            email = "%(dominio)s%(asunto)s <no-reply@%(dominio)s.%(sufijo)s>" % {'dominio': extracted.domain,
                                                                                 'asunto': asunto,
                                                                                 'sufijo': extracted.suffix}
        else:
            email = "Run0km%(asunto)s <%(email)s>" % {'asunto': asunto, 'email': settings.DEFAULT_FROM_EMAIL}
        return email
