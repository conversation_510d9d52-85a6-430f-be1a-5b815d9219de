# -*- coding: utf-8 -*-


from django.db import models, migrations
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_user_distribucion'),
    ]

    operations = [
        migrations.CreateModel(
            name='PermisoDePersonificacion',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('alias', models.ForeignKey(related_name=b'permisos_de_personificadores', to=settings.AUTH_USER_MODEL)),
                ('usuario', models.ForeignKey(related_name=b'permisos_de_personificacion', to=settings.AUTH_USER_MODEL)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name='permisodepersonificacion',
            unique_together=set([('usuario', 'alias')]),
        ),
    ]
