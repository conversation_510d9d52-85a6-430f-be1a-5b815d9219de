import json

from django.urls import reverse

from equipos.models import Equi<PERSON>
from testing.base import BaseFixturedTest, BaseLoggedTest


class PermisosDeEquipoTest(BaseFixturedTest):
    def test_vendedor_sin_permiso(self):
        self.client.login(username='vend1', password='admin')
        # No puede acceder a la lista de equipos
        response = self.client.get('/vendedores/', {})
        self.assertRedirects(response, reverse("resumen"), status_code=302, target_status_code=200, msg_prefix='')
        #No puede ver un equipos
        response = self.client.get('/equipos/1/', {})
        self.assertRedirects(response, reverse("resumen"), status_code=302, target_status_code=200, msg_prefix='')
        #No puede crear un equipo
        response = self.client.post('/vendedores/', {"nombre": "Nombre"})
        self.assertRedirects(response, reverse("resumen"), status_code=302, target_status_code=200, msg_prefix='')
        #No puede borrar equipo
        response = self.client.get('/equipos/borrar-equipo/1/', {})
        self.assertRedirects(response, reverse("resumen"), status_code=302, target_status_code=200, msg_prefix='')
        #No puede renombrar
        response = self.client.get('/equipos/renombrar/1/', {"nombre": "Nombre"})
        response_msg = json.loads(response.content)
        self.assertIn('status', response_msg)
        self.assertFalse(response_msg['status'])
        #No puede guardar integrantes
        response = self.client.get('/equipos/guardar-integrantes/1/', {"integrantes": "1,2,3"})
        self.assertRedirects(response, reverse("resumen"), status_code=302, target_status_code=200, msg_prefix='')

    def test_supervisor_sin_permiso(self):
        self.client.login(username='sup2', password='admin')
        equipo_de_otro_supervisor = self.fixture['equipo_1']
        # No puede ver equipo que no tiene asignado
        response = self.client.get('/equipos/%s/' % equipo_de_otro_supervisor.pk, {})
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        #No puede borrar equipo que no tiene asignado
        response = self.client.get('/equipos/borrar-equipo/%s/' % equipo_de_otro_supervisor.pk, {})
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        #No puede renombrar equipo que no tiene asignado
        response = self.client.get('/equipos/renombrar/%s/' % equipo_de_otro_supervisor.pk,
                                   {"nombre": "Nombre"})
        response_msg = json.loads(response.content)
        self.assertIn('status', response_msg)
        self.assertFalse(response_msg['status'])
        #No puede guardar integrantes en equipo que no tiene asignado
        response = self.client.get('/equipos/guardar-integrantes/%s/' % equipo_de_otro_supervisor.pk,
                                   {"integrantes": "1,2,3"})
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')


class BaseLoggedEquiposTest(BaseLoggedTest):
    def setUp(self):
        super(BaseLoggedEquiposTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.user = self.supervisor.user
        self.assertTrue(self.client.login(username=self.user.username, password='admin'))

    def test_supervisor_con_permiso(self):
        # Puede acceder a /vendedores, ver como chequea la lista de equipos
        response = self.client.get('/vendedores/')
        self.assertEqual(response.status_code, 200)
        #Puede crear un equipo
        nombre = "Nombre 2"
        response = self.client.post('/vendedores/', {"nombre": nombre})
        self.assertEqual(response.status_code, 200)
        equipo = Equipo.objects.get(supervisor=self.supervisor, nombre=nombre)

        # Puede renombrar
        nuevo_nombre = "Nuevo Nombre"
        response = self.client.get(('/equipos/renombrar/%d/' % equipo.id), {"nombre": nuevo_nombre})
        response_msg = json.loads(response.content)
        self.assertIn('status', response_msg)
        self.assertTrue(response_msg['status'])
        equipo = Equipo.objects.get(supervisor=self.supervisor, nombre=nuevo_nombre)

        # Puede guardar integrantes
        integrantes = [self.fixture['vend_1'].id, self.fixture['vend_2'].id, self.fixture['vend_5'].id, ]
        response = self.client.post(('/equipos/guardar-integrantes/%d/' % equipo.id),
                                    {"integrantes": json.dumps(integrantes)}, follow=True)
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        self.assertEqual(len(integrantes), equipo.integrantes.count())
        self.assertContains(response, 'Se ha modificado el equipo: %s' % nuevo_nombre)

        # Puede desasignar integrantes
        integrantes = [self.fixture['vend_1'].id, self.fixture['vend_2'].id, ]
        response = self.client.post(('/equipos/guardar-integrantes/%d/' % equipo.id),
                                    {"integrantes": json.dumps(integrantes)})
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        self.assertEqual(len(integrantes), equipo.integrantes.count())

        # Puede borrar equipo
        response = self.client.get(('/equipos/borrar-equipo/%d/' % equipo.id), {})
        self.assertRedirects(response, '/vendedores/', status_code=302, target_status_code=200, msg_prefix='')
        self.assertEqual(0, Equipo.objects.filter(supervisor=self.user.vendedor, id=equipo.id).count())
