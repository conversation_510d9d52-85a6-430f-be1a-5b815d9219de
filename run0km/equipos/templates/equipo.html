{%extends "base.html"%}

{% block js%}
  {{block.super}}
    <link href="{{ STATIC_URL }}css/staff.css" type="text/css" rel="stylesheet" />
    <script type="text/javascript" src="{{STATIC_URL}}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/equipos.js"></script>
    <script type="text/javascript">
      var renombrar_equipo_url = "{% url 'renombrar-equipo' equipo.id %}";
      var guardar_integrantes_url = "{% url 'guardar-integrantes' equipo.id %}";
      var integrantes = {{json_integrantes|safe}}
      var libres = {{json_libres|safe}}
      var csrf_token = '{{csrf_token}}';
      $(InicializarSelector);
    </script>
{% endblock %}

{% block content %}
<div class="principal prospecto-resumen contenido">
    <div class="titulo">
       <img src="{{STATIC_URL}}img/titulo.png" />
       <div><h1>Equipos</h1></div>
    </div>
    <div class="contenedor">
        <div class="contenedor-borde">
            <div class="filtro">
              <label for="nombre">Nombre</label><input type="text" name="nombre" id="nombre" value="{{equipo.nombre}}"/>
              <input class="filtros" type="button" value="Cambiar" onclick="Renombrar()"/>
              <div id="spin_holder" class='spin-holder'></div> 
            </div>
            <div class="bandeja-entrada">
               <img src="{{STATIC_URL}}img/flecha.png" />
               <p>Integrantes</p>
            </div>
            <div style="min-height:25px;" id="holder-integrantes" class="sms-llamados contenedor-equipo">
               <form id="guardar_integrantes_form" action="{% url 'guardar-integrantes' equipo.id %}" method="post">
                 {% csrf_token %}
                 <input type="hidden" id="id_integrantes" name="integrantes" />
                 <input style='float:right' class="filtros" type="button" value="Guardar" onclick="GuardarIntegrantes()"/>
               </form>
            </div>
            <div class="bandeja-entrada">
               <img src="{{STATIC_URL}}img/flecha.png" />
               <p>Sin Equipo</p>
            </div>
            <div id="holder-libres" class="sms-llamados contenedor-equipo">
            </div>
        </div>
    </div>
    <img src="{{STATIC_URL}}img/sombra-contenedor.png" />
</div>

{%endblock%}

