from django.db.models import Q

from core.querysets import DeliveryQuerySet


class CampaniaQuerySet(DeliveryQuerySet):
    def campanias_de_vendedor(self, vendedor):
        return self.filter(Q(prospectos__vendedor=vendedor) | Q(prospectos__responsable=vendedor)).distinct()

    def campanias_de_concesionaria(self, concesionaria):
        return self.filter(Q(prospectos__vendedor__concesionaria=concesionaria) |
                           Q(prospectos__responsable__concesionaria=concesionaria)).distinct()

    def vendedor_tiene_campania(self, vendedor, campania):
        return self.campanias_de_vendedor(vendedor).filter(id=campania.id).count() == 1

    def gerente_tiene_campania(self, gerente, campania):
        return self.campanias_de_concesionaria(gerente.concesionaria).filter(id=campania.id).count() == 1

    def campanias_externas_de_concesionaria(self, concesionaria):
        return self.filter(concesionaria=concesionaria, categoria__es_externa=True)

    def campanias_no_externas(self):
        return self.filter(categoria__es_externa=False)

    def campanias_no_externas_con_nombre(self, nombre):
        return self.campanias_no_externas().get(nombre=nombre)

    def campanias_de_calidad(self, calidad):
        return self.filter(categoria__tipo_de_origen=calidad)

    def campanias_externas_de_concesionaria_y_calidad(self, concesionaria, calidad):
        campanias_de_concesionaria = self.campanias_externas_de_concesionaria(concesionaria=concesionaria)
        campanias_filtradas = campanias_de_concesionaria.filter(categoria__tipo_de_origen=calidad)
        return campanias_filtradas

    def campanias_genericas_de_nombre(self, nombre):
        return self.filter(categoria__es_externa=False, nombre__iexact=nombre)

    def con_nombre(self, nombre):
        return self.get(nombre=nombre)


class CategoriaDeCampaniaQuerySet(DeliveryQuerySet):
    def externas(self):
        return self.filter(es_externa=True)

    def obtener_categorias_no_externas(self):
        return self.filter(es_externa=False)

    def con_nombre_no_vacio_ordenadas_por_interna_luego_externa(self):
        return self.exclude(nombre__exact='').order_by('es_externa')


class TipoDeOrigenQuerySet(DeliveryQuerySet):
    pass
