from django.conf import settings

from client_crm.core import CRMIntegrationAPIClient, Run0KmAPIClient
from propuestas.constructores import ConstructorDeMarcas, ConstructorDeModelos, ConstructorDePlanes, \
    ConstructorDeTiposDePlanes, ConstructorDeConcesionariasExternas
from propuestas.models import ConcesionariaExterna


class ServicioDeIntegracion(object):
    @classmethod
    def actualizar_todos(cls):
        cls.para_marcas().realizar_actualizacion()
        cls.para_modelos().realizar_actualizacion()
        cls.para_tipos_de_planes().realizar_actualizacion()
        cls.para_planes().realizar_actualizacion()
        cls.para_concesionarias_externas().realizar_actualizacion()
        cls.para_planes_de_concesionarias_externas().realizar_actualizacion()

    @classmethod
    def para_planes(cls):
        return ServicioDePlanes()

    @classmethod
    def para_tipos_de_planes(cls):
        return ServicioDeTiposDePlanes()

    @classmethod
    def para_marcas(cls):
        return ServicioDeMarcas()

    @classmethod
    def para_modelos(cls):
        return ServicioDeModelos()

    @classmethod
    def para_concesionarias_externas(cls):
        return ServicioDeConcesionariasExternas()

    @classmethod
    def para_planes_de_concesionarias_externas(cls):
        return ServicioDePlanesDeConcesionariasExternas()

    @classmethod
    def para_nombre(cls, nombre):
        for subclass in cls.__subclasses__():
            if subclass.nombre() == nombre:
                return subclass()

    @classmethod
    def nombre(cls):
        raise NotImplementedError("Subclass Responsibility")

    def __init__(self):
        self._token = settings.CRM_INTEGRATION_API_TOKEN

    def cliente(self):
        raise NotImplementedError("Subclass Responsibility")

    def constructor(self):
        raise NotImplementedError("Subclass Responsibility")

    def _obtener_informacion(self):
        cliente = self.cliente()
        return cliente.call()

    def realizar_actualizacion(self):
        constructor = self.constructor()
        datos_de_instancias = self._obtener_informacion()
        for datos_de_instancia in datos_de_instancias:
            constructor.construir(datos_de_instancia)


class ServicioDeMarcas(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'brand'

    def cliente(self):
        return CRMIntegrationAPIClient.brands_api_client(token=self._token)

    def constructor(self):
        return ConstructorDeMarcas()


class ServicioDeModelos(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'model'

    def cliente(self):
        return CRMIntegrationAPIClient.models_api_client(token=self._token)

    def constructor(self):
        return ConstructorDeModelos()


class ServicioDePlanes(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'plan'

    def cliente(self):
        return CRMIntegrationAPIClient.plans_api_client(token=self._token)

    def constructor(self):
        return ConstructorDePlanes()


class ServicioDeTiposDePlanes(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'plan-type'

    def cliente(self):
        return CRMIntegrationAPIClient.plan_types_api_client(token=self._token)

    def constructor(self):
        return ConstructorDeTiposDePlanes()


class ServicioDeConcesionariasExternas(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'external-concessioner'

    def cliente(self):
        return Run0KmAPIClient.concessioners_client(token=self._token)

    def constructor(self):
        return ConstructorDeConcesionariasExternas()

    def realizar_actualizacion(self):
        self.__class__().para_marcas().realizar_actualizacion()
        super(ServicioDeConcesionariasExternas, self).realizar_actualizacion()


class ServicioDePlanesDeConcesionariasExternas(ServicioDeIntegracion):
    @classmethod
    def nombre(cls):
        return 'external-concessioner-plan'

    def _cliente_para(self, pk_concesionaria_externa):
        return Run0KmAPIClient.concessioners_plans_client(token=self._token, concessioner_pk=pk_concesionaria_externa)

    def constructor(self):
        return ConstructorDePlanes()

    def realizar_actualizacion(self):
        self.__class__().para_concesionarias_externas().realizar_actualizacion()
        concesionarias_externas = ConcesionariaExterna.objects.all()
        for concesionaria_externa in concesionarias_externas:
            datos_de_planes = self._obtener_informacion_para(concesionaria_externa.identificador())
            self._crear_y_asociar_planes(datos_de_planes, concesionaria_externa)

    def _obtener_informacion_para(self, pk_concesionaria_externa):
        cliente = self._cliente_para(pk_concesionaria_externa)
        return cliente.call()

    def _crear_y_asociar_planes(self, datos_de_planes, concesionaria_externa):
        constructor = self.constructor()
        planes = []
        for datos_de_plan in datos_de_planes:
            plan = constructor.construir(datos_de_plan)
            planes.append(plan)
        constructor.asociar_planes_con_concesionaria_externa(planes, concesionaria_externa)






