from lib.api_client import ApiClient


class CRMIntegrationAPIClient(ApiClient):
    def __init__(self, endpoint, token):
        url = "http://www.run0km.com/integrations/api/{}/?allow-finalized=1".format(endpoint)
        super(CRMIntegrationAPIClient, self).__init__(url, ApiClient.GET)
        self._headers = {'Authorization': 'Token {}'.format(token)}
        self.response = None

    def name(self):
        return 'CRMIntegrationAPI'

    def headers(self):
        return self._headers

    def call(self, *args, **kwargs):
        response = super(CRMIntegrationAPIClient, self).call(*args, **kwargs)
        return response.json()

    @classmethod
    def brands_api_client(cls, token):
        return cls('brands', token)

    @classmethod
    def models_api_client(cls, token):
        return cls('models', token)

    @classmethod
    def plans_api_client(cls, token):
        return cls('plans', token)

    @classmethod
    def plan_types_api_client(cls, token):
        return cls('plan-types', token)


class Run0KmAPIClient(ApiClient):
    def __init__(self, endpoint, token):
        url = "http://www.run0km.com/api/{}/".format(endpoint)
        super(Run0KmAPIClient, self).__init__(url, ApiClient.GET)
        self._headers = {'Authorization': 'Token {}'.format(token)}

    def name(self):
        return 'Run0KmAPI'

    def headers(self):
        return self._headers

    def call(self, *args, **kwargs):
        response = super(Run0KmAPIClient, self).call(*args, **kwargs)
        return response.json()

    @classmethod
    def concessioners_client(cls, token):
        return cls('concessioners', token)

    @classmethod
    def concessioners_plans_client(cls, token, concessioner_pk):
        return cls('concessioners/{0}/plans'.format(concessioner_pk), token)
