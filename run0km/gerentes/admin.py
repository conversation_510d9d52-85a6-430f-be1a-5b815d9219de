# coding=utf-8
from django.contrib import admin
from gerentes.models import <PERSON>erente
from gerentes.admin_forms import AddGerenteAdminForm, ChangeGerenteAdminForm


class GerentesAdmin(admin.ModelAdmin):
    form = ChangeGerenteAdminForm
    list_display = ('user_fullname', 'user_username', 'user_email', 'concesionaria')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name')

    def user_fullname(self, obj):
        return obj.user.get_full_name()
    user_fullname.short_description = "Nombre completo"

    def user_username(self, obj):
        return obj.user.username
    user_username.short_description = "Nombre de usuario"

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = "Correo electrónico"

    def get_form(self, request, obj=None, **kwargs):
        self.exclude = ()
        self.readonly_fields = ()
        if obj is None:
            self.form = AddGerenteAdminForm
            self.exclude = ('user', )
        else:
            self.form = ChangeGerenteAdminForm
            self.readonly_fields = ('user', )
        return super(GerentesAdmin, self).get_form(request, obj, **kwargs)

admin.site.register(Gerente, GerentesAdmin)
