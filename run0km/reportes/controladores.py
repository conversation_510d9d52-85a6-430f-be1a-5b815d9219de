# coding=utf-8
from django.http import Http404
from reportes.forms import ReporteParaGerentesForm, ReporteParaSupervisoresForm, ProgramacionDeReporteParaGerenteForm, \
    ProgramacionDeReporteParaSupervisorForm
from reportes.models import TipoDeStaff, ProgramacionDeReporte
from reportes.generacion_de_reportes import ConfiguracionDeReporteParaGerente, ConfiguracionDeReporteParaSupervisor


class ControllerError(Exception):
    pass


class ReportesController(object):
    @classmethod
    def nuevo_para(cls, user):
        if user.is_gerente():
            return ReportesGerenteController()
        elif user.is_vendedor() and user.vendedor.es_supervisor():
            return ReportesSupervisorController()
        else:
            raise ControllerError('Usuario incorrecto')

    def opcion_de_staff(self):
        raise NotImplementedError('Subclass Responsibility')

    def programador(self, user):
        raise NotImplementedError('Subclass Responsibility')

    def get_form(self, user, data=None):
        raise NotImplementedError('Subclass Responsibility')

    def es_reporte_de_gerente(self):
        raise NotImplementedError('Subclass Responsibility')

    def configuracion_de_reportes_para(self, request, form):
        raise NotImplementedError('Subclass Responsibility')

    def get_programacion_form_class(self):
        raise NotImplementedError('Subclass Responsibility')

    def get_programacion_form_kwargs(self, request):
        programador = self.programador(user=request.user)
        kwargs = {'programador': programador}
        return kwargs

    def get_programacion(self, programacion_pk, request):
        tipo_de_programacion = ProgramacionDeReporte.tipo_para(request.user)
        programacion = tipo_de_programacion.objects.get(pk=programacion_pk, programador=self.programador(request.user))
        return programacion

    def nueva_programacion_de_reportes_para(self, equipos, mails, staff_a_cargo, tipo_de_frecuencia, tipo_de_staff,
                                            tipos_de_reportes, user):
        programacion = ProgramacionDeReporte.nuevo(
            programador=self.programador(user),
            tipos_de_reportes=tipos_de_reportes,
            tipo_de_frecuencia=tipo_de_frecuencia,
            tipo_de_staff=tipo_de_staff,
            equipos=equipos,
            staff_a_cargo=staff_a_cargo,
            mails=mails,
        )
        return programacion


    def programaciones_para(self, user):
        programador = self.programador(user)
        programaciones = programador.programaciones.all()
        return programaciones

    def get_programacion_or_404(self, programacion_pk, request):
        try:
            programacion = self.get_programacion(programacion_pk, request)
            return programacion
        except ProgramacionDeReporte.DoesNotExist:
            raise Http404("Programación no encontrada.")

    def editar_programacion_para(self, programacion, form):
        raise NotImplementedError('Subclass Responsibility')


class ReportesGerenteController(ReportesController):
    def opcion_de_staff(self):
        return 'supervisores'

    def get_form(self, user, data=None):
        return ReporteParaGerentesForm(data=data, gerente=user.gerente)

    def es_reporte_de_gerente(self):
        return True

    def configuracion_de_reportes_para(self, request, form):
        cd = form.cleaned_data
        tipo_de_staff = cd['tipo_de_staff']
        staff = cd['equipos'] if tipo_de_staff == TipoDeStaff.EQUIPOS else cd['staff_a_cargo']
        configuracion = ConfiguracionDeReporteParaGerente(desde=cd['desde'],
                                                          hasta=cd['hasta'],
                                                          tipo_de_staff=tipo_de_staff,
                                                          staff=staff,
                                                          tipos_de_reportes=cd['tipos_de_reportes'])
        return configuracion

    def get_programacion_form_class(self):
        return ProgramacionDeReporteParaGerenteForm

    def programador(self, user):
        return user.gerente

    def editar_programacion_para(self, programacion, form):
        programacion.editar(
            tipos_de_reportes=form.cleaned_data['tipos_de_reportes'],
            tipo_de_frecuencia=form.cleaned_data['tipo_de_frecuencia'],
            tipo_de_staff=form.cleaned_data['tipo_de_staff'],
            equipos=form.cleaned_data['equipos'],
            staff_a_cargo=form.cleaned_data['staff_a_cargo'],
            mails=form.cleaned_data['mails']
        )


class ReportesSupervisorController(ReportesController):
    def opcion_de_staff(self):
        return 'vendedores'

    def get_form(self, user, data=None):
        return ReporteParaSupervisoresForm(data=data, supervisor=user.vendedor)

    def es_reporte_de_gerente(self):
        return False

    def configuracion_de_reportes_para(self, request, form):
        cd = form.cleaned_data
        tipo_de_staff = cd['tipo_de_staff']
        staff = cd['equipos'] if tipo_de_staff == TipoDeStaff.EQUIPOS else cd['staff_a_cargo']
        configuracion = ConfiguracionDeReporteParaSupervisor(supervisor=self.programador(user=request.user),
                                                             desde=cd['desde'],
                                                             hasta=cd['hasta'],
                                                             tipo_de_staff=tipo_de_staff,
                                                             staff=staff,
                                                             tipos_de_reportes=cd['tipos_de_reportes'])
        return configuracion

    def get_programacion_form_class(self):
        return ProgramacionDeReporteParaSupervisorForm

    def programador(self, user):
        return user.vendedor

    def editar_programacion_para(self, programacion, form):
        programacion.editar(
            tipos_de_reportes=form.cleaned_data['tipos_de_reportes'],
            tipo_de_frecuencia=form.cleaned_data['tipo_de_frecuencia'],
            tipo_de_staff=form.cleaned_data['tipo_de_staff'],
            equipos=form.cleaned_data['equipos'],
            staff_a_cargo=form.cleaned_data['staff_a_cargo'],
            mails=form.cleaned_data['mails']
        )
