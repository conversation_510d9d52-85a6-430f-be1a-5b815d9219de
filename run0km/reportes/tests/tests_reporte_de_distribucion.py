from datetime import time

from django.utils import timezone

from campanias.models import CategoriaDeCampania
from core.support import make_aware_when_is_naive
from prospectos.models import FiltroDePedido
from prospectos.models import Proveedor
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from reportes.reporte_de_distribucion import GeneradorDeReporteDeDistribucion, RangoDeFechas, CalculosDeReporte, \
    CalculosDeRegion
from vendedores.models import Vendedor

"""
    {
        "categoria": "Ahora-84",
        "marca": "Toyota",
        "detalle": { //en el periodo
            "amba": {"cantidad_pedida": "50", "cantidad_entregada": "25", "porcentaje_entregado": "50" },
            "resto": {"cantidad_pedida": "10", "cantidad_entregada": "5", "porcentaje_entregado": "50" },
            "total": {"cantidad_pedida": "60", "cantidad_entregada": "30", "porcentaje_entregado": "50" }
        }
    }
"""


class GeneradorDeReporteDeDistribucionTest(PedidosTest):
    def setUp(self):
        super(GeneradorDeReporteDeDistribucionTest, self).setUp()
        self.generador = GeneradorDeReporteDeDistribucion.nuevo()
        self.proveedor = Proveedor.objects.create(source_id='pprod')
        self.categoria = self.campania_uno.categoria

    def test_cantidad_pedida_mensual_para_supervisor_y_marca(self):
        rango = RangoDeFechas.mes_pasado()
        ford = 'Ford'
        cantidad = 100
        supervisor_uno = self._crear_supervisor()
        self._agregar_pedido_para_marca(
            supervisor=supervisor_uno, campania=self.campania_uno, cantidad_pedida=cantidad,
            marca=ford, fecha=rango.fecha_desde())

        calculos = self._crear_calculos_para(
            categorias=[self.categoria], supervisores=[supervisor_uno], proveedores=[self.proveedor])
        reporte_de_cantidades = self.generador.reporte_cantidad_pedida_y_entregada_para(
            calculos=calculos,
            marcas=[ford],
            fecha_desde=rango.fecha_desde(),
            fecha_hasta=rango.fecha_hasta())
        self._assert_reporte_de_cantidades_pedidas_y_entregadas(
            reporte_de_cantidades,
            marca=ford,
            cantidad_pedida=cantidad)

    def test_cantidad_pedida_mensual_para_supervisor_y_marca_con_detalles_de_region(self):
        rango = RangoDeFechas.mes_pasado()
        ford = 'Ford'
        cantidad_amba = 100
        cantidad_resto = 23
        cantidad_indefinida = 44
        supervisor_uno = self._crear_supervisor()
        self._crear_pedido_para_marca_y_region(cantidad_amba, ford, 'amba', rango, supervisor_uno)
        self._crear_pedido_para_marca_y_region(cantidad_resto, ford, 'resto', rango, supervisor_uno)
        self._crear_pedido_para_marca_y_region(cantidad_indefinida, ford, 'indefinido', rango, supervisor_uno)

        calculos = self._crear_calculos_para(
            categorias=[self.categoria], supervisores=[supervisor_uno], proveedores=[self.proveedor])
        reporte_de_cantidades = self.generador.reporte_cantidad_pedida_y_entregada_para(
            calculos=calculos, marcas=[ford], fecha_desde=rango.fecha_desde(), fecha_hasta=rango.fecha_hasta())
        self._assert_reporte_de_cantidades_pedidas_y_entregadas(
            reporte_de_cantidades, marca=ford, cantidad_pedida=cantidad_amba + cantidad_resto + cantidad_indefinida)

        self._assert_reporte_de_cantidades_pedidas_y_entregadas_para_region(
            reporte_de_cantidades, region='amba', marca=ford, cantidad_pedida=cantidad_amba + cantidad_indefinida / 2)

        self._assert_reporte_de_cantidades_pedidas_y_entregadas_para_region(
            reporte_de_cantidades, region='resto', marca=ford, cantidad_pedida=cantidad_resto + cantidad_indefinida / 2)

    def test_reporte_sin_asignar_debe_contar_los_prospectos_sin_responsable_sin_tener_en_cuenta_el_rango_de_fechas(
            self):
        rango = RangoDeFechas.mes_pasado()
        ford = 'Ford'
        toyota = 'Toyota'
        cantidad_de_ford = 100
        cantidad_de_toyota = 15
        supervisor_uno = self._crear_supervisor()
        self.creador_de_contexto.crear_prospectos_nuevos(
            cantidad=cantidad_de_ford, campania=self.campania_uno, marca=ford,
            fecha=self._fecha_relativa_a_rango(rango, delta_dias=1), proveedor=self.proveedor
        )
        self.creador_de_contexto.poner_a_cargo_prospectos_nuevos(
            supervisor=supervisor_uno, cantidad=5, campania=self.campania_uno, marca=ford,
            fecha=self._fecha_relativa_a_rango(rango, delta_dias=1), proveedor=self.proveedor)
        self.creador_de_contexto.crear_prospectos_nuevos(
            cantidad=cantidad_de_toyota, campania=self.campania_uno, marca=toyota,
            fecha=self._fecha_relativa_a_rango(rango, delta_dias=-1), proveedor=self.proveedor
        )
        calculos = self._crear_calculos_para(
            categorias=[self.categoria], supervisores=[supervisor_uno], proveedores=[self.proveedor])
        reporte = self.generador.reporte_de_prospectos_sin_asignar(calculos)

        self._assert_reporte_de_prospectos_sin_asignar_total(
            reporte, categoria=self.categoria, total=cantidad_de_ford+cantidad_de_toyota,
            cantidad_por_marca={ford: cantidad_de_ford, toyota: cantidad_de_toyota}
        )

    # def test_reporte_de_entregas_por_categoria(
    # NO FUNCIONA LA CONSULTA EN SQLITE
    #         self):
    #     rango = RangoDeFechas.mes_pasado()
    #     ford = 'Ford'
    #     toyota = 'Toyota'
    #     cantidad_de_ford = 100
    #     cantidad_de_toyota = 15
    #     supervisor_uno = self._crear_supervisor()
    #     fecha_en_rango = self._fecha_relativa_a_rango(rango, delta_dias=1)
    #     fecha_fuera_de_rango = self._fecha_relativa_a_rango(rango, delta_dias=-1)
    #     self.creador_de_contexto.crear_prospectos_nuevos(
    #         cantidad=10, campania=self.campania_uno, marca=ford,
    #         fecha=fecha_en_rango, proveedor=self.proveedor
    #     )
    #     self.creador_de_contexto.poner_a_cargo_prospectos_nuevos(
    #         supervisor=supervisor_uno, cantidad=cantidad_de_ford, campania=self.campania_uno, marca=ford,
    #         fecha_de_asignacion_a_supervisor=fecha_en_rango,
    #         proveedor=self.proveedor
    #     )
    #     self.creador_de_contexto.poner_a_cargo_prospectos_nuevos(
    #         supervisor=supervisor_uno, cantidad=cantidad_de_toyota, campania=self.campania_uno, marca=toyota,
    #         fecha_de_asignacion_a_supervisor=fecha_fuera_de_rango,
    #         proveedor=self.proveedor
    #     )
    #     calculos = self._crear_calculos_para(
    #         categorias=[self.categoria], supervisores=[supervisor_uno], proveedores=[self.proveedor])
    #     reporte = self.generador.reporte_de_entregas_por_categoria(
    #         calculos,
    #         marcas=[ford, toyota],
    #         fecha_desde=rango.fecha_desde(),
    #         fecha_hasta=rango.fecha_hasta()
    #     )
    #
    #     self._assert_reporte_de_entregas(
    #         reporte, categoria=self.categoria, total={{self._formatear_fecha(fecha_en_rango): cantidad_de_ford}},
    #         marcas={ford: {self._formatear_fecha(fecha_en_rango): cantidad_de_ford}}
    #     )

    def _assert_reporte_de_cantidades_pedidas_y_entregadas(self, reportes, marca, cantidad_pedida):
        reporte = self._reporte_para_marca(marca, reportes)
        self.assertEqual(reporte.get('cantidad_pedida', 0), cantidad_pedida)
        self.assertEqual(reporte.get('cantidad_entregada', 0), 0)
        self.assertEqual(reporte.get('marca', ''), marca)

    def _assert_reporte_de_cantidades_pedidas_y_entregadas_para_region(self, reportes, region, marca, cantidad_pedida):
        reporte = self._reporte_para_marca(marca, reportes)
        reporte_de_region = reporte.get('detalle', {}).get(region, {})
        self.assertEqual(reporte_de_region.get('cantidad_pedida', 0), cantidad_pedida)
        self.assertEqual(reporte_de_region.get('cantidad_entregada', 0), 0)

    def _assert_reporte_de_prospectos_sin_asignar_total(self, reporte, categoria, total, cantidad_por_marca):
        # {'categoria_id': {nombre: '...', total: 45, datos: {'Volkswagen': 15, ...}}
        reporte_categoria = reporte.get(categoria.id, {})
        self.assertEqual(reporte_categoria.get('nombre'), categoria.nombre)
        self.assertEqual(reporte_categoria.get('total', 0), total)
        self.assertEqual(reporte_categoria.get('datos', {}), cantidad_por_marca)

    def _assert_reporte_de_entregas(self, reporte, categoria, total, marcas):
        # {'categoria_id': { nombre: '...', total: {{ dia: cantidad, ...}, ...},
        # 'marcas': { 'marca': { dia: cantidad, ...}, ... }}, }
        reporte_categoria = reporte.get(categoria.id, {})
        self.assertEqual(reporte_categoria.get('total', {}), total)

    def _crear_pedido_para_marca_y_region(self, cantidad_amba, marca, region, rango, supervisor_uno):
        pedido = self._agregar_pedido_para_marca(
            supervisor=supervisor_uno, campania=self.campania_uno, cantidad_pedida=cantidad_amba,
            marca=marca, fecha=rango.fecha_desde())
        if region == 'amba':
            self._agregar_filtro_para_localidad(pedido, 'amba')
        elif region == 'resto':
            self._agregar_filtro_para_localidad(pedido, 'San Rafael')
        return pedido

    def _agregar_pedido_para_marca(self, fecha, supervisor, campania, cantidad_pedida, marca):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=campania.categoria.valor*cantidad_pedida, fecha=fecha,
        )
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido, 'marca', FiltroDePedido.CONTIENE, marca)
        return pedido

    def _crear_calculos_para(self, categorias, supervisores, proveedores):
        return CalculosDeReporte.nuevo(
            categorias=CategoriaDeCampania.objects.filter(id__in=[categoria.id for categoria in categorias]),
            responsables=Vendedor.all_objects.filter(id__in=[supervisor.id for supervisor in supervisores]),
            proveedores=Proveedor.objects.filter(id__in=[proveedor.id for proveedor in proveedores])
        )

    def _agregar_filtro_para_localidad(self, pedido, localidad):
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor=localidad)

    def _reporte_para_marca(self, marca, reportes):
        for reporte in reportes:
            marca_del_reporte = reporte.get('marca', '')
            if marca == marca_del_reporte:
                return reporte
        return None

    def _fecha_relativa_a_rango(self, rango, delta_dias):
        dia = rango.fecha_desde()
        fecha = make_aware_when_is_naive(timezone.datetime.combine(dia, time()))
        return fecha + timezone.timedelta(days=delta_dias)

    def _formatear_fecha(self, fecha):
        return timezone.datetime.strftime(fecha, '%d-%m-%Y')


class CalculosDeRegionTest(PedidosTest):

    def test_pedido_con_filtro_por_localidad_del_resto_pertence_a_la_region_resto(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor='San Rafael')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertFalse(region.es_region_indefinida())
        self.assertTrue(region.no_es_amba())

    def test_pedido_con_filtro_por_localidad_capital_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor='capital')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertFalse(region.es_region_indefinida())
        self.assertTrue(region.es_amba())

    def test_pedido_con_filtro_por_localidad_caba_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor='caba')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertTrue(region.es_amba())

    def test_pedido_con_filtro_por_localidad_amba_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor='amba')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertTrue(region.es_amba())

    def test_pedido_con_filtro_por_prefijo_11_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='prefijo', selector=FiltroDePedido.CONTIENE, valor='11')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertTrue(region.es_amba())

    def test_pedido_con_filtro_por_telefono_que_comienza_con_11_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='telefono', selector=FiltroDePedido.PREFIJO, valor='11')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertTrue(region.es_amba())

    def test_pedido_con_filtro_por_telefono_que_comienza_con_011_pertence_a_la_region_amba(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor)
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido, campo='telefono', selector=FiltroDePedido.PREFIJO, valor='011')

        region = CalculosDeRegion.de_pedido(pedido)
        self.assertTrue(region.es_amba())
