# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0016_auto_20160217_1425'),
        ('equipos', '0001_initial'),
        ('gerentes', '__first__'),
        ('reportes', '0008_auto_20160407_1313'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProgramacionDeReporteParaGerentes',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tipo_de_frecuencia', models.CharField(default=b'S', max_length=1, choices=[(b'S', b'Semanal'), (b'M', b'Mensual')])),
                ('selecciono_equipos', models.BooleanField(default=False)),
                ('equipos', models.ManyToManyField(default=None, to='equipos.Equipo', null=True, blank=True)),
                ('mails', models.ManyToManyField(to='reportes.Mail')),
                ('programador', models.ForeignKey(related_name='programaciones', to='gerentes.Gerente')),
                ('staff_a_cargo', models.ManyToManyField(default=None, related_name='programaciones_de_gerente', null=True, to='vendedores.Vendedor', blank=True)),
                ('tipos_de_reportes', models.ManyToManyField(to='reportes.TipoDeReporte')),
            ],
            options={
                'abstract': False,
                'verbose_name': 'programaci\xf3n de reporte',
                'verbose_name_plural': 'programaciones de reportes',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ProgramacionDeReporteParaSupervisores',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tipo_de_frecuencia', models.CharField(default=b'S', max_length=1, choices=[(b'S', b'Semanal'), (b'M', b'Mensual')])),
                ('selecciono_equipos', models.BooleanField(default=False)),
                ('equipos', models.ManyToManyField(default=None, to='equipos.Equipo', null=True, blank=True)),
                ('mails', models.ManyToManyField(to='reportes.Mail')),
                ('programador', models.ForeignKey(related_name='programaciones', to='vendedores.Vendedor')),
                ('staff_a_cargo', models.ManyToManyField(default=None, related_name='programaciones_de_supervisor', null=True, to='vendedores.Vendedor', blank=True)),
                ('tipos_de_reportes', models.ManyToManyField(to='reportes.TipoDeReporte')),
            ],
            options={
                'abstract': False,
                'verbose_name': 'programaci\xf3n de reporte',
                'verbose_name_plural': 'programaciones de reportes',
            },
            bases=(models.Model,),
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='concesionaria',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='equipos',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='mails',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='supervisor',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='supervisores',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='tipos_de_reportes',
        ),
        migrations.RemoveField(
            model_name='programaciondereporte',
            name='vendedores',
        ),
        migrations.DeleteModel(
            name='ProgramacionDeReporte',
        ),
    ]
