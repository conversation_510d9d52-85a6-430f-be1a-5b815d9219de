# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-08-31 20:44


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_sistema_facebook_deshabilitado'),
    ]

    operations = [
        migrations.AddField(
            model_name='sistema',
            name='factor_criterio_conversiones_para_seleccion_de_participantes',
            field=models.IntegerField(default=1, help_text='Factor para la selecci\xf3n de participantes de compulsa por cantidad de conversiones'),
        ),
        migrations.AddField(
            model_name='sistema',
            name='factor_criterio_ultima_actividad_para_seleccion_de_participantes',
            field=models.IntegerField(default=1, help_text='Factor para la selecci\xf3n de participantes de compulsa por ultima actividad'),
        ),
        migrations.AddField(
            model_name='sistema',
            name='factor_criterio_ventas_para_seleccion_de_participantes',
            field=models.IntegerField(default=1, help_text='Factor para la selecci\xf3n de participantes de compulsa por cantidad de ventas de chat'),
        ),
        migrations.AlterField(
            model_name='sistema',
            name='regla_ventas_mensuales_maximo',
            field=models.PositiveIntegerField(default=100, help_text='Valor m\xe1ximo de regla ventas mensuales. Ejemplo [Max: 100, Min: -50]. N\xfamero positivo de 0 a 99'),
        ),
    ]
