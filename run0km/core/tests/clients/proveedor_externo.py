import json

from django.urls import reverse


class ProveedorExterno(object):
    """
        Proveedor externo que se cominica via un post http. Por ahora queda pegado el metodo de autenticacion.
    """

    @classmethod
    def nuevo_para(cls, usuario, http_client):
        return cls(http_client, usuario.auth_token.key)

    @classmethod
    def nuevo_con_usuario_por_defecto(cls, creador_de_contexto, http_client):
        usuario = cls._usuario_proveedor(creador_de_contexto)
        return cls.nuevo_para(usuario, http_client)

    @classmethod
    def _usuario_proveedor(cls, creador_de_contexto):
        usuario = creador_de_contexto.crear_usuario()
        creador_de_contexto.agregar_token_para(usuario)
        return usuario

    def __init__(self, http_client, token_key):
        super(ProveedorExterno, self).__init__()
        self._http_client = http_client
        self._token_key = token_key

    def _post(self, datos, content_type=None):
        content_type = content_type or self._content_type()
        response = self._http_client.post(
            self._url(), datos, HTTP_AUTHORIZATION='Token %s' % self._token_key, content_type=content_type)
        return response

    def _post_json(self, datos):
        response = self._post(json.dumps(datos), content_type='application/json')
        return response

    def _url(self):
        return reverse(self._nombre_de_endpoint())

    def _content_type(self):
        return 'application/json'

    def _nombre_de_endpoint(self):
        raise NotImplementedError('subclass responsibility')