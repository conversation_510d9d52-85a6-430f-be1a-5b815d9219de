
class ServicioNormalizarError(Exception):
    @classmethod
    def error_type(cls):
        raise NotImplementedError('Subclass responsibility')

    def __init__(self, e, request=None, response=None, *args, **kwargs):
        self.request = request
        self.response = response
        super(ServicioNormalizarError, self).__init__(e, *args, **kwargs)


class ServicioNormalizarComunicacionError(ServicioNormalizarError):
    @classmethod
    def error_type(cls):
        return 'El serivicio no responde'


class NormalizacionTelefonoError(ServicioNormalizarError):
    @classmethod
    def error_type(cls):
        return 'Error al normalizar un telefono'


class ServicioNormalizarRespuestaInesperadaError(ServicioNormalizarError):
    @classmethod
    def error_type(cls):
        return 'Respuesta inesperada'
