from unittest import skipIf, TestCase

import requests

from zeep import Client
from zeep.transports import Transport
from django.conf import settings

from lib.normalizador import NormalizadorDeTelefonos, NUMERO, TELCO, MOVIL, \
    BIEN_CONSTITUIDO, SPAM, ID
from lib.normalizador.errors import ServicioNormalizarComunicacionError, NormalizacionTelefonoError


@skipIf(not hasattr(settings, 'REALIZAR_NORMALIZACION_TEST') or not settings.REALIZAR_NORMALIZACION_TEST,
        'No esta habilitada la ejecucion de tests de normalizacion, configurar en true REALIZAR_NORMALIZACION_TEST')
class NormalizacionRealTest(TestCase):
    def setUp(self):
        super(NormalizacionRealTest, self).setUp()
        self.wsdl = settings.NORMALIZACION_SERVICE_URL
        self.wsdl = 'https://portabilidad.coverone.la/services/Normalizer.svc?WSDL'

    def test_deberia_fallar_coneccion(self):
        url_invalida = 'http://156465489132nada.com/service1.svc?WSDL'
        normalizador = NormalizadorDeTelefonos.nuevo_con_url(url_invalida)
        self.assertRaises(ServicioNormalizarComunicacionError, normalizador.normalizar, numeros_telefonicos=[('1', '')])

    def test_deberia_fallar_normalizacion_por_datos_incorrectos(self):
        normalizador = NormalizadorDeTelefonos.nuevo_con_url(self.wsdl)
        response = normalizador.normalizar([('1', '')])
        self.assertRaises(NormalizacionTelefonoError, response.get_telefono, codigo='1')

    def test_deberia_responder_telefono_normalizado(self):
        normalizador = NormalizadorDeTelefonos.nuevo_con_url(self.wsdl)
        response = normalizador.normalizar([('prospecto1', '1122368515')])

        self.assertEqual(response.cantidad_de_telefonos(), 1)
        try:
            telefono = response.get_telefono('prospecto1')
        except ValueError:
            self.fail("Deberia encontrarse el telefono normalizado")
        else:
            self.assertIsNotNone(telefono)
            self.assertEqual('prospecto1', telefono[ID].lower())
            self.assertEqual('1122368515', telefono[NUMERO])
            self.assertEqual('TELECOM ARGENTINA SOCIEDAD ANONIMA', telefono[TELCO])
            self.assertTrue(telefono[MOVIL])
            self.assertTrue(telefono[BIEN_CONSTITUIDO])
            self.assertFalse(telefono[SPAM])

    # no prueba nada, es solo un ejemplo de uso
    def ejemplo_suds_pelado(self):
        wsdlUrl = 'https://portabilidad.coverone.la/services/Normalizer.svc?WSDL'
        session = requests.Session()
        client = Client(wsdl=wsdlUrl, transport=Transport(session=session))
        endpoint_url = 'https://portabilidad.coverone.la/services/Normalizer.svc'
        client.service._binding_options['address'] = endpoint_url
        query = '<xml><telefonos><telefono codigo="1">2346440223</telefono></telefonos></xml>'

        try:
            response = client.service.Normalizar(query)
        except Exception as exc:
            print('error')
            print(exc)
        else:
            print(response)
