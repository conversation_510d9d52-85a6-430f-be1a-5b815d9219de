# inspired from http://code.activestate.com/recipes/366254-generic-proxy-object-with-beforeafter-method-hooks/
import inspect
from django.contrib.admin import ModelAdmin


class MethodWrapper(object):
    """
    Wrapper object for a method to be called.
    """

    def __init__(self, wrapper, method):
        self._wrapper = wrapper
        self._method = method

    def __call__(self, *args, **kwargs):
        # evaluate the method with in the context of the proxy (delegation)
        answer = self._method(self._wrapper, *args, **kwargs)
        return answer

    def __getattr__(self, name):
        return getattr(self._method, name)


def propserties_of(wrapped):
    properties = inspect.getmembers(wrapped.__class__, lambda member: isinstance(member, property))
    properties = {each[0]:each[1] for each in properties}
    return properties


class ModelAdminWrapper(ModelAdmin):
    # An improper use of `is` in the AdminSite >> #register forces to inherit from ModelAdmin
    def __init__(self, wrapped):
        self._wrapped = wrapped

    # def __setattr__(self, name, value): # real signature unknown; restored from __doc__
    #     """
    #     I need override it because it depends of __getattribute__
    #     """
    #     attributes = object.__getattribute__(self, '__dict__')
    #     attributes[name] = value

    def __getattribute__(self, item):
        """
        Return a method wrapper object if this is a method call.
        """
        if item in object.__getattribute__(self, '__dict__') or item in object.__getattribute__(self,
                                                                                                '__class__').__dict__:
            return object.__getattribute__(self, item)
        wrapped = object.__getattribute__(self, '_wrapped')

        properties = propserties_of(wrapped)
        if item in properties:
            property_item = properties[item]
            return property_item.__get__(self)

        attribute = getattr(wrapped, item)
        if inspect.ismethod(attribute):
            unbound_method = getattr(wrapped.__class__, item)
            return MethodWrapper(wrapper=self, method=unbound_method)
        else:
            return attribute

            # def __type__(self):
            #     return type(self._wrapped)
