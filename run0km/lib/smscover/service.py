import logging
import random
import string
from datetime import time

from django.conf import settings
from django.utils.module_loading import import_string


from lib.smscover.helpers import TemplateMessageInvalidParameter, TimeRangeList, SMSSendingSettings, SMSSystem
from lib.smscover.errors import SMSCoverAPIError

logger = logging.getLogger(__name__)


class SMSClientPortfolio(object):
    def __init__(self, clients, default_named):
        super(SMSClientPortfolio, self).__init__()
        self._clients = clients
        self._default = self._clients.get(default_named, None)

    def clients(self):
        return list(self._clients.values())

    def client_named(self, name):
        client = self._clients.get(name, None)
        if not client:
            raise ValueError('Client named %s not found' % name)
        else:
            return client

    def update_status(self, query_response):
        logger.debug("update_status")
        xml_response = query_response.xml
        invalid_lots = self._update_status_with_sending_list(query_response.lot_sending_list(), xml_response)
        logger.debug("invalid lots: %s" % invalid_lots)
        answers = query_response.get_all_answers()
        self._update_status_to_answers(answers, xml_response)

    def _update_status_to_answers(self, answers, xml_response):
        for client in self.clients():
            client.update_answer_list(answers, xml_response)

    def _update_status_with_sending_list(self, lot_sending_list, xml_response):
        invalid_lots = []
        any_lot_request_is_none = False
        for lot_sending in lot_sending_list:
            lot_request = lot_sending.lot_request
            if lot_request is None and not any_lot_request_is_none:
                any_lot_request_is_none = True
                logger.debug("Response with None lot request")
                logger.debug("XML Response: %s" % xml_response)
            self._with_client_for(lot_request,
                                  if_present_do=lambda client: client.update_sending_list(lot_sending, xml_response),
                                  if_absent_do=lambda: invalid_lots.append(lot_sending))
        return invalid_lots

    def _with_client_for(self, lot_request, if_present_do, if_absent_do):
        if lot_request is None:
            if self._default is not None:
                if_present_do(self._default)
            else:
                if_absent_do()
            return
        for each in self.clients():
            if each.has_identifier(lot_request):
                if_present_do(each)
        if_absent_do()

    @classmethod
    def new_with(cls, strategies, default_named=None):
        clients = cls._create_clients(strategies)
        return cls(clients, default_named)

    @classmethod
    def _create_clients(cls, strategies):
        client_count = len(strategies)
        digits = len(str(client_count))
        clients = {}
        for index in range(0, client_count):
            client = cls._create_client_for(digits, index, strategies)
            clients[client.name()] = client
        return clients

    @classmethod
    def _create_client_for(cls, digits, index, strategies):
        prefix = str(index)
        prefix = prefix.zfill(digits)
        strategy = strategies[index]
        client = SMSServiceClient.new_with_client_prefix(name=strategy.name(),
                                                         prefix=prefix,
                                                         strategy=strategy)
        return client


class SMSServiceClient(object):
    def __init__(self, name, client_lot, strategy):
        super(SMSServiceClient, self).__init__()
        self._name = name
        self._client_lot = client_lot
        self._strategy = strategy

    def name(self):
        return self._name

    def has_identifier(self, client_identifier):
        return self._client_lot.has_identifier(client_identifier)

    def update_answer_list(self, answer_list, xml_response):
        self._strategy.update_answer_list(answer_list, xml_response)

    def update_sending_list(self, lot_sending, xml_response):
        lot_request = self._client_lot.lot_identifier_of(lot_sending.lot_request)
        self._strategy.update_sending_list(lot_request, lot_sending.sending_list(), xml_response)

    def client_identifier_for(self, lot_identifier):
        return self._client_lot.client_identifier_for(lot_identifier)

    def handle_template_message_invalid_parameter_exception(self, error, lot_send_request):
        return self._strategy.persistence_strategy.handle_error(error=error, lot_send_request=lot_send_request)

    def handle_api_error(self, error, lot_generator):
        return self._strategy.handle_sending_error(error=error, lot_send_request=lot_generator,
                                                   request=error.request, response=error.response)

    def successful_send_request(self, lot_generator, messages):
        return self._strategy.successful_send_request(lot_generator, messages)

    @classmethod
    def new_with_client_prefix(cls, name, prefix, strategy):
        client_lot = SMSClientLot.new_with(prefix)
        return cls(name, client_lot, strategy)


class SMSClientLot(object):
    def __init__(self, prefix):
        super(SMSClientLot, self).__init__()
        self._prefix = prefix

    def has_identifier(self, client_identifier):
        return client_identifier.startswith(self._prefix)

    def client_identifier_for(self, lot_identifier):
        return self._prefix + lot_identifier

    def lot_identifier_of(self, client_identifier):
        if client_identifier is None:
            return None
        if self.has_identifier(client_identifier):
            return client_identifier[len(self._prefix):]
        else:
            return ValueError('The lot identifier %s is not valid to this client' % client_identifier)

    @classmethod
    def new_with(cls, prefix):
        return cls(prefix)


class SMSServiceStrategy(object):
    def __init__(self, strategy_name):
        super(SMSServiceStrategy, self).__init__()
        self._name = strategy_name

    def name(self):
        return self._name

    def update_sending_list(self, lot_request, sending_list, xml_response):
        raise NotImplementedError('Subclass responsibility')

    def update_answer_list(self, answer_list, xml_response):
        raise NotImplementedError('Subclass responsibility')

    def successful_send_request(self, lot_send_request, messages):
        raise NotImplementedError('Subclass responsibility')

    def handle_sending_error(self, error, lot_send_request, request='', response=''):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def named(cls, strategy_name):
        return cls(strategy_name)


class SMSServiceSkipStrategy(SMSServiceStrategy):
    def update_sending_list(self, lot_request, sending_list, xml_response):
        pass

    def update_answer_list(self, answer_list, xml_response):
        pass

    def handle_sending_error(self, error, lot_send_request, request='', response=''):
        pass

    def successful_send_request(self, lot_generator, messages):
        pass


class SMSService(object):
    def __init__(self, client_portfolio, sender=None):
        self._client_portfolio = client_portfolio
        self.sender = sender or self._default_sender()

    def _default_sender(self):
        client_class = self._import_class_from(settings.SMSCOVER_CLIENT_CLASS)
        client = client_class(settings.SMSCOVER_USER, settings.SMSCOVER_KEY)
        time_range_list = TimeRangeList.new_with('1', time(settings.SMSCOVER_ENVIOS_HORARIO_INICIO),
                                                 time(settings.SMSCOVER_ENVIOS_HORARIO_FIN))
        sms_settings = SMSSendingSettings(time_range_list=time_range_list)
        sender_class = self._import_class_from(settings.SMSCOVER_SMS_SENDER_CLASS)
        return sender_class(client, sms_settings, SMSSystem())

    def _import_class_from(self, variable):
        try:
            sender_class = import_string(variable)
        except ImportError:
            raise ValueError('Debe configurar el servicio de chat: %s' % variable)
        return sender_class

    def send(self, client_name, messages, lot_generator):
        client = self._client_portfolio.client_named(client_name)
        lot_name = client.client_identifier_for(lot_generator.lot_identifier())

        try:
            self.sender.send_lot(lot_name, messages)
        except Exception as e:
            SMSServiceErrorHandler.for_this(exception=e).handle_from(client=client, lot_generator=lot_generator)
            return False
        else:
            client.successful_send_request(lot_generator=lot_generator, messages=messages)
            return True

    def update_status(self):
        query_response = self.sender.query_status()
        return self._client_portfolio.update_status(query_response)

    @classmethod
    def new_with(cls, strategies, default_named=None, sender=None):
        client_portfolio = SMSClientPortfolio.new_with(strategies, default_named)
        return cls(client_portfolio, sender=sender)


class LotGenerator(object):
    def lot_identifier(self):
        raise NotImplementedError('Subclass responsibility')


class MessageLotSendRequestMock(LotGenerator):
    _SIZE = 15

    def lot_identifier(self):
        return ''.join([random.choice(string.digits) for _ in range(self._SIZE)])

# class PersistAllMessageLotRequests(SMSServicePersistenceStrategy):
#     def persist_message_lot_request(self, messages):
#         lot_send_request = MessageLotSendRequest.new()
#         lot_send_request.add_message_requests_for(messages)
#
#         return lot_send_request
#
#     def persist_error(self, type_of_error, description, lot_send_request):
#         SMSErrorLog.new(lot_request=lot_send_request, type=type_of_error, description=description)
#
#     def persist_successful_send_request(self, lot_send_request, messages):
#         lot_send_request.message_requests().mark_as_in_process()


class SMSServiceErrorHandler(object):
    @classmethod
    def for_this(cls, exception):
        for subclass in cls.__subclasses__():
            if subclass.can_handle(exception):
                return subclass(exception)

        return DefaultErrorHandler(exception)

    @classmethod
    def can_handle(cls, exception):
        raise NotImplementedError('Subclass responsibility')

    def __init__(self, exception):
        self.error = exception

    def handle_from(self, client, lot_generator):
        raise NotImplementedError('Subclass responsibility')


class TemplateMessageInvalidParameterErrorHandler(SMSServiceErrorHandler):
    @classmethod
    def can_handle(cls, exception):
        return isinstance(exception, TemplateMessageInvalidParameter)

    def handle_from(self, client, lot_generator):
        client.handle_template_message_invalid_parameter_exception(error=self.error,
                                                                   lot_generator=lot_generator)


class SMSCoverAPIErrorErrorHandler(SMSServiceErrorHandler):
    @classmethod
    def can_handle(cls, exception):
        return isinstance(exception, SMSCoverAPIError)

    def handle_from(self, client, lot_generator):
        client.handle_api_error(error=self.error, lot_generator=lot_generator)


class DefaultErrorHandler(SMSServiceErrorHandler):
    @classmethod
    def can_handle(cls, exception):
        return False

    def handle_from(self, client, lot_generator):
        raise self.error
