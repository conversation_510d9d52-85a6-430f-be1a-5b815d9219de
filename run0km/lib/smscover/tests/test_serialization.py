# coding=utf-8
import unittest
from datetime import time, datetime

from lib.smscover import TimeRange, TimeRangeList, SMSSendingSettings, SMSSystem, TemplateMessageInvalidParameter
from lib.smscover.api import SMSMessage, SMSCoverClient, SMSMessageList, SMSCoverRequest


class SMSRequestSerializationTest(unittest.TestCase):
    def assert_serialization(self, sms_object, xml):
        self.assertEqual(str(xml), str(sms_object.serialize_as_xml()))

    def test_client(self):
        client = SMSCoverClient('helper', 'pass12')
        self.assert_serialization(client, '<user>helper</user><key>pass12</key>')

    def test_time_range(self):
        time_from = time(9, 10)
        time_to = time(14, 25)
        range = TimeRange('1', time_from, time_to)

        self.assert_serialization(range, '<rango-horario id="1"><hora-desde am-pm="am">9:10</hora-desde>' 
                                         '<hora-hasta am-pm="pm">2:25</hora-hasta></rango-horario>')

    def test_time_range_list(self):
        time_from = time(9, 10)
        time_to = time(14, 25)
        range_list = TimeRangeList.new_with('1', time_from, time_to)

        self.assert_serialization(range_list,
                                  '<rangos-horarios><rango-horario id="1"><hora-desde am-pm="am">9:10</hora-desde>' 
                                  '<hora-hasta am-pm="pm">2:25</hora-hasta></rango-horario></rangos-horarios>')

    def test_send_settings(self):
        sms_settings = SMSSendingSettings('codigo-lote-cliente', datetime(2015, 1, 6), datetime(2015, 1, 8),
                                          time_range_list=TimeRangeList.new_with('1', time(8), time(22)))

        settings_xml = '<lote-request>codigo-lote-cliente</lote-request>' \
                       '<formato>NoClass_7Bit</formato>' \
                       '<bocas></bocas>' \
                       '<prioridad>6</prioridad>' \
                       '<fecha-comienzo>2015/01/06</fecha-comienzo>' \
                       '<fecha-fin>2015/01/08</fecha-fin>' \
                       '<continuacion>0</continuacion>' \
                       '<rangos-horarios>' \
                       '<rango-horario id="1"><hora-desde am-pm="am">8:00</hora-desde>' \
                       '<hora-hasta am-pm="pm">10:00</hora-hasta></rango-horario></rangos-horarios>'

        self.assert_serialization(sms_settings, settings_xml)

    def test_system_configuration(self):
        sms_system = SMSSystem()  # the default values
        self.assert_serialization(sms_system,
                                  '<metadata><server>stringway.smscover.com</server><version>Gateway_V3</version><accion>0</accion></metadata>')

    def test_message_text(self):
        message = SMSMessage.new_from_text('60-20100321-213321-00796995-00702693',
                                           'Ariel, el jueves 10.30 hs. tiene un turno con el dr Perez, responda anular si no puede asistir',
                                           '1164462659', '+54')
        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Ariel, el jueves 10.30 hs. tiene un turno con el dr Perez, responda anular si no puede asistir</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_template(self):
        message = SMSMessage.new_from_template('60-20100321-213321-00796995-00702693',
                                               '{name}, {appointment}. tiene un turno con el dr Perez, responda anular si no puede asistir',
                                               {'name': 'Ariel', 'appointment': 'el jueves 10.30 hs'}, '1164462659',
                                               '+54')

        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Ariel, el jueves 10.30 hs. tiene un turno con el dr Perez, responda anular si no puede asistir</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_template_with_invalid_evaluation(self):
        message = SMSMessage.new_from_template('60-20100321-213321-00796995-00702693',
                                               '{name}, {appointment}. tiene un turno con el dr Perez, responda anular si no puede asistir',
                                               {'name': 'Ariel'}, '1164462659',
                                               '+54')

        self.assertRaises(TemplateMessageInvalidParameter, message.serialize_as_xml)

    def test_message_text_escape_invalid_xml_characters(self):
        message = SMSMessage.new_from_text('60-20100321-213321-00796995-00702693',
                                           'Ariel>, Financiación.', '1164462659', '+54')
        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Ariel&gt;, Financiacion.</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_text_ampersand_is_escaped_with_a_space(self):
        message = SMSMessage.new_from_text('60-20100321-213321-00796995-00702693',
                                           'Flor&ISIS, Financiación.', '1164462659', '+54')
        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Flor ISIS, Financiacion.</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_text_accept_percent_sign(self):
        message = SMSMessage.new_from_text('60-20100321-213321-00796995-00702693',
                                           'Ariel, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. '
                                           'Responde con tu celular y horario y te bonificamos 7 cuotas.',
                                           '1164462659', '+54')
        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Ariel, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. ' \
                      'Responde con tu celular y horario y te bonificamos 7 cuotas.</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_template_accept_percent_sign(self):
        message = SMSMessage.new_from_template('60-20100321-213321-00796995-00702693',
                                               '{name}, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. '
                                               'Responde con tu celular y horario y te bonificamos 7 cuotas.',
                                               {'name': 'Ariel'}, '1164462659', '+54')

        message_xml = '<destinatario><idTran>60-20100321-213321-00796995-00702693</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                      '<mensaje>Ariel, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. ' \
                      'Responde con tu celular y horario y te bonificamos 7 cuotas.</mensaje></destinatario>'
        self.assert_serialization(message, message_xml)

    def test_message_list(self):
        message_list = SMSMessageList()
        message_list.add_message_from_text('1',
                                           'Hello World!!',
                                           '1164462659',
                                           '+54')
        message_list.add_message_from_template('2',
                                               'Hello {name}!!',
                                               {'name': 'Roman'},
                                               '11111111',
                                               '+54')

        message_list_xml = '<destinatarios><destinatario>' \
                           '<idTran>1</idTran><numeroTelefono pais="+54">1164462659</numeroTelefono>' \
                           '<mensaje>Hello World!!</mensaje></destinatario>' \
                           '<destinatario>' \
                           '<idTran>2</idTran><numeroTelefono pais="+54">11111111</numeroTelefono>' \
                           '<mensaje>Hello Roman!!</mensaje></destinatario>' \
                           '</destinatarios>'
        self.assert_serialization(message_list, message_list_xml)

    def test_sending_request(self):
        client = SMSCoverClient('helper', 'pass12')
        sms_system = SMSSystem()
        sms_settings = SMSSendingSettings('codigo-lote-cliente', datetime(2015, 1, 6), datetime(2015, 1, 8),
                                          time_range_list=TimeRangeList.new_with('1', time(8), time(22)))

        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1164462659', '+54')

        request = SMSCoverRequest(client, sms_settings, sms_system, message_list)
        request_xml = '<?xml version="1.0" encoding="UTF-8"?><xml-push-sms>' \
                      '<metadata>' \
                      '<server>stringway.smscover.com</server><version>Gateway_V3</version><accion>0</accion>' \
                      '</metadata>' \
                      '<parametros><cabecera><user>helper</user><key>pass12</key>' \
                      '<lote-request>codigo-lote-cliente</lote-request><formato>NoClass_7Bit</formato>' \
                      '<bocas></bocas><prioridad>6</prioridad><fecha-comienzo>2015/01/06</fecha-comienzo>' \
                      '<fecha-fin>2015/01/08</fecha-fin><continuacion>0</continuacion><rangos-horarios>' \
                      '<rango-horario id="1"><hora-desde am-pm="am">8:00</hora-desde>' \
                      '<hora-hasta am-pm="pm">10:00</hora-hasta></rango-horario></rangos-horarios>' \
                      '</cabecera><detalle><destinatarios><destinatario><idTran>1</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono><mensaje>Hello World!!</mensaje></destinatario>' \
                      '</destinatarios></detalle></parametros></xml-push-sms>'

        self.assert_serialization(request, request_xml)

    def test_sending_request_with_message_with_percent_sign(self):
        client = SMSCoverClient('helper', 'pass12%')
        sms_system = SMSSystem()
        sms_settings = SMSSendingSettings('codigo-lote-cliente', datetime(2015, 1, 6), datetime(2015, 1, 8),
                                          time_range_list=TimeRangeList.new_with('1', time(8), time(22)))

        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Ariel, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. '
                                           'Responde con tu celular y horario y te bonificamos 7 cuotas.', '1164462659', '+54')
        message_list.add_message_from_text('2', 'Juan, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. '
                                           'Responde con tu celular y horario y te bonificamos 7 cuotas.', '1164462659', '+54')

        request = SMSCoverRequest(client, sms_settings, sms_system, message_list)
        request_xml = '<?xml version="1.0" encoding="UTF-8"?><xml-push-sms>' \
                      '<metadata>' \
                      '<server>stringway.smscover.com</server><version>Gateway_V3</version><accion>0</accion>' \
                      '</metadata>' \
                      '<parametros><cabecera><user>helper</user><key>pass12%</key>' \
                      '<lote-request>codigo-lote-cliente</lote-request><formato>NoClass_7Bit</formato>' \
                      '<bocas></bocas><prioridad>6</prioridad><fecha-comienzo>2015/01/06</fecha-comienzo>' \
                      '<fecha-fin>2015/01/08</fecha-fin><continuacion>0</continuacion><rangos-horarios>' \
                      '<rango-horario id="1"><hora-desde am-pm="am">8:00</hora-desde>' \
                      '<hora-hasta am-pm="pm">10:00</hora-hasta></rango-horario></rangos-horarios>' \
                      '</cabecera><detalle><destinatarios><destinatario><idTran>1</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono><mensaje>Ariel, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. ' \
                      'Responde con tu celular y horario y te bonificamos 7 cuotas.</mensaje></destinatario>' \
                      '<destinatario><idTran>2</idTran>' \
                      '<numeroTelefono pais="+54">1164462659</numeroTelefono><mensaje>Juan, Financiacion hasta el 100 % de tu 0 KM  en tasa 0 % . Entrega en cuota 6. ' \
                      'Responde con tu celular y horario y te bonificamos 7 cuotas.</mensaje></destinatario>' \
                      '</destinatarios></detalle></parametros></xml-push-sms>'

        self.assert_serialization(request, request_xml)

    def test_sending_query_request(self):
        client = SMSCoverClient('helper', 'pass12')
        sms_system = SMSSystem()
        sms_settings = SMSSendingSettings()
        sms_system.config_as_sending_query()
        request = SMSCoverRequest(client, sms_settings, sms_system)

        request_xml = '<?xml version="1.0" encoding="UTF-8"?><xml-push-sms>' \
                      '<metadata>' \
                      '<server>stringway.smscover.com</server><version>Gateway_V3</version><accion>1</accion>' \
                      '</metadata>' \
                      '<parametros><cabecera><user>helper</user><key>pass12</key>' \
                      '<lote-request></lote-request><formato>NoClass_7Bit</formato>' \
                      '<bocas></bocas><prioridad>6</prioridad><continuacion>0</continuacion><rangos-horarios>' \
                      '</rangos-horarios></cabecera><detalle><destinatarios>' \
                      '</destinatarios></detalle></parametros></xml-push-sms>'

        self.assert_serialization(request, request_xml)
