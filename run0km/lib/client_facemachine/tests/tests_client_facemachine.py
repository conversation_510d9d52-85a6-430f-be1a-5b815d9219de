# coding=utf-8
from datetime import date, timedelta

import mock

from lib.api_client.errors import ClientConnectionError
from lib.api_client.testcase import APIClientTestCase
from lib.client_facemachine.core import FacemachineAPIClient
from django.conf import settings

from lib.client_facemachine.tests.mocks import FacemachineRequestMock, PaginatedAccountFriendsMock, \
    PaginatedSentFriendshipRequestsMock


class FacemachineAPISendFriendshipRequestMockedTest(APIClientTestCase):
    @mock.patch('requests.post', return_value=FacemachineRequestMock.friendship_request_success())
    def test_send_a_friendship_request_from_a_random_account(self, _):
        client = FacemachineAPIClient.facebook_frienship_request_client(settings.FACEMACHINE_API_TOKEN)
        fake_user = '<EMAIL>'
        brand_name = 'Volkswagen'
        response = client.send_friendship_request(user=fake_user, brand_name=brand_name)
        self.assertEqual(response, 2)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.friendship_request_success())
    def test_send_many_friendship_requests_with_the_same_account(self, _):
        client = FacemachineAPIClient.facebook_frienship_request_client(settings.FACEMACHINE_API_TOKEN)
        users = ['<EMAIL>' % d for d in range(3)]
        brand_name = 'Volkswagen'
        responses = client.send_multiple_requests_with_same_account(users=users, brand_name=brand_name)
        self.assertEqual([2] * 3, responses)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.friendship_request_too_exclusive())
    def test_send_friendship_request_with_too_exclusive_filters_raises_an_exception(self, _):
        client = FacemachineAPIClient.facebook_frienship_request_client(settings.FACEMACHINE_API_TOKEN)
        fake_user = '<EMAIL>'
        brand_name = 'Renault'
        group_name = 'FakeGroup'
        is_internal = True
        self.assertRaisesWithMessage(ClientConnectionError, "No hay cuentas con estos parámetros.",
                                     client.send_friendship_request, user=fake_user, brand_name=brand_name,
                                     group_name=group_name, is_internal=is_internal)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.invalid_token())
    def test_send_friendship_request_with_invalid_token_raises_an_exception(self, _):
        client = FacemachineAPIClient.facebook_frienship_request_client('FAKETOKEN')
        fake_user = '<EMAIL>'
        brand_name = 'Volkswagen'
        self.assertRaisesWithMessage(ClientConnectionError, "404 Client Error: Not Found",
                                     client.send_friendship_request, user=fake_user, brand_name=brand_name)


class FacemachineAPIAccountFriendsMockedTest(APIClientTestCase):
    @mock.patch('requests.post', side_effect=PaginatedAccountFriendsMock.post)
    def test_ask_for_all_account_friends_returns_at_least_one_friend(self, _):
        client = FacemachineAPIClient.facebook_account_friends_client(settings.FACEMACHINE_API_TOKEN)
        accounts = client.get_friends_of_user()
        self.assertGreater(len(accounts), 0)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.empty_account_friends_list())
    def test_ask_for_future_account_friends_returns_empty_list(self, _):
        client = FacemachineAPIClient.facebook_account_friends_client(settings.FACEMACHINE_API_TOKEN)
        tomorrow = date.today() + timedelta(days=1)
        accounts = client.get_friends_of_user(from_date=tomorrow)
        self.assertEqual(len(accounts), 0)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.invalid_token())
    def test_ask_for_account_friends_with_invalid_token_raises_an_exception(self, _):
        client = FacemachineAPIClient.facebook_account_friends_client('FAKETOKEN')
        self.assertRaisesWithMessage(ClientConnectionError, "404 Client Error: Not Found",
                                     client.get_friends_of_user)


class FacemachineAPISentFriendshipRequestsMockedTest(APIClientTestCase):
    @mock.patch('requests.post', side_effect=PaginatedSentFriendshipRequestsMock.post)
    def test_ask_for_all_sent_friendship_requests_returns_at_least_one_friendship_request(self, _):
        client = FacemachineAPIClient.facebook_sent_friendship_requests_client(settings.FACEMACHINE_API_TOKEN)
        friendship_requests = client.check_sent_requests()
        self.assertGreater(len(friendship_requests), 0)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.empty_friendship_requests_list())
    def test_ask_for_future_sent_friendship_requests_returns_empty_list(self, _):
        client = FacemachineAPIClient.facebook_sent_friendship_requests_client(settings.FACEMACHINE_API_TOKEN)
        tomorrow = date.today() + timedelta(days=1)
        accounts = client.check_sent_requests(from_date=tomorrow)
        self.assertEqual(len(accounts), 0)

    @mock.patch('requests.post', return_value=FacemachineRequestMock.invalid_token())
    def test_ask_for_sent_friendship_requests_with_invalid_token_raises_an_exception(self, _):
        client = FacemachineAPIClient.facebook_account_friends_client('FAKETOKEN')
        self.assertRaisesWithMessage(ClientConnectionError, "404 Client Error: Not Found",
                                     client.get_friends_of_user)
