import json

import requests
from rest_framework import status

from lib.api_client.api import ApiClient
from lib.api_client.errors import UnexpectedStatus, ClientValidationError, ClientErrorResponse


class PilotSender(ApiClient):
    _HEADERS = {'Content-type': 'application/json', 'Accept': 'application/json, text/javascript'}

    def name(self):
        return 'Pilot'

    def serialize(self, request):
        return request.as_dict()

    def headers(self):
        return self._HEADERS

    def _load_content(self, response):
        return json.loads(response.content)

    def _send_request(self, headers, request_data, **kwargs):
        response = super(PilotSender, self)._send_request(
            headers=headers, request_data=request_data, **kwargs)
        if response.status_code != status.HTTP_200_OK:
            raise UnexpectedStatus(message='The response status should be 200 but is %s' % response.status_code,
                                   request=request_data, response=response)
        response_to_dict = self._load_content(response)
        if not response_to_dict['success']:
            raise ClientErrorResponse(message=response_to_dict['message'], data=response_to_dict['data'],
                                      request=request_data, response=response)

        return response_to_dict

    def _post(self, url, headers, data, **kwargs):
        """
            Al parecer pilot espera los parametros via url y no via parametros POST
        """
        return requests.post(url=url, params=data, headers=headers, **kwargs)

    @classmethod
    def new_for(cls, url):
        return cls(url, cls.POST)


class PilotLead(object):
    required_fields = ['action', 'appkey', 'firstname', 'contact_type_id', 'business_type_id', 'phone', 'suborigin_id']

    def __init__(self, action, appkey, firstname, contact_type_id, business_type_id, phone, suborigin_id):
        self.business_type_id = business_type_id
        self.contact_type_id = contact_type_id
        self.suborigin_id = suborigin_id
        self.firstname = firstname
        self.appkey = appkey
        self.action = action
        self.phone = phone
        self.debug = True
        self.notification_email = None
        self.lastname = None
        self.vendor_phone = None
        self.cellphone = None
        self.email = None
        self.notes = None
        self.origin_id = None
        self.assigned_user = None
        self.car_brand = None
        self.car_modelo = None
        self.city = None
        self.province = None
        self.country = None
        self.vendor_name = None
        self.vendor_email = None
        self.provider_service = None
        self.provider_url = None
        self.assigned_user = None

    @classmethod
    def _validate_arguments(cls, **kwargs):
        for key, value in list(kwargs.items()):
            if value is None:
                raise ClientValidationError(key)

    @classmethod
    def new_creation_action(cls, appkey, firstname, contact_type_id, business_type_id, suborigin_id, phone):
        request = cls.new_with(action='create', appkey=appkey, firstname=firstname, contact_type_id=contact_type_id,
                               business_type_id=business_type_id, suborigin_id=suborigin_id, phone=phone)
        return request

    @classmethod
    def new_with(cls, action, appkey, firstname, contact_type_id, business_type_id, suborigin_id, phone):
        cls._validate_arguments(appkey=appkey, firstname=firstname, suborigin_id=suborigin_id,
                                contact_type_id=contact_type_id, business_type_id=business_type_id, phone=phone)

        return cls(action=action, appkey=appkey, firstname=firstname, contact_type_id=contact_type_id,
                   business_type_id=business_type_id, suborigin_id=suborigin_id, phone=phone)

    def as_debug(self, is_debug=True):
        self.debug = is_debug

    def as_dict(self):
        request = {
            'action': self.action,
            'appkey': self.appkey,
            'debug': self._debug_value(),
        }

        if self.notification_email:
            request.update({'notification_email': self.notification_email})

        for key, value in list(self.__dict__.items()):
            if (self._is_required(key) or value) and key not in request:
                request['pilot_%s' % key] = value

        return request

    def __hash__(self):
        return hash(self.as_dict())

    def __eq__(self, other):
        return self.as_dict() == other.as_dict()

    def _is_required(self, field_name):
        return field_name in self.required_fields

    def _debug_value(self):
        return 1 if self.debug else 0
