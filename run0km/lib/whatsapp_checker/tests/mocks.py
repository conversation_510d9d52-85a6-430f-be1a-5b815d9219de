# coding=utf-8
from raven.utils import json
from lib.whatsapp_checker.errors import WhatsAppCheckerConnectionError


class WhatsAppCheckerConnectionFailMock(object):
    def call(self, number):
        raise WhatsAppCheckerConnectionError('mock error')

    def get_from_url(self, url_to_image):
        raise WhatsAppCheckerConnectionError('mock error')


class HTTPResponseErrorMock(object):
    def __init__(self, error):
        self.error = error

    def generate(self):
        raise self.error

    @classmethod
    def connection_error(cls):
        return cls(WhatsAppCheckerConnectionError('mock error'))


class HTTPResponseMock(object):
    def __init__(self, content):
        self.content = content

    def generate(self):
        return self

    def generate_from(self, number):
        # it is polimorphic with JsonParametricResponseMock

        return self.generate()

    @classmethod
    def response(cls, error, number, has_whatsapp, profile_image_url=None, privacy=None):
        json_response = {'error': error, 'numberVerified': number, 'tieneWA': has_whatsapp}
        if profile_image_url is not None:
            json_response.update({'m_profile': profile_image_url})
        if privacy is not None:
            json_response.update({'privacidad': privacy})
        content = json.dumps(json_response)
        return cls(content)


class JsonParametricResponseMock(object):
    def __init__(self, answer, profile_image_url='http://example/example.jpg'):
        self.answer = answer
        self.profile_image_url = profile_image_url

    def generate_from(self, number):
        response = HTTPResponseMock.response(False, number, self.answer, profile_image_url=self.profile_image_url)
        return response.generate_from(number)


class WhatsAppCheckerMock(object):
    DEFAULT_IMAGE_CONTENT = ''

    def __init__(self, response, image_response=None):
        self.http_response = response
        self.image_response = image_response

    def call(self, number):
        return self.http_response.generate_from(number)

    def get_from_url(self, url_to_image):
        return self.image_response.generate()

    @classmethod
    def response_from(cls, string):
        response = HTTPResponseMock(string)
        return cls(response)

    @classmethod
    def response_empty_json(cls):
        return cls.response_from('{}')

    @classmethod
    def response_format_unexpected(cls):
        return cls.response_from('[]')

    @classmethod
    def response(cls, error, number, has_whatsapp, profile_image_url=None, privacy=None, image=DEFAULT_IMAGE_CONTENT):
        response = HTTPResponseMock.response(error, number, has_whatsapp, profile_image_url, privacy)
        image_response = HTTPResponseMock(image)
        return cls(response, image_response)

    @classmethod
    def response_true(cls, number, profile_image_url=None, privacy=None, image=DEFAULT_IMAGE_CONTENT):
        return cls.response(False, number, True, profile_image_url, privacy, image)

    @classmethod
    def response_always_has_whatsapp_and_image_error(cls):
        response = JsonParametricResponseMock(True)
        image_response = HTTPResponseErrorMock.connection_error()
        return cls(response, image_response)

    @classmethod
    def response_error(cls, number):
        return cls.response(True, number, False)

    @classmethod
    def response_always(cls, boolean, image=DEFAULT_IMAGE_CONTENT):
        response = JsonParametricResponseMock(boolean)
        image_response = HTTPResponseMock(image)
        return cls(response, image_response)
