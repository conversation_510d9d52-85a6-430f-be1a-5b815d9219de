# -*- coding: utf-8 -*-
import json

from dateutil import parser
from django.conf import settings
from django.utils.timezone import now, timedelta, localtime

from prospectos.models import Llamado

INTERVALO_DE_CALCULO_DE_ALERTAS = 120  # Minutos


def agregar_datos_de_alertas_al_contexto(request):
    controlador = ControladorDeAlertasDeLlamadosProgramados(request)
    corresponde_alertar = controlador.corresponde_alertar_por_llamados()
    context = {'corresponde_alertar_por_llamados': corresponde_alertar, }
    if corresponde_alertar:
        context['delta_alerta_llamados'] = settings.DELTA_ALERTA_LLAMADOS
        alertas = controlador.horarios_de_llamados_a_alertar()
        context['alertas_de_llamado'] = json.dumps(alertas)
    else:
        controlador.limpiar_datos_de_llamados_en_session()
    return context


class ControladorDeAlertasDeLlamadosProgramados(object):
    def __init__(self, request):
        self.request = request

    def horarios_de_llamados_a_alertar(self):
        if self._es_necesario_recalcular_alertas():
            self._recalcular_alertas()
        return self._obtener_horarios_de_llamados_a_alertar()

    def limpiar_datos_de_llamados_en_session(self):
        if 'llamados_a_alertar' in self.request.session:
            del self.request.session['llamados_a_alertar']
        if 'ultimo_calculo_de_alertas' in self.request.session:
            del self.request.session['ultimo_calculo_de_alertas']
        self.request.session.modified = True

    def _obtener_horarios_de_llamados_a_alertar(self):
        return list(self.request.session['llamados_a_alertar'].items())

    def _es_necesario_recalcular_alertas(self):
        if 'ultimo_calculo_de_alertas' not in self.request.session:
            return True
        else:
            ahora = now()
            ultima_alerta = parser.parse(self.request.session['ultimo_calculo_de_alertas'])
            return ahora - ultima_alerta > timedelta(minutes=INTERVALO_DE_CALCULO_DE_ALERTAS)

    def corresponde_alertar_por_llamados(self):
        if not self.request.user.is_authenticated() or self.request.user.personificado:
            return False
        if self.request.user.is_vendedor():
            return True
        return False

    def _recalcular_alertas(self):
        ahora = localtime(now())
        self.request.session['ultimo_calculo_de_alertas'] = ahora.isoformat()
        limite = ahora + timedelta(minutes=INTERVALO_DE_CALCULO_DE_ALERTAS)
        llamados_por_id = self._obtener_llamados_a_alertar(ahora, limite)
        self.request.session['llamados_a_alertar'] = llamados_por_id
        self.request.session.modified = True

    def _obtener_llamados_a_alertar(self, limite_inferior, limite_superior):
        vendedor = self.request.user.vendedor
        llamados_en_rango = Llamado.objects.llamados_para_prospectos_de(vendedor,
                                                                        fecha_desde=limite_inferior,
                                                                        fecha_hasta=limite_superior)
        llamados_por_id = {}
        for llamado in llamados_en_rango:
            fecha_local = localtime(llamado.fecha)
            llamados_por_id[str(llamado.prospecto_id)] = fecha_local.isoformat()
        return llamados_por_id

    def nuevo_llamado(self, llamado):
        self.eliminar_alerta_de_llamado(llamado.prospecto_id)
        if self.corresponde_alertar_por_llamados() and llamado.prospecto.vendedor_id == self.request.user.vendedor.id:
            ahora = localtime(now())
            limite = ahora + timedelta(minutes=INTERVALO_DE_CALCULO_DE_ALERTAS)
            if ahora <= localtime(llamado.fecha) <= limite:
                self.request.session['llamados_a_alertar'][str(llamado.prospecto_id)] = llamado.fecha.isoformat()
                self.request.session.modified = True

    def eliminar_alerta_de_llamado(self, prospecto_id):
        if 'llamados_a_alertar' in self.request.session:
            ids_llamados = list(self.request.session['llamados_a_alertar'].keys())
            if prospecto_id in ids_llamados:
                self.request.session['llamados_a_alertar'].pop(prospecto_id)
                self.request.session.modified = True
