from layers.application.commands.base.proto_command import ProtoAppCommand
from vendedores.gestor import GestorDeVendedores


class CambiarConfiguracionDeServiciosDeConcesionariaCommando(ProtoAppCommand):
    """
        Primera iteracion para reificar el cambio de configuracion de servicios para concesionaria.
        Por ahora solo fuerza la actualizacion luego que la configuracion de servicios fue modificada.
        Esto es porque django admin rompe en encapsulamiento y manipula directamente el model.

    """

    def __init__(self):
        super(CambiarConfiguracionDeServiciosDeConcesionariaCommando, self).__init__()
        self._configuracion_de_servicios = None
        self._campos_modificados = None

    def set_arguments(self, configuracion_de_servicios, campos_modificados):
        self._configuracion_de_servicios = configuracion_de_servicios
        self._campos_modificados = campos_modificados

    def execute(self):
        self._actualizar_cambios(self._configuracion_de_servicios, self._campos_modificados)

    # --- private methods ---

    def _actualizar_cambios(self, configuracion_de_servicios, campos_modificados):
        configuracion_de_servicios.actualizar_permisos_en_cascada(campos_modificados)


class CambiarConfiguracionDeServiciosDeVendedorCommando(ProtoAppCommand):
    """
        Primera iteracion para reificar el cambio de configuracion de servicios para vendedores.
        Idem a CambiarConfiguracionDeServiciosDeConcesionariaCommando.
        Por ahora solo fuerza la actualizacion luego que la configuracion de servicios fue modificada.
        Esto es porque django admin rompe en encapsulamiento y manipula directamente el model.

    """
    def __init__(self):
        super(CambiarConfiguracionDeServiciosDeVendedorCommando, self).__init__()
        self._vendedor = None
        self._campos_modificados = None

    def set_arguments(self, vendedor, campos_modificados):
        self._vendedor = vendedor
        self._campos_modificados = campos_modificados

    def execute(self):
        self._actualizar_cambios(self._vendedor, self._campos_modificados)

    # --- private methods ---

    def _actualizar_cambios(self, vendedor, campos_modificados):
        gestor = GestorDeVendedores.nuevo()
        gestor.actualizar_permisos_en_cascada(vendedor=vendedor, campos_modificados=campos_modificados)
