from layers.application import AppCommand
from layers.application.commands.ingreso_de_prospectos.puesto_de_ingreso_de_prospectos import \
    PuestoDeIngresoDeProspectos
from layers.application.commands.validators.generic import NotNone
from layers.application.commands.validators.validator import Parameter
from prospectos.models.exceptions import ProspectoRechazadoException, LoteDeProspectosRechazadoException
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.models.origen import OrigenDeProspectoJotform


class IngresoDeProspectoDesdeJotformComando(AppCommand):
    """
        Comando que reifica el ingreso de un prospecto desde jotform.
    """

    def __init__(self):
        super(IngresoDeProspectoDesdeJotformComando, self).__init__()
        self._puesto_de_ingreso = PuestoDeIngresoDeProspectos.nuevo()
        self._repartidor = RepartidorDeProspectos()
        self._origen = OrigenDeProspectoJotform()

    def _initial_parameters(self):
        return [
            Parameter("datos_del_prospecto", [NotNone()]),
            Parameter("campos_extra", []),
            Parameter("debe_validar_email", []),
        ]

    def required_parameter_names(self):
        return ["datos_del_prospecto"]

    def can_execute(self):
        result = super(IngresoDeProspectoDesdeJotformComando, self).can_execute()
        if not self._telefono() and not self._email():
            result.add_error('Datos de contactos invalidos.')
        return result

    def _execute_from_successful_result(self, result):
        try:
            self._puesto_de_ingreso.ingresar(
                [self._datos_del_prospecto()],
                metodo_constructor=self._crear_prospecto, argumentos=[result])
        except (ProspectoRechazadoException, LoteDeProspectosRechazadoException) as exc:
            result.add_error(str(exc))
        return result

    def _crear_prospecto(self, result):
        try:
            resultado = self._repartidor.crear_nuevo_prospecto_desde(
                datos=self._datos_del_prospecto(),
                campos_extra=self._campos_extra(),
                validar_email=self._debe_validar_email(),
                origen_de_prospecto=self._origen)

            # Este comando deberia tener esta responsabilidad
            # tasks.completar_informacion_de_geolocalizacion_desde_ip_de_prospecto.delay(prospecto_id=nuevo_prospecto.id)
            # if nuevo_prospecto.esta_asignado():
                # AdministradorDePedidos().contabilizar_carga_de_prospecto_con_responsable(prospecto=nuevo_prospecto)

            result.set_object(resultado)

        except ProspectoRechazadoException as exception:
            result.add_error(str(exception))
        return result

    # Parametros
    def set_datos_del_prospecto(self, datos_del_prospecto):
        self.set_argument_value('datos_del_prospecto', datos_del_prospecto)

    def set_campos_extra(self, campos_extra):
        self.set_argument_value('campos_extra', campos_extra)

    def set_debe_validar_email(self, debe_validar_email):
        self.set_argument_value('debe_validar_email', debe_validar_email)

    def set_origen(self, origen):
        self.set_argument_value('origen', origen)

    def _datos_del_prospecto(self):
        return self.get_argument_named('datos_del_prospecto')

    def _campos_extra(self):
        return self.get_argument_named('campos_extra', [])

    def _debe_validar_email(self):
        return self.get_argument_named('debe_validar_email') or False

    def _origen(self):
        return self.get_argument_named('origen')

    # Metodos de conveniencia
    def _telefono(self):
        return self._datos_del_prospecto().get('telefono')

    def _email(self):
        return self._datos_del_prospecto().get('email')

    def _marca_de_tarjeta(self):
        return self._datos_del_prospecto().get('marca_de_tarjeta', '')

