import pusher
from pusher.errors import <PERSON>ush<PERSON><PERSON>rror

from mobileapi.errors import ComunicationChannelException


class CommunicationChannel(object):
    @classmethod
    def new_with(cls, app_id, key, secret):
        return cls(app_id, key, secret)

    def __init__(self, app_id, key, secret):
        self._sender = pusher.Pusher(
            app_id=app_id,
            key=key,
            secret=secret,
            ssl=True
        )

    def authenticate(self, channel_name, socket_id):
        authentication = self._sender.authenticate(channel_name=channel_name, socket_id=socket_id)
        return authentication

    def send_message(self, channels, event_name, message):
        """
        Pusher::trigger will throw a TypeError if called with parameters of the wrong type; or a ValueError if called
        on more than 100 channels, with an event name longer than 200 characters, or with more than 10240 characters of
        data (post JSON serialisation).
        """
        try:
            self._sender.trigger(channels=channels, event_name=event_name, data=message)
        except (ValueError, TypeError, PusherError) as error:
            raise ComunicationChannelException(str(error))

    def send_message_batch(self, channels, event_name, data):
        if not channels:
            return

        batch_data = []
        for channel in channels:
            event_data_per_channel = {"channel": channel, "name": event_name, "data": data}
            batch_data.append(event_data_per_channel)

        try:
            self._sender.trigger_batch(batch_data)
        except (TypeError, PusherError) as error:
            raise ComunicationChannelException(str(error))

    def obtener_medios_activos(self, prefijo):
        # Filtra los canales que contengan 'prefijo' en su nombre e informa la cantidad de usuarios conectados
        return self._sender.channels_info(prefijo, ['user_count'])

    def obtener_usuarios_de(self, canal):
        return self._sender.users_info('presence-'+canal)



# cdc = ComunicationChannel.nuevo()
# mensaje = {'message': 'Houston tenemos un pushlema'}
# cdc.enviar_mensaje_a(canales='my-channel', eventos='my-event', mensaje=mensaje)
# medios_activos = cdc.obtener_medios_activos()
# pusher_client.trigger('my-channel', 'my-event', {'message': 'hello world'})
# pusher.channels_info(u"presence-", [u'user_count'])