# coding=utf-8
import mock
from django.test import override_settings
from django.utils.timezone import now

from conversaciones.models import Conversacion
from mobileapi.comandos import <PERSON>mando, AgregaMensajeDeWhatsappAConversacionComando, MarcaConversacionComoLeidaComando
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoConversacionSincronizable


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class ComandosTest(SincronizadorCoreTest):
    def setUp(self):
        super(ComandosTest, self).setUp()
        self.vendedor_uno = self.fixture['vend_1']
        self.prospecto_de_vendedor_uno = self.fixture['p_1']
        self.prospecto2 = self.fixture['p_2']
        self.tipo_sincronizable = TipoConversacionSincronizable()
        self.conversacion = Conversacion.nuevo(tipo=Conversacion.TIPO_WHATSAPP,
                                               prospecto=self.prospecto_de_vendedor_uno,
                                               fecha_ultima_respuesta=None, fecha_ultimo_mensaje=now(), fue_leida=True,
                                               eliminada=False)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_mensaje_de_whatsapp_a_conversacion_debe_agregar_un_mensaje_nuevo_del_vendedor(
            self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        conversacion = Conversacion.nuevo(tipo=Conversacion.TIPO_WHATSAPP, prospecto=self.prospecto2,
                                          fecha_ultima_respuesta=None, fecha_ultimo_mensaje=now(), fue_leida=True,
                                          eliminada=False)

        fecha_string = "2017-03-20-16:49:30"
        texto = 'a whatsapp message'
        argumentos = self.creador_de_comandos.argumentos_nuevo_mensaje_de_whatsapp(fecha=fecha_string, texto=texto)
        comando = Comando.nuevo_para(nombre=AgregaMensajeDeWhatsappAConversacionComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregaMensajeDeWhatsappAConversacionComando)
        comando.ejecutar(modelo=conversacion, vendedor=self.vendedor_uno)
        self._assert_conversacion_tiene_un_mensaje_de_whatsapp(conversacion, texto, fecha_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_marcar_conversacion_como_leida_debe_marcar_la_conversacion_como_leida(self,
                                                                                                   pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        comando = Comando.nuevo_para(nombre=MarcaConversacionComoLeidaComando.nombre(), argumentos={},
                                     tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, MarcaConversacionComoLeidaComando)
        comando.ejecutar(modelo=self.conversacion, vendedor=self.vendedor_uno)
        self._assert_conversacion_fue_leida(self.conversacion)
        self.assertFalse(pusher_trigger_mock.called)
