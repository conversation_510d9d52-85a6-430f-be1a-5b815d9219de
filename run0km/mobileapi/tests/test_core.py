import datetime
import json

import mock
from django.conf import settings
from django.urls import reverse
from django.test import override_settings
from django.utils import timezone
from django.utils.timezone import localtime
from freezegun import freeze_time
from rest_framework import status

from core.support import make_aware_when_is_naive
from mobileapi.comandos import <PERSON>mand<PERSON>, CargarVentaComando
from mobileapi.errors import VersionDeAPIInvalida, ParametroRequerido
from mobileapi.errors import WhatsappDeshabilitado, ProspectoInexistente, \
    ConversacionInexistente, ProspectoNoEsDelVendedor
from core.locker.errors import ResourceLockedError
from mobileapi.models import Sincronizacion, SesionAppMobile
from mobileapi.notificador import NotificadorDeSincronizaciones
from mobileapi.sincronizador import Sincronizador
from mobileapi.tests.creador_de_comandos import CreadorDeComandosMobile
from mobileapi.tests.sincronizador.test_context import ContextoMobile
from propuestas.tests.validador import ValidadorDePropuesta
from prospectos.models import Finalizacion, <PERSON><PERSON>
from notificaciones import FormaDeEnvioWhatsapp
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model
from vendedores.gestor import GestorDeVendedores
from vendedores.models import LogActividad


@override_settings(SINCRONIZACIONES=['mobileapi.sincronizador.Sincronizador'])
class SincronizadorCoreTest(BaseFixturedTest):
    def setUp(self):
        super(SincronizadorCoreTest, self).setUp()
        self.sincronizador = Sincronizador()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()
        self.creador_de_comandos = CreadorDeComandosMobile()
        self.contexto_mobile = ContextoMobile()
        self.version_api = '1.1-delivery-run-android-alpha'

    def _assert_envio_de_mensaje(self, mensaje_esperado, pusher_trigger_mock, vendedor, verificar_call_once=True):
        self.assertTrue(not verificar_call_once or pusher_trigger_mock.call_count == 1)
        pusher_trigger_mock.assert_any_call(
            event_name=mensaje_esperado['operation'],
            channels=NotificadorDeSincronizaciones.CHANNEL_NAME_TEMPLATE % vendedor.pk,
            data=mensaje_esperado['arguments'])

    def _assert_respuesta_no_autorizado(self, response):
        self.assert_response_status_code(response, status.HTTP_403_FORBIDDEN)

    def _assert_sincronizacion_lockeada(self, response):
        self.assert_response_status_code(response, status.HTTP_429_TOO_MANY_REQUESTS)

    def _assert_respuesta_version_invalida(self, response):
        self.assert_response_status_code(response, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data, {'message': str(VersionDeAPIInvalida.nueva()),
                                         'version_api': Sincronizador().version_api_actual()})

    def _assert_respuesta_whatsapp_deshabilitado(self, response):
        self.assert_response_status_code(response, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data, {'message': str(WhatsappDeshabilitado.nueva())})

    def _assert_respuesta_prospecto_inexistente(self, response):
        self.assert_response_status_code(response, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data, {'message': str(ProspectoInexistente.nueva())})

    def _assert_respuesta_prospecto_no_es_del_vendedor(self, response):
        self.assert_response_status_code(response, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data, {'message': str(ProspectoNoEsDelVendedor.nueva())})

    def _assert_respuesta_conversacion_inexistente(self, response):
        self.assert_response_status_code(response, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data, {'message': str(ConversacionInexistente.nueva())})

    def _assert_respuesta_fcm_token_requerido(self, response):
        self.assert_response_status_code(response, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data, {'message': str(ParametroRequerido.nueva(parametro='fcm_token'))})

    def _assert_respuesta_pedido_invalido(self, response):
        self.assert_response_status_code(response, status.HTTP_400_BAD_REQUEST)

    def _assert_sesion_con(self, sesion, tipo_sincronizable, modelos_asignados, ids_modelos_removidos,
                           modelos_modificados):
        sesion.refresh_from_db()
        self.assertEqual(set(sesion.modelos_asignados(tipo_sincronizable)), set(modelos_asignados))
        self.assertEqual(set(sesion.ids_modelos_removidos(tipo_sincronizable)), set(ids_modelos_removidos))
        self.assertEqual(set(sesion.modelos_modificados(tipo_sincronizable)), set(modelos_modificados))

    def _assert_envio_de_comando_prospecto_modificado(self, pusher_trigger_mock, vendedor, verificar_call_once=True):
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor, verificar_call_once)

    def _deny_envio_de_comando_prospecto_modificado(self, pusher_trigger_mock, vendedor, verificar_call_once=True):
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        try:
            self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor, verificar_call_once)
        except self.failureException:
            pass
        else:
            self.fail('No debe enviarse notificacion')

    def _assert_resultado_de_sincronizacion_de_modelos(self, resultado, numero_de_sinc, tipo_sincronizable,
                                                       modelos_asignados=None, modelos_modificados_exitosos=None,
                                                       modelos_modificados_fallidos=None, modelos_removidos=None):
        resultado_esperado = self._resultado_esperado_para(numero_de_sinc, tipo_sincronizable,
                                                           modelos_asignados=modelos_asignados,
                                                           modelos_removidos=modelos_removidos,
                                                           modelos_modificados_exitosos=modelos_modificados_exitosos,
                                                           modelos_modificados_fallidos=modelos_modificados_fallidos)
        self.assertEqual(resultado_esperado['sync_sequence_number'], resultado['sync_sequence_number'])
        self._assert_listas_identicas(resultado=resultado['result']['assigned'],
                                      resultado_esperado=resultado_esperado['result']['assigned'])
        self._assert_listas_identicas(resultado=resultado['result']['modified'],
                                      resultado_esperado=resultado_esperado['result']['modified'])
        self.assertEqual(set(resultado_esperado['result']['removed']), set(resultado['result']['removed']))

    def _assert_listas_identicas(self, resultado, resultado_esperado):
        """
            Python no me deja hacer set de diccionarios, por eso esta comparacion "casera"

        """
        self.assertEqual(len(resultado), len(resultado_esperado))
        for elemento_esperado in resultado_esperado:
            self.assertIn(elemento_esperado, resultado)

    def _get_to_api(self, user, url, datos):
        token_key = self._token_key_de(user)
        response = self.client.get(path=url,
                                   data=datos,
                                   HTTP_AUTHORIZATION='Token %s' % token_key,
                                   content_type='application/json')
        return response

    def _post_to_api(self, user, url, datos):
        token_key = self._token_key_de(user)
        response = self.client.post(path=url,
                                    data=json.dumps(datos),
                                    HTTP_AUTHORIZATION='Token %s' % token_key,
                                    content_type='application/json')
        return response

    def _token_key_de(self, user):
        return self.contexto_mobile.token_key_de(user=user)

    def habilitar_app_del_vendedor(self, vendedor):
        self.contexto_mobile.habilitar_app_del_vendedor(vendedor=vendedor)

    def habilitar_whatsapp_del_vendedor(self, vendedor):
        self.contexto_mobile.habilitar_whatsapp_del_vendedor(vendedor=vendedor)

    def _iniciar_sesion_para(self, vendedor, version_api=None, fcm_token='1234'):
        return self.contexto_mobile.iniciar_sesion_para(vendedor=vendedor, version_api=version_api, fcm_token=fcm_token)

    def _invalidar_sesion_para(self, sesion, version_api):
        assert (not SesionAppMobile.es_version_valida(version_api))
        sesion.cambiar_version(version_api)

    def _deshabilitar_vendedor(self, vendedor):
        self.contexto_mobile.deshabilitar_app_del_vendedor(vendedor=vendedor)

    def _comando_pedido_de_sincronizacion_de_prospectos(self):
        return NotificadorDeSincronizaciones()._notificacion_para_pedido_de_sincronizacion_de_tipo(
            NotificadorDeSincronizaciones.TIPO_PROSPECTOS)

    def _comando_pedido_de_sincronizacion_de_conversaciones(self):
        return NotificadorDeSincronizaciones()._notificacion_para_pedido_de_sincronizacion_de_tipo(
            NotificadorDeSincronizaciones.TIPO_CONVERSACIONES)

    def _formatear_fecha(self, fecha):
        return self.creador_de_comandos.formatear_fecha(fecha, settings.API_MOBILE_FORMATO_FECHA_HORA)

    def _fecha_desde_string(self, fecha_string):
        fecha = datetime.datetime.strptime(fecha_string, settings.API_MOBILE_FORMATO_FECHA_HORA)
        return fecha

    def _resultado_esperado_para(self, numero_de_sinc, tipo_sincronizable, modelos_asignados=None,
                                 modelos_removidos=None, modelos_modificados_exitosos=None,
                                 modelos_modificados_fallidos=None):
        adapter = tipo_sincronizable.adapter()
        modelos_asignados = modelos_asignados or []
        modelos_removidos = modelos_removidos or []
        modelos_modificados_exitosos = modelos_modificados_exitosos or []
        modelos_modificados_fallidos = modelos_modificados_fallidos or {}
        self._refresh_all(modelos_asignados)
        self._refresh_all(modelos_modificados_exitosos)

        exitosos = [self._modificacion_existosa_para(tipo_sincronizable, modelo, adapter) for modelo in
                    modelos_modificados_exitosos]
        fallidos = [self._modificacion_fallida_para(tipo_sincronizable, id_modelo, mensaje)
                    for id_modelo, mensaje in list(modelos_modificados_fallidos.items())]

        resultado = {
            'sync_sequence_number': numero_de_sinc,
            'result': {
                'assigned': [adapter.adapt_this(modelo) for modelo in modelos_asignados],
                'removed': [modelo.id for modelo in modelos_removidos],
                'modified': exitosos + fallidos
            }
        }
        return resultado

    def _modificacion_existosa_para(self, tipo_sincronizable, modelo, adapter):
        return {
            "status": "successful",
            tipo_sincronizable.clave_de_serializacion(): adapter.adapt_this(modelo)
        }

    def _modificacion_fallida_para(self, tipo_sincronizable, id_modelo, mensaje):
        return {"status": 'failed', "message": mensaje, 'id': id_modelo}

    def _refresh_all(self, modelos):
        for modelo in modelos:
            modelo.refresh_from_db()

    def _assert_prospecto_tiene_nueva_llamada_programada(self, prospecto, fecha_string):
        self.assertTrue(prospecto.tiene_llamado())
        llamado = prospecto.llamado
        self.assertEqual(localtime(llamado.fecha).strftime(Comando.FORMATO_FECHA), fecha_string)

    def _assert_se_envio_una_propuesta_correctamente(self, vendedor, propuesta, prospecto, cantidad_actual):
        validador = ValidadorDePropuesta(self)
        validador.assert_registro_de_envios(cantidad_actual + 1, FormaDeEnvioWhatsapp, vendedor)
        validador.assert_conversacion_whatsapp(prospecto, propuesta)

    def _assert_prospecto_tiene_nueva_finalizacion(self, prospecto, vendedor, descripcion_de_motivo, otro_motivo,
                                                   comentario, es_otro_motivo=False):
        finalizacion = Finalizacion.de(prospecto=prospecto)
        self.assertIsNotNone(finalizacion)
        self.assertEqual(finalizacion.vendedor, vendedor)
        self.assertEqual(finalizacion.otro_motivo, otro_motivo)
        self.assertEqual(finalizacion.comentario, comentario)
        motivo_de_finalizacion = finalizacion.obtener_motivo()
        if es_otro_motivo:
            self.assertIsNone(motivo_de_finalizacion)
        else:
            self.assertIsNotNone(motivo_de_finalizacion)
            self.assertEqual(motivo_de_finalizacion.descripcion, descripcion_de_motivo)

    def _assert_prospecto_tiene_venta_con_datos(self, prospecto, marca, modelo, numero_de_contrato,
                                                precio, fecha_string):
        venta = prospecto.obtener_venta_activa()
        self.assertIsNotNone(venta)
        self.assertEqual(venta.precio, precio)
        self.assertEqual(venta.marca, marca)
        self.assertEqual(venta.modelo, modelo)
        self.assertEqual(venta.numero_de_contrato, numero_de_contrato)
        self.assertEqual(venta.fecha_de_realizacion.strftime(CargarVentaComando.FORMATO_FECHA), fecha_string)

    def _assert_prospecto_tiene_comentario(self, prospecto, vendedor, texto, es_automatico, fecha_string):
        self.assertEqual(prospecto.comentarios.count(), 1)
        comentario = prospecto.comentarios.first()
        self.assertEqual(localtime(comentario.datetime).strftime(Comando.FORMATO_FECHA), fecha_string)
        self.assertEqual(comentario.automatico, es_automatico)
        self.assertEqual(comentario.texto(), texto)
        self.assertEqual(comentario.obtener_vendedor(), vendedor)

    def _assert_conversacion_tiene_un_mensaje_de_whatsapp(self, conversacion, texto, fecha_string):
        from conversaciones.models import Conversacion
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp

        self.assertEqual(1, conversacion.cantidad_de_mensajes())
        self.assertTrue(conversacion.medio().es_de_tipo(Conversacion.TIPO_WHATSAPP))
        mensaje = conversacion.mensajes()[0]
        self.assertEqual(fecha_string, localtime(mensaje.obtener_fecha()).strftime(Comando.FORMATO_FECHA))
        self.assertEqual(texto, mensaje.texto())
        self.assertEqual(MensajesWhatsapp.VENDEDOR, mensaje.obtener_emisor())

    def _assert_conversacion_fue_leida(self, conversacion):
        self.assertTrue(conversacion.fue_leida)

    def _assert_llamada_realizada_tiene_formulario_de_interes(self, prospecto, pregunta, respuesta):
        self.assertEqual(prospecto.llamadas_realizadas.count(), 1)
        llamada_realizada = prospecto.llamadas_realizadas.first()
        self.assertEqual(llamada_realizada.respuestas_de_cuestionario.count(), 1)
        respuesta_a_pregunta = llamada_realizada.respuestas_de_cuestionario.first()
        self.assertEqual(respuesta_a_pregunta.pregunta, pregunta)
        self.assertEqual(respuesta_a_pregunta.contenido, respuesta)

    def _assert_prospecto_tiene_llamada_realizada(self, prospecto, duracion, fecha_inicio_string):
        self.assertEqual(prospecto.llamadas_realizadas.count(), 1)
        llamada = prospecto.llamadas_realizadas.first()
        self.assertEqual(localtime(llamada.fecha_comienzo).strftime(Comando.FORMATO_FECHA), fecha_inicio_string)
        self.assertEqual(llamada.duracion, duracion)

    def _assert_cantidad_de_sincronizaciones_exitosas(self, sesion, numero_de_version, cantidad_esperada):
        sincronizaciones = Sincronizacion.objects.exitosas_de(sesion, numero_de_version)
        self.assertEqual(
            sincronizaciones.count(), cantidad_esperada,
            'Las sincronizaciones exitosas son %d y se esperaba que sean %d' % (sincronizaciones.count(),
                                                                                cantidad_esperada))

    def _assert_cantidad_de_sincronizaciones_rechazadas(self, sesion, numero_de_version, cantidad_esperada):
        sincronizaciones = Sincronizacion.objects.rechazadas_de(sesion, numero_de_version)
        self.assertEqual(
            sincronizaciones.count(), cantidad_esperada,
            'Las sincronizaciones rechazadas son %d y se esperaba que sean %d' % (sincronizaciones.count(),
                                                                                  cantidad_esperada))

    def _assert_cantidad_de_sincronizaciones_pendientes(self, sesion, numero_de_version, cantidad_esperada):
        sincronizaciones = Sincronizacion.objects.pendientes_de(sesion, numero_de_version)
        self.assertEqual(
            sincronizaciones.count(), cantidad_esperada,
            'Las sincronizaciones pendientes son %d y se esperaba que sean %d' % (sincronizaciones.count(),
                                                                                  cantidad_esperada))

    def _assert_sesion(self, sesion, tipo_sincronizable, sync_sequence_number,
                       sync_sequence_number_actual_esperado, cantidad_exitosas, cantidad_rechazadas,
                       cantidad_pendientes):
        sesion.refresh_from_db()
        self.assertEqual(tipo_sincronizable.numero_de_secuencia_actual_para(sesion),
                         sync_sequence_number_actual_esperado)
        self._assert_cantidad_de_sincronizaciones_exitosas(sesion, sync_sequence_number, cantidad_exitosas)
        self._assert_cantidad_de_sincronizaciones_rechazadas(sesion, sync_sequence_number, cantidad_rechazadas)
        self._assert_cantidad_de_sincronizaciones_pendientes(sesion, sync_sequence_number, cantidad_pendientes)

    def _assert_sesion_sin_pendientes(self, sesion, tipo_sincronizable, sync_sequence_number, cantidad_exitosas,
                                      cantidad_rechazadas):
        self._assert_sesion(sesion,
                            tipo_sincronizable,
                            sync_sequence_number=sync_sequence_number,
                            sync_sequence_number_actual_esperado=sync_sequence_number + 1,
                            cantidad_exitosas=cantidad_exitosas,
                            cantidad_rechazadas=cantidad_rechazadas,
                            cantidad_pendientes=0)

    def _assert_sincronizacion_rechazada_para_prospectos(self, sesion, numero_de_secuencia, id_prospecto):
        self._assert_sincronizacion_de_prospectos_con_estado_para(
            sesion, numero_de_secuencia, id_prospecto, estado_esperado=Sincronizacion.RECHAZADA)

    def _assert_sincronizacion_rechazada_para_conversaciones(self, sesion, numero_de_secuencia, id_conversacion):
        self._assert_sincronizacion_de_conversaciones_con_estado_para(
            sesion, numero_de_secuencia, id_conversacion, estado_esperado=Sincronizacion.RECHAZADA)

    def _assert_sincronizacion_exitosa_para_prospecto(self, sesion, numero_de_secuencia, prospecto):
        self._assert_sincronizacion_de_prospectos_con_estado_para(
            sesion, numero_de_secuencia, prospecto.id, estado_esperado=Sincronizacion.EXITOSA)

    def _assert_sincronizacion_exitosa_para_conversacion(self, sesion, numero_de_secuencia, conversacion):
        self._assert_sincronizacion_de_conversaciones_con_estado_para(
            sesion, numero_de_secuencia, conversacion.id, estado_esperado=Sincronizacion.EXITOSA)

    def _assert_sincronizacion_de_prospectos_con_estado_para(self, sesion, numero_de_secuencia, id_prospecto,
                                                             estado_esperado):
        self._assert_sincronizacion_de_modelos_con_estado_para(sesion, numero_de_secuencia, id_prospecto,
                                                               estado_esperado, Sincronizacion.TIPO_PROSPECTOS)

    def _assert_sincronizacion_de_conversaciones_con_estado_para(self, sesion, numero_de_secuencia, id_prospecto,
                                                                 estado_esperado):
        self._assert_sincronizacion_de_modelos_con_estado_para(sesion, numero_de_secuencia, id_prospecto,
                                                               estado_esperado, Sincronizacion.TIPO_CONVERSACIONES)

    def _assert_sincronizacion_de_modelos_con_estado_para(self, sesion, numero_de_secuencia, id_modelo, estado_esperado,
                                                          tipo):
        sesion.refresh_from_db()
        sincronizacion = Sincronizacion.objects.de_modelo_numero_y_sesion(sesion=sesion, id_model=id_modelo,
                                                                          tipo=tipo,
                                                                          numero=numero_de_secuencia)
        self.assertEqual(sincronizacion.estado(), estado_esperado)

    def _assert_sincronizacion_fallida_para_prospecto(
            self, sesion, numero_de_secuencia, id_prospecto, mensaje_de_error):
        sesion.refresh_from_db()
        sincronizacion = Sincronizacion.objects.de_modelo_numero_y_sesion(sesion=sesion, id_model=id_prospecto,
                                                                          tipo=Sincronizacion.TIPO_PROSPECTOS,
                                                                          numero=numero_de_secuencia)
        self.assertEqual(sincronizacion.estado(), Sincronizacion.RECHAZADA)
        self.assertEqual(sincronizacion.mensaje(), mensaje_de_error)

    def _assert_prospecto_reactivado(self, prospecto):
        prospecto = reload_model(prospecto)
        self.assertTrue(prospecto.en_proceso)

    def _assert_prospecto_finalizado(self, prospecto):
        prospecto = reload_model(prospecto)
        self.assertTrue(prospecto.finalizado)

    def _assert_prospecto_no_tiene_conversacion_de_tipo(self, prospecto, tipo):
        prospecto = reload_model(prospecto)
        self.assertEqual(0, prospecto.conversacion.filter(tipo=tipo).count())

    def _assert_prospecto_tiene_conversacion_de_cierto_tipo_con_mensajes(self, prospecto, tipo, cantidad_de_mensajes):
        prospecto = reload_model(prospecto)
        conversaciones_de_tipo = prospecto.conversacion.filter(tipo=tipo)
        self.assertEqual(1, conversaciones_de_tipo.count())
        self.assertEqual(cantidad_de_mensajes, len(conversaciones_de_tipo.first().mensajes()))

    def _assert_no_se_registro_actividad_para(self, vendedor):
        self.assertFalse(LogActividad.objects.filter(vendedor=vendedor).exists())

    def _assert_se_registro_actividad_para(self, vendedor, mock_now):
        log = LogActividad.objects.ultimo_log_del_vendedor(vendedor)
        self.assertEqual(log.ultima, make_aware_when_is_naive(mock_now.return_value))

    def _agregar_llamada_programada(self, prospecto, delta_minutos=0):
        fecha = make_aware_when_is_naive(timezone.now()) + timezone.timedelta(minutes=delta_minutos)
        gestor = GestorDeProspecto.nuevo_para(rol=prospecto.obtener_vendedor())
        gestor.programar_nuevo_llamado_para(prospecto=prospecto, fecha=fecha, debe_sincronizar=False)

    def _agregar_llamada_realizada_a(self, prospecto, vendedor, duracion, fecha_inicio_string):
        fecha_inicio = self._fecha_desde_string(fecha_inicio_string)
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.agregar_llamada_realizada(prospecto, fecha_inicio=fecha_inicio, duracion=duracion)

    def _pasar_prospecto_a_en_proceso(self, prospecto, vendedor):
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.comentar_prospecto(prospecto=prospecto, fecha=timezone.now(), texto='Hola', es_automatico=False)

    def _finalizar_seguimiento(self, prospecto, vendedor, notificar=True):
        self._realizar_notificando(
            notificar=notificar,
            funcion=lambda: GestorDeProspecto.nuevo_para(vendedor).finalizar_prospecto(
                prospecto, debe_sincronizar=False)
        )

    def _reactivar_seguimiento(self, prospecto, vendedor, notificar=True):
        self._realizar_notificando(
            notificar=notificar,
            funcion=lambda: GestorDeProspecto.nuevo_para(vendedor).reactivar_seguimiento(
                prospecto, debe_sincronizar=False)
        )

    def _realizar_notificando(self, funcion, notificar=True):
        if notificar:
            return funcion()
        else:
            with mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None):
                return funcion()

    def _post_sincronizar_prospectos(self, datos, vendedor):
        url = reverse('sincronizacion-de-prospectos')
        user = vendedor.user
        return self._post_to_api(user, url, datos)

    def _post_sincronizar_conversaciones(self, datos, vendedor):
        url = reverse('sincronizacion-de-conversaciones')
        user = vendedor.user
        return self._post_to_api(user, url, datos)

    def _post_aceptar_compulsa(self, compulsa, vendedor):
        url = reverse('aceptar-compulsa-mobile', kwargs={'compulsa_pk': compulsa.pk})
        user = vendedor.user
        return self._post_to_api(user, url, {})

    def _post_rechazar_compulsa(self, compulsa, vendedor):
        url = reverse('rechazar-compulsa-mobile', kwargs={'compulsa_pk': compulsa.pk})
        user = vendedor.user
        return self._post_to_api(user, url, {})

    def _post_enviar_mensaje_chat(self, datos, vendedor):
        url = reverse('enviar-mensaje-chat-mobile')
        user = vendedor.user
        return self._post_to_api(user, url, datos)

    def _get_cantidad_de_mensajes_no_leidos(self, vendedor):
        url = reverse('cantidad-de-mensajes-no-leidos-mobile')
        user = vendedor.user
        return self._get_to_api(user, url, {})

    def _get_propuestas(self, vendedor):
        url = reverse('propuestas-mobile')
        user = vendedor.user
        return self._get_to_api(user, url, {})

    def _pedido_vacio(self, numero_de_version):
        return {'sync_sequence_number': numero_de_version, 'changes': []}

    def _fuera_de_horario_laboral(self):
        return make_aware_when_is_naive(timezone.datetime(
            day=10, month=8, year=2017, hour=23, minute=35))

    def assert_response_status_code(self, response, http_code):
        self.assertEqual(response.status_code, http_code,
                         msg="%s != %s: %s" % (response.status_code, status.HTTP_200_OK, response.content))

    def do_locking_raise_resource_locked(self, resource, a_function, arguments):
        raise ResourceLockedError('testing')

    def _crear_sesion_expirada(self, vendedor):
        limite = settings.API_MOBILE_DIAS_EXPIRACION_DE_SESION + 5
        fecha = timezone.now() - timezone.timedelta(days=limite)
        with freeze_time(fecha):
            sesion = self._iniciar_sesion_para(vendedor=vendedor, fcm_token='1234')
        return sesion

    def _crear_pedido_de_toyota_para_supervisor(self, supervisor):
        toyota = Marca.obtener_or_crear_con_nombre('Toyota')
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor)
        self.creador_de_contexto.crear_filtro_para_marca(pedido, nombre_de_marca=toyota.nombre())
        return pedido
