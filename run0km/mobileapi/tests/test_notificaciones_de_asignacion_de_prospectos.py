import json

import mock
from django.urls import reverse
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time
from rest_framework import status
from rest_framework.authtoken.models import Token

from campanias.models import CategoriaDeCampania, Campania
from mobileapi.sincronizador import Sincronizador
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable
from prospectos.models import ModoSeleccionDeVendedorChoices, MetodosDeAsignacionChoices, Prospecto, \
    Marca
from prospectos.models.entrega_de_datos.opciones import TipoDeSeleccionParaAsignacionChoices, \
    AccionesDeAsignacionDeVendedorChoices
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from testing.creador_de_contexto import CreadorDeContexto
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from prospectos.utils.carga_por_csv import CargadorDeProspectosPorCSV
from prospectos.views.admin_forms import AddProspectosCSVAdminForm
from testing.test_utils import IngresoDeProspectosViaCSVHelper, PedidoDeCreacionXML


@freeze_time("2012-08-10 13:21:34")
@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
@override_settings(REALIZAR_NORMALIZACION=False)
@override_settings(REALIZAR_CHECKEO_DE_WHATSAPP=False)
class IntegracionDeNotificacionesParaAsignacionesTest(SincronizadorCoreTest):
    def setUp(self):
        super(IntegracionDeNotificacionesParaAsignacionesTest, self).setUp()
        self.sincronizador = Sincronizador()
        self.hoy = timezone.now()
        self.prospecto = self.fixture['p_1']
        self.campania_de_sms = self.fixture['camp_1']
        self.vendedor = self.prospecto.vendedor
        self.supervisor = self.fixture['sup_1']
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.vendedor.supervisor,
                                                     fixture=self.fixture)
        self.tipo_sincronizable = TipoProspectoSincronizable()

    def _crear_campania_externa_para(self, vendedor):
        concesionaria = vendedor.obtener_concesionaria()
        origen = self.fixture['tipo_s']
        try:
            categoria = CategoriaDeCampania.objects.externas().get(tipo_de_origen=origen)
        except CategoriaDeCampania.DoesNotExist:
            categoria = CategoriaDeCampania.nueva_externa('externa', origen)
        campania = Campania.objects.create(concesionaria=concesionaria, nombre='mi-campania', categoria=categoria)
        return campania

    def _asignar_prospecto_a_vendedor(self, prospecto, vendedor, supervisor):
        repartidor = RepartidorDeProspectos.nuevo()
        if vendedor is None:
            repartidor.asignar_responsable_a(supervisor=supervisor, prospecto=prospecto, debe_sincronizar=False)
        else:
            repartidor.asignar_prospecto_a(vendedor=vendedor, prospecto=prospecto, debe_sincronizar=False)

    def logear_supervisor(self, supervisor=''):
        self.supervisor = supervisor or self.fixture['sup_1']
        self.user = self.supervisor.user
        login_correct = self.client.login(username=self.user.username, password='admin')
        self.assertTrue(login_correct)

    def _post_data_cargar_prospecto(self, cantidad_de_campos_extra_agregados, email, marca, mensaje, nombre, provincia,
                                    nombre_de_campania='', campania_creada=False, calidad='', prefijo_celular='',
                                    calidad_numerica=3, numero_celular='', prefijo_telefono='',
                                    numero_telefono='', vendedor=''):
        data = {'cantidad_de_campos_extra_agregados': cantidad_de_campos_extra_agregados,
                'nombre_de_campania': nombre_de_campania, 'email': email,
                'campania_creada': json.dumps(campania_creada),
                'marca': Marca.objects.con_codigo(marca.lower()).id, 'mensaje': mensaje, 'nombre': nombre, 'calidad_numerica': calidad_numerica,
                'calidad': calidad, 'provincia': provincia, 'prefijo_celular': prefijo_celular,
                'numero_celular': numero_celular, 'prefijo_telefono': prefijo_telefono,
                'numero_telefono': numero_telefono, 'vendedor': vendedor}
        return data

    def _post_cargar_prospecto(self, user, data):
        self.login_and_assert_correct(user)
        path = reverse('cargar-prospecto')
        response = self.client.post(path=path, data=data, follow=True)
        return response

    def _post_reasignar_prospecto(self, prospecto, vendedor):
        self.login_and_assert_correct(vendedor.responsable().user)
        url = reverse('reasignar', kwargs={'pk': prospecto.pk})
        response = self.client.post(url, {'vendedor': vendedor.pk}, follow=True)
        return response

    def _aplicar_post_via_administracion_de_supervisores(self, forma_de_asignacion, cantidad, accion,
                                                         vendedores_ids=None, vendedor_id='',
                                                         equipo_id='', metodo='', seleccion_de_prospectos='{}',
                                                         asignados='si'):
        if not vendedores_ids:
            vendedores_ids = self.fixture['sup_1'].id
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': asignados,
                  'vendedores': vendedores_ids, 'campania': '', 'accion': accion,
                  'cantidad': cantidad,
                  'asignar': forma_de_asignacion,
                  'metodo': metodo,
                  'equipo': equipo_id,
                  'vendedor': vendedor_id,
                  'seleccion_de_prospectos': seleccion_de_prospectos
                  }
        url = reverse('administracion')
        response = self.client.post(url, params)
        return response

    def _post_reasignar_via_administracion_de_supervisores_a_vendedor(self, cantidad, vendedor,
                                                                      ids_de_vendedores_con_prospectos_a_reasignar=None,
                                                                      seleccion_de_prospectos='{}', asignados='si'):
        response = self._aplicar_post_via_administracion_de_supervisores(forma_de_asignacion='vendedor',
                                                                         cantidad=cantidad,
                                                                         vendedor_id=vendedor.id,
                                                                         vendedores_ids=ids_de_vendedores_con_prospectos_a_reasignar,
                                                                         seleccion_de_prospectos=seleccion_de_prospectos,
                                                                         asignados=asignados,
                                                                         accion='reasignar')
        return response

    def _post_asignar_via_asignacion_inicial(self, supervisores_ids=None, cantidad=1, campanias_pk_list=None,
                                             asignar_a=None,
                                             asignar_segun=None, vendedor_id=None, equipo_id=None, accion=None,
                                             metodo=None,
                                             restricciones=None, aplicar_restricciones_del_pedido=False,
                                             descontar_a_pedidos=False, pedidos=None,
                                             metodo_por_productividad=False):
        supervisores_ids = supervisores_ids or [self.fixture['sup_1'].id]
        asignar_a = asignar_a or ModoSeleccionDeVendedorChoices.TODOS
        asignar_segun = asignar_segun or TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES
        vendedor_id = vendedor_id or ''
        accion = accion or AccionesDeAsignacionDeVendedorChoices.ASIGNAR
        metodo = metodo or MetodosDeAsignacionChoices.UNIFORME
        pedidos = pedidos or []
        campanias_pk_list = campanias_pk_list or [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk,
                                                  self.fixture['camp_3'].pk]
        equipo_id = equipo_id or ''
        restricciones = restricciones or []

        data = {
            'accion': accion,
            'responsables': supervisores_ids,
            'asignar_a': asignar_a,
            'asignar_segun': asignar_segun,
            'vendedor': vendedor_id,
            'equipo': equipo_id,
            'metodo': metodo,
            'cantidad': cantidad,
            'campanias': campanias_pk_list,
            'marcas': list(Prospecto.objects.marcas().codigos()),
            'provincias': [each.upper() for each in Prospecto.objects.provincias(Prospecto.objects.all())],
            'prefijos': Prospecto.objects.prefijos(Prospecto.objects.all()),
            'descontar_a_pedidos': descontar_a_pedidos,
            'pedidos': pedidos,
            'restricciones': restricciones,
            'aplicar_restricciones_del_pedido': aplicar_restricciones_del_pedido,
            'metodo_por_productividad': metodo_por_productividad
        }
        asignar_url = '/admin/prospectos/prospecto/asignacion_inicial/'
        response = self.client.post(asignar_url, data)
        return response

    def _post_agregar_prospecto(self, datos, supervisor=None):
        if not supervisor:
            supervisor = self.supervisor
        token = Token(user=supervisor.user)
        token.save()
        url = reverse('api-prospecto-add')
        response = self.client.post(url, datos,
                                    HTTP_AUTHORIZATION='Token %s' % token.key,
                                    content_type='application/xml')
        return response

    def _assert_envio_de_notificacion_fcm(self, mock_fcm):
        self.assertTrue(mock_fcm.called)

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_carga_via_csv_prospecto_asignado_notifica_asignacion(self, pusher_trigger_mock, on_commit_mock, ):
        campania = Campania.objects.filter(categoria__tipo_de_origen__codigo='S')[0]
        vendedor = self.fixture['vend_2']
        sesion = self._iniciar_sesion_para(vendedor)
        data = {'origen': campania.categoria.tipo_de_origen.id,
                'campania': campania.id,
                'responsable': self.supervisor.id}
        form = AddProspectosCSVAdminForm(data, IngresoDeProspectosViaCSVHelper().json_con_archivo_con_template_para(0))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        prospecto = Prospecto.objects.get(nombre='Pepe')
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[prospecto],
                                ids_modelos_removidos=[], modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_carga_via_csv_prospecto_asignado_fuera_de_horario_laboral_debe_notificar_asignacion(
            self, pusher_trigger_mock, on_commit_mock):
        with freeze_time(self._fuera_de_horario_laboral()):
            campania = Campania.objects.filter(categoria__tipo_de_origen__codigo='S')[0]
            vendedor = self.fixture['vend_2']
            sesion = self._iniciar_sesion_para(vendedor)
            data = {'origen': campania.categoria.tipo_de_origen.id,
                    'campania': campania.id,
                    'responsable': self.supervisor.id}
            form = AddProspectosCSVAdminForm(
                data, IngresoDeProspectosViaCSVHelper().json_con_archivo_con_template_para(0))
            self.assertTrue(form.is_valid())
            cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
            cargador.cargar_prospectos()
            self._assert_envio_de_comando_prospecto_modificado(pusher_trigger_mock, vendedor)
            prospecto = Prospecto.objects.get(nombre='Pepe')
            self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[prospecto],
                                    ids_modelos_removidos=[], modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_crear_y_asignar_prospecto_a_vendedor_habilitado_via_API_debe_notificar_asignacion(
            self, pusher_trigger_mock, on_commit_mock):
        self.logear_supervisor()
        sesion = self._iniciar_sesion_para(self.vendedor)
        datos = PedidoDeCreacionXML().desde_api_con_formato_simple()
        datos = datos.replace('<origen>S</origen>', '<origen>%s</origen>' % self.campania_de_sms.origen)
        datos = datos.replace('<campania>SMS</campania>', '<campania>%s</campania>' % self.campania_de_sms.nombre)
        response = self._post_agregar_prospecto(datos)

        self.assertEqual(response.status_code, 201)
        prospecto_uno = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[prospecto_uno],
                                ids_modelos_removidos=[], modelos_modificados=[])

    # @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    # @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    # def test_crear_y_asignar_prospecto_a_vendedor_via_API_fuera_de_horario_laboral_no_debe_notificar_asignacion(
    #         self, pusher_trigger_mock, on_commit_mock):
    #     with freeze_time(self._fuera_de_horario_laboral()):
    #         self.logear_supervisor()
    #         sesion = self._iniciar_sesion_para(self.vendedor)
    #         datos = self.XML_DATA_PARA_API_ADD_PROSPECTO.replace('<origen>S</origen>',
    #                                                              '<origen>%s</origen>' % self.campania_de_sms.origen)
    #         datos = datos.replace('<campania>SMS</campania>', '<campania>%s</campania>' % self.campania_de_sms.nombre)
    #         response = self._post_agregar_prospecto(datos)
    #         self.assertEquals(response.status_code, 201)
    #         prospecto_uno = Prospecto.objects.get(nombre="Nombre Prospecto 1")
    #         self._deny_envio_de_comando_prospecto_modificado(pusher_trigger_mock, self.vendedor)
    #         self._assert_sesion_de(sesion,
    #                                prospectos_asignados=[prospecto_uno],
    #                                ids_prospectos_removidos=[],
    #                                prospectos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_crear_y_asignar_prospecto_a_vendedor_habilitado_via_carga_de_prospectos_debe_notificar_asignacion(
            self, pusher_trigger_mock, on_commit_mock):
        self.logear_supervisor()
        sesion = self._iniciar_sesion_para(self.vendedor)
        self._crear_pedido_de_toyota_para_supervisor(self.vendedor.responsable())
        campania = self._crear_campania_externa_para(self.vendedor)
        calidad = campania.categoria.tipo_de_origen
        nombre = 'Juan Perez'
        datos_de_prospecto = {
            'cantidad_de_campos_extra_agregados': 0,
            'calidad': calidad.pk,
            'mensaje': 'asd',
            'prefijo_celular': '123',
            'numero_celular': '456',
            'email': '<EMAIL>',
            'provincia': 'Buenos Aires Interior',
            'marca': self.vendedor.marcas_pedidas()[0],
            'nombre': nombre
        }

        data = self._post_data_cargar_prospecto(nombre_de_campania=campania.nombre, **datos_de_prospecto)
        response = self._post_cargar_prospecto(user=self.vendedor.user, data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(Prospecto.objects.filter(nombre=nombre).exists())
        nuevo_prospecto = Prospecto.objects.get(nombre=nombre)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[nuevo_prospecto],
                                ids_modelos_removidos=[], modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_via_asignacion_inicial_a_vendedor_habilitado_en_horario_laboral_debe_notificar_asignacion(
            self, pusher_trigger_mock, on_commit_mock):
        self.logear_supervisor()
        prospecto_sin_asignar_uno = self.fixture['p_4']
        prospecto_sin_asignar_dos = self.fixture['p_5']
        sesion = self._iniciar_sesion_para(self.vendedor, fcm_token='1234')
        response = self._post_asignar_via_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                             asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
                                                             vendedor_id=self.vendedor.pk,
                                                             cantidad=2)
        self.assertEqual(response.status_code, 200)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[prospecto_sin_asignar_uno,
                                                                                    prospecto_sin_asignar_dos],
                                ids_modelos_removidos=[], modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_via_asignacion_inicial_a_vendedor_habilitado_fuera_de_horario_laboral_no_debe_notificar(
            self, pusher_trigger_mock, on_commit_mock):
        with freeze_time(self._fuera_de_horario_laboral()):
            self.logear_supervisor()
            prospecto_sin_asignar_uno = self.fixture['p_4']
            prospecto_sin_asignar_dos = self.fixture['p_5']
            sesion = self._iniciar_sesion_para(self.vendedor, fcm_token='1234')
            response = self._post_asignar_via_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                                 asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
                                                                 vendedor_id=self.vendedor.pk,
                                                                 cantidad=2)
            self.assertEqual(response.status_code, 200)
            self._deny_envio_de_comando_prospecto_modificado(pusher_trigger_mock, self.vendedor)
            self._assert_sesion_con(sesion, self.tipo_sincronizable,
                                    modelos_asignados=[prospecto_sin_asignar_uno,
                                                       prospecto_sin_asignar_dos], ids_modelos_removidos=[],
                                    modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_reasignar_prospecto_sin_asignar_a_vendedor_habilitado_via_reasignar_debe_pedir_sincronizacion(
            self, pusher_trigger_mock, on_commit_mock):
        sesion = self._iniciar_sesion_para(self.vendedor)
        self._asignar_prospecto_a_vendedor(prospecto=self.prospecto,
                                           vendedor=None,
                                           supervisor=self.vendedor.supervisor)
        self._post_reasignar_prospecto(self.prospecto, vendedor=self.vendedor)
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, self.vendedor)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[self.prospecto],
                                ids_modelos_removidos=[], modelos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_reasignar_prospecto_asignado_a_vendedor_via_reasignar_debe_notificar_asignacion_y_remocion(
            self, pusher_trigger_mock, on_commit_mock):
        otro_vendedor = self.fixture['vend_2']
        sesion = self._iniciar_sesion_para(self.vendedor)
        sesion_de_otro_vendedor = self._iniciar_sesion_para(otro_vendedor)
        self._asignar_prospecto_a_vendedor(prospecto=self.prospecto,
                                           vendedor=otro_vendedor,
                                           supervisor=self.vendedor.supervisor)
        self._post_reasignar_prospecto(self.prospecto, vendedor=self.vendedor)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=False)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, otro_vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[self.prospecto],
                                ids_modelos_removidos=[], modelos_modificados=[])
        self._assert_sesion_con(sesion_de_otro_vendedor, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[self.prospecto.id], modelos_modificados=[])

    # @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    # @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    # def test_reasignar_prospecto_asignado_via_reasignar_fuera_horario_laboral_debe_notificar_asignacion_y_remocion(
    #         self, pusher_trigger_mock, on_commit_mock):
    #     """
    #         Fuera de horario laboral se envia pedidos de sincronizacion para remociones pero no para asignaciones.
    #     """
    #     with freeze_time(self._fuera_de_horario_laboral()):
    #         otro_vendedor = self.fixture['vend_2']
    #         sesion = self._iniciar_sesion_para(self.vendedor)
    #         sesion_de_otro_vendedor = self._iniciar_sesion_para(otro_vendedor)
    #         self._asignar_prospecto_a_vendedor(prospecto=self.prospecto,
    #                                            vendedor=otro_vendedor,
    #                                            supervisor=self.vendedor.supervisor)
    #         self._post_reasignar_prospecto(self.prospecto, vendedor=self.vendedor)
    #         # Se pedido para remocion
    #         self._assert_envio_de_comando_prospecto_modificado(
    #             pusher_trigger_mock, otro_vendedor, verificar_call_once=False)
    #         # No se envia pedido de asignacion
    #         self._deny_envio_de_comando_prospecto_modificado(
    #             pusher_trigger_mock,  self.vendedor, verificar_call_once=False)
    #
    #         self._assert_sesion_de(sesion,
    #                                prospectos_asignados=[self.prospecto],
    #                                ids_prospectos_removidos=[],
    #                                prospectos_modificados=[])
    #         self._assert_sesion_de(sesion_de_otro_vendedor,
    #                                prospectos_asignados=[],
    #                                ids_prospectos_removidos=[self.prospecto.id],
    #                                prospectos_modificados=[])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_reasignar_prospecto_asignado_via_administracion_de_supervisores_debe_notificar_asignacion_y_remocion(
            self, pusher_trigger_mock, on_commit_mock):
        self.logear_supervisor()
        otro_vendedor = self.fixture['vend_2']
        sesion = self._iniciar_sesion_para(self.vendedor)
        sesion_de_otro_vendedor = self._iniciar_sesion_para(otro_vendedor)
        self._asignar_prospecto_a_vendedor(prospecto=self.prospecto,
                                           vendedor=self.vendedor,
                                           supervisor=self.vendedor.supervisor)
        self._post_reasignar_via_administracion_de_supervisores_a_vendedor(
            vendedor=otro_vendedor, ids_de_vendedores_con_prospectos_a_reasignar=[self.vendedor.id], cantidad=1)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=False)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, otro_vendedor, verificar_call_once=False)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[self.prospecto.id], modelos_modificados=[])
        self._assert_sesion_con(sesion_de_otro_vendedor, self.tipo_sincronizable,
                                modelos_asignados=[self.prospecto], ids_modelos_removidos=[],
                                modelos_modificados=[])
