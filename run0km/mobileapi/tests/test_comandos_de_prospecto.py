# coding=utf-8

import mock
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from conversaciones.models import Conversacion
from mobileapi.comandos import Comando, NuevoLlamadoRealizadoComando, AgregarFormularioDeLlamadaRealizadaComando, \
    AgregaComentarioComando, AgregaLlamadaProgramadaComando, FinalizacionComando, CargarVentaComando, \
    ReactivarSeguimientoComando, EnviarPropuestaComando, AgregarMensajesAProspectoComando
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable
from notificaciones import FormaDeEnvioWhatsapp
from propuestas.models import TipoDePlan, Plan, Propuesta, RegistroDiarioDeEnvioDePropuesta
from prospectos.models import Marca, Modelo
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.test_utils import reload_model
from vendedores.models import ConfiguracionDeCanal


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
@freeze_time("2017-03-20 13:21:34")
class ComandosTest(SincronizadorCoreTest):
    def setUp(self):
        super(ComandosTest, self).setUp()
        self.vendedor_uno = self.fixture['vend_1']
        self.prospecto_de_vendedor_uno = self.fixture['p_1']
        # self.prospecto_de_vendedor_dos = self.fixture['p_6']
        self.tipo_sincronizable = TipoProspectoSincronizable()

    def _crear_propuesta_para_whatsapp(self, texto, imagen_whatsapp=None):
        tipo_de_plan = TipoDePlan.nuevo("codigo", 2, "nombre")
        marca = Marca.nueva_normalizada("marca test")
        modelo = Modelo.nuevo("modelo test", "codigo", marca)
        plan = Plan.nuevo(3, "nombre plan", 1, 1, tipo_de_plan, 5, 20, 29, 24242, 29000, modelo=modelo)
        return Propuesta.nueva(1, self.vendedor_uno.user, "propuesta 1", marca, plan,
                               texto_whatsapp=texto, imagen_whatsapp=imagen_whatsapp)

    def _configurar_limite_diario_a(self, vendedor, forma_de_envio, cantidad_actual, cantidad_limite):
        ConfiguracionDeCanal.objects.all().delete()
        ConfiguracionDeCanal.nuevo(FormaDeEnvioWhatsapp, cantidad_limite)
        registro = RegistroDiarioDeEnvioDePropuesta.nuevo_para(vendedor.responsable(), forma_de_envio)
        registro._cantidad = cantidad_actual
        registro.save()

    def _vender_prospecto(self, prospecto, vendedor):
        venta = self.creador_de_contexto.asignar_venta_a(prospecto=prospecto, vendedor=vendedor)
        return venta

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_llamada_realizada_debe_agregar_llamada(self, pusher_trigger_mock):
        fecha_inicio_string = "2017-01-23-11:51:35"
        duracion = 345
        argumentos = self.creador_de_comandos.argumentos_comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string,
            duracion=duracion)
        comando = Comando.nuevo_para(nombre=NuevoLlamadoRealizadoComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, NuevoLlamadoRealizadoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto_de_vendedor_uno, duracion, fecha_inicio_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_llamada_realizada_con_duracion_nula_debe_agregar_llamada(self,
                                                                                              pusher_trigger_mock):
        fecha_inicio_string = "2017-01-23-11:51:35"
        duracion = None
        argumentos = self.creador_de_comandos.argumentos_comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string,
            duracion=duracion)
        comando = Comando.nuevo_para(nombre=NuevoLlamadoRealizadoComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, NuevoLlamadoRealizadoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto_de_vendedor_uno, duracion, fecha_inicio_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_agregar_llamada_realizada_con_llamado_programado_agrega_llamada_y_borra_programado(
            self, pusher_trigger_mock):
        ahora = timezone.localtime(timezone.now())
        fecha_inicio_string = ahora.strftime("%Y-%m-%d-%H:%M:%S")
        duracion = 345
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        fecha = ahora + timezone.timedelta(minutes=10)
        gestor.programar_nuevo_llamado_para(self.prospecto_de_vendedor_uno, fecha=fecha)
        argumentos = self.creador_de_comandos.argumentos_comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string,
            duracion=duracion)
        comando = Comando.nuevo_para(nombre=NuevoLlamadoRealizadoComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, NuevoLlamadoRealizadoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto_de_vendedor_uno, duracion, fecha_inicio_string)
        prospecto = reload_model(self.prospecto_de_vendedor_uno)
        self.assertFalse(prospecto.tiene_llamado())
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_agregar_llamada_realizada_programado_para_el_dia_siguiente_agrega_llamada_y_no_borra_programado(
            self, pusher_trigger_mock):
        ahora = timezone.now()
        fecha_inicio_string = ahora.strftime("%Y-%m-%d-%H:%M:%S")
        duracion = 345
        fecha = ahora + timezone.timedelta(days=1)
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        gestor.programar_nuevo_llamado_para(self.prospecto_de_vendedor_uno, fecha=fecha)
        argumentos = self.creador_de_comandos.argumentos_comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string,
            duracion=duracion)
        comando = Comando.nuevo_para(nombre=NuevoLlamadoRealizadoComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, NuevoLlamadoRealizadoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto_de_vendedor_uno, duracion, fecha_inicio_string)
        prospecto = reload_model(self.prospecto_de_vendedor_uno)
        self.assertTrue(prospecto.tiene_llamado())
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_formulario_de_llamada_realizada_debe_agregar_formulario(
            self, pusher_trigger_mock):
        duracion = 345
        fecha_inicio_string = "2017-01-23-11:51:35"
        self._agregar_llamada_realizada_a(self.prospecto_de_vendedor_uno, self.vendedor_uno,
                                          duracion, fecha_inicio_string)
        pregunta = "¿Hubo interés?"
        respuesta = "true"
        argumentos = self.creador_de_comandos.argumentos_agregar_formulario_de_llamado(
            pregunta, respuesta, fecha_inicio_string, duracion)
        comando = Comando.nuevo_para(nombre=AgregarFormularioDeLlamadaRealizadaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregarFormularioDeLlamadaRealizadaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_llamada_realizada_tiene_formulario_de_interes(self.prospecto_de_vendedor_uno, pregunta, respuesta)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_formulario_de_llamada_realizada_crea_la_llamada_cuando_no_existe(
            self, pusher_trigger_mock):
        duracion = 345
        fecha_inicio_string = "2017-01-23-11:51:35"
        pregunta = "¿Hubo interés?"
        respuesta = "true"
        argumentos = self.creador_de_comandos.argumentos_agregar_formulario_de_llamado(
            pregunta, respuesta, fecha_inicio_string, duracion)
        comando = Comando.nuevo_para(nombre=AgregarFormularioDeLlamadaRealizadaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregarFormularioDeLlamadaRealizadaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto_de_vendedor_uno, duracion, fecha_inicio_string)
        self._assert_llamada_realizada_tiene_formulario_de_interes(self.prospecto_de_vendedor_uno, pregunta, respuesta)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_cargar_venta_debe_cargar_la_venta(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        fecha_de_realizacion_string = "2017-01-23"
        marca = 'Ford'
        modelo = 'Ka'
        precio = '1232456'
        numero_de_contrato = '112345678'
        argumentos = self.creador_de_comandos.argumentos_cargar_venta(
            marca=marca, modelo=modelo, precio=precio,
            numero_de_contrato=numero_de_contrato,
            fecha_de_realizacion_string=fecha_de_realizacion_string)

        comando = Comando.nuevo_para(nombre=CargarVentaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, CargarVentaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_venta_con_datos(prospecto=self.prospecto_de_vendedor_uno, marca=marca,
                                                     modelo=modelo, precio=str(precio),
                                                     numero_de_contrato=str(numero_de_contrato),
                                                     fecha_string=fecha_de_realizacion_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_cargar_venta_a_prospecto_vendido_no_debe_lanzar_excepcion(self, pusher_trigger_mock):
        venta_vieja = self._vender_prospecto(prospecto=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._iniciar_sesion_para(self.vendedor_uno)
        fecha_de_realizacion_string = "2017-01-23"
        marca = 'Ford'
        modelo = 'Ka'
        precio = '1232456'
        numero_de_contrato = '112345678'
        argumentos = self.creador_de_comandos.argumentos_cargar_venta(
            marca=marca, modelo=modelo, precio=precio,
            numero_de_contrato=numero_de_contrato,
            fecha_de_realizacion_string=fecha_de_realizacion_string)

        comando = Comando.nuevo_para(nombre=CargarVentaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, CargarVentaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)

        self._assert_prospecto_tiene_venta_con_datos(prospecto=self.prospecto_de_vendedor_uno, marca=venta_vieja.marca,
                                                     modelo=venta_vieja.modelo, precio=str(venta_vieja.precio),
                                                     numero_de_contrato=str(venta_vieja.numero_de_contrato),
                                                     fecha_string=venta_vieja.fecha_de_realizacion.strftime(
                                                         CargarVentaComando.FORMATO_FECHA))
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_nuevo_comentario_debe_agregar_comentario(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        fecha_string = "2017-03-20-16:49:30"
        texto = 'a comment'
        es_automatico = False
        argumentos = self.creador_de_comandos.argumentos_nuevo_comentario(
            fecha=fecha_string, texto=texto, es_automatico=es_automatico)
        comando = Comando.nuevo_para(nombre=AgregaComentarioComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregaComentarioComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_comentario(
            self.prospecto_de_vendedor_uno, self.vendedor_uno, texto, es_automatico, fecha_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_nueva_llamada_programada_debe_agregar_llamada(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        fecha_string = "2017-03-20-16:49:30"
        argumentos = self.creador_de_comandos.argumentos_nueva_llamada_programada(fecha=fecha_string)
        comando = Comando.nuevo_para(nombre=AgregaLlamadaProgramadaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregaLlamadaProgramadaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_nueva_llamada_programada(self.prospecto_de_vendedor_uno, fecha_string)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_finalizacion_debe_finalizar_prospecto(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        argumentos = self.creador_de_comandos.argumentos_finalizacion_prospecto(
            descripcion_motivo='Usuario Pierde Interes',
            texto_otro_motivo='Me quiso robar mi chupetin',
            comentario='Tambien me robo una pantufla')
        comando = Comando.nuevo_para(nombre=FinalizacionComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, FinalizacionComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_nueva_finalizacion(prospecto=self.prospecto_de_vendedor_uno,
                                                        vendedor=self.vendedor_uno,
                                                        otro_motivo='',
                                                        descripcion_de_motivo='Usuario Pierde Interes',
                                                        comentario='Tambien me robo una pantufla')
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_de_envio_de_propuesta(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        self.habilitar_whatsapp_del_vendedor(self.vendedor_uno)
        cantidad_actual = 5
        self._configurar_limite_diario_a(self.vendedor_uno, FormaDeEnvioWhatsapp,
                                         cantidad_actual=cantidad_actual, cantidad_limite=50)
        texto = 'Hello whatsapp'
        propuesta = self._crear_propuesta_para_whatsapp(texto=texto)
        argumentos = self.creador_de_comandos.argumentos_enviar_propuesta(id_propuesta=propuesta.id)
        comando = Comando.nuevo_para(nombre=EnviarPropuestaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, EnviarPropuestaComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_se_envio_una_propuesta_correctamente(
            vendedor=self.vendedor_uno, propuesta=propuesta, prospecto=self.prospecto_de_vendedor_uno,
            cantidad_actual=cantidad_actual)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_reactivar_seguimiento_debe_reactivar_prospecto(self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        self._finalizar_seguimiento(self.prospecto_de_vendedor_uno, self.vendedor_uno)
        comando = Comando.nuevo_para(nombre=ReactivarSeguimientoComando.nombre(), argumentos={},
                                     tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, ReactivarSeguimientoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_reactivado(self.prospecto_de_vendedor_uno)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_mensajes_a_prospecto_no_modifica_el_prospecto_si_no_hay_mensajes_en_la_lista(
            self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        tipo = Conversacion.TIPO_WHATSAPP
        argumentos = self.creador_de_comandos.argumentos_agregar_mensajes_a_prospecto(mensajes=[], tipo=tipo)
        comando = Comando.nuevo_para(nombre=AgregarMensajesAProspectoComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregarMensajesAProspectoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_no_tiene_conversacion_de_tipo(self.prospecto_de_vendedor_uno, tipo)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_evaluar_comando_agregar_mensajes_a_prospecto_crea_una_conversacion_al_prospecto_si_no_la_tiene(
            self, pusher_trigger_mock):
        self._iniciar_sesion_para(self.vendedor_uno)
        tipo = Conversacion.TIPO_WHATSAPP
        mensajes = [{"text": "test1", "date": "2018-05-12-17:54:20"}, {"text": "test2", "date": "2018-05-12-17:54:24"}]
        argumentos = self.creador_de_comandos.argumentos_agregar_mensajes_a_prospecto(mensajes=mensajes, tipo=tipo)
        comando = Comando.nuevo_para(nombre=AgregarMensajesAProspectoComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        self.assertIsInstance(comando, AgregarMensajesAProspectoComando)
        comando.ejecutar(modelo=self.prospecto_de_vendedor_uno, vendedor=self.vendedor_uno)
        self._assert_prospecto_tiene_conversacion_de_cierto_tipo_con_mensajes(self.prospecto_de_vendedor_uno,
                                                                              tipo,
                                                                              len(mensajes))
        self.assertFalse(pusher_trigger_mock.called)
