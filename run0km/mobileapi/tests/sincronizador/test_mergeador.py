# coding=utf-8
import mock
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from mobileapi.comandos import <PERSON>mando, AgregaComentarioComando, FinalizacionComando, ReactivarSeguimientoComando, \
    CargarVentaComando, NuevoLlamadoRealizadoComando, AgregarFormularioDeLlamadaRealizadaComando, \
    AgregaLlamadaProgramadaComando
from mobileapi.mergeador import MergeadorDeConflictos
from mobileapi.tests.creador_de_comandos import CreadorDeComandosMobile
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from testing.test_utils import reload_model


@mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
@freeze_time("2017-01-20 13:21:34")
class MergeadorDeConflictosTest(SincronizadorCoreTest):
    def setUp(self):
        super(MergeadorDeConflictosTest, self).setUp()
        self.mergeador = MergeadorDeConflictos.nuevo()
        self.creador_de_comandos = CreadorDeComandosMobile.nuevo()
        self.prospecto = self.fixture['p_1']
        self.vendedor = self.prospecto.vendedor
        self.tipo_sincronizable = TipoProspectoSincronizable()

    def test_sin_cambios_locales_debe_aplicar_cambios_remotos(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        fecha_inicio_string = '2017-01-23-11:51:35'
        duracion = 345
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[self._comando_agregar_llamado_realizado(fecha_inicio_string, duracion)],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_prospecto_tiene_llamada_realizada(
            self.prospecto, duracion=duracion, fecha_inicio_string=fecha_inicio_string)

    def test_con_cambios_locales_y_remotamente_se_agrega_un_comentario_debe_aplicar_cambios(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)
        fecha_string = "2017-03-20-16:49:30"
        texto = 'a comment'
        es_automatico = False
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[self._comando_nuevo_comentario(fecha_string=fecha_string,
                                                     texto=texto,
                                                     es_automatico=es_automatico)],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_prospecto_tiene_comentario(
            self.prospecto, self.vendedor, fecha_string=fecha_string, texto=texto, es_automatico=es_automatico)

    def test_con_cambios_locales_y_remotamente_se_agrega_llamador_realizado_debe_aplicar_cambios(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)
        fecha_inicio_string = "2017-01-23-11:51:35"
        duracion = 345
        argumentos = self.creador_de_comandos.argumentos_comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string,
            duracion=duracion)
        comando = Comando.nuevo_para(nombre=NuevoLlamadoRealizadoComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto, duracion, fecha_inicio_string)

    def test_con_cambios_locales_y_remotamente_se_agrega_formulario_de_interes_debe_aplicar_cambios(self,
                                                                                                    on_commit_mock):
        duracion = 345
        fecha_inicio_string = "2017-01-23-11:51:35"
        self._agregar_llamada_realizada_a(self.prospecto, self.vendedor, duracion, fecha_inicio_string)
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)

        pregunta = "¿Hubo interés?"
        respuesta = "true"
        argumentos = self.creador_de_comandos.argumentos_agregar_formulario_de_llamado(
            pregunta, respuesta, fecha_inicio_string, duracion)
        comando = Comando.nuevo_para(nombre=AgregarFormularioDeLlamadaRealizadaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_llamada_realizada_tiene_formulario_de_interes(self.prospecto, pregunta, respuesta)

    def test_con_cambios_locales_y_remotamente_se_agrega_llamada_programada_debe_aplicar_cambios(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)

        fecha_string = "2017-03-20-16:49:30"
        argumentos = self.creador_de_comandos.argumentos_nueva_llamada_programada(fecha=fecha_string)
        comando = Comando.nuevo_para(nombre=AgregaLlamadaProgramadaComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_prospecto_tiene_nueva_llamada_programada(self.prospecto, fecha_string)

    def test_se_vende_prospecto_y_remotamente_se_finaliza_debe_ingorar_finalizacion(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._vender_prospecto(self.prospecto, self.vendedor, sesion)
        argumentos = self.creador_de_comandos.argumentos_finalizacion_prospecto(
            descripcion_motivo='Usuario Pierde Interes',
            texto_otro_motivo='Me quiso robar mi chupetin',
            comentario='Tambien me robo una pantufla')
        comando = Comando.nuevo_para(nombre=FinalizacionComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertFalse(prospecto.finalizado)
        self.assertTrue(prospecto.vendido)

    def test_prospecto_modificado_localmente_y_remotamente_se_reactiva_seguimiento_debe_realizar_reactivacion(self,
                                                                                                              on_commit_mock):
        self._finalizar_seguimiento(self.prospecto, self.vendedor, notificar=False)
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)
        comando = Comando.nuevo_para(nombre=ReactivarSeguimientoComando.nombre(), argumentos={},
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertFalse(prospecto.finalizado)

    def test_se_vende_prospecto_y_remotamente_se_reactiva_seguimiento_debe_ingorar_reactivacion(self, on_commit_mock):
        """
            El escenario es dado un prospecto finalizado, se reactiva y se vende el prospecto localmente, y remotamente
                     se reactiva el seguimiento
        """
        self._finalizar_seguimiento(self.prospecto, self.vendedor, notificar=False)
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._reactivar_seguimiento(self.prospecto, self.vendedor, notificar=False)
        self._vender_prospecto(self.prospecto, self.vendedor, sesion)
        comando = Comando.nuevo_para(nombre=ReactivarSeguimientoComando.nombre(), argumentos={},
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertFalse(prospecto.finalizado)
        self.assertTrue(prospecto.vendido)

    def test_se_vende_prospecto_y_remotamente_se_reactiva_debe_ingorar_reactivacion(self, on_commit_mock):
        """
            El escenario es dado un prospecto finalizado, se reactiva y se vende el prospecto localmente, y remotamente
                     se reactiva el seguimiento
        """
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._finalizar_seguimiento(self.prospecto, self.vendedor, notificar=False)
        self._reactivar_seguimiento(self.prospecto, self.vendedor, notificar=False)
        self._vender_prospecto(self.prospecto, self.vendedor, sesion)
        comando = Comando.nuevo_para(nombre=ReactivarSeguimientoComando.nombre(), argumentos={},
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertFalse(prospecto.finalizado)
        self.assertTrue(prospecto.vendido)

    def test_prospecto_modificado_y_remotamente_carga_venta_debe_cargar_venta(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._registrar_como_modificado(self.prospecto, sesion)
        fecha_de_realizacion_string = "2017-01-23"
        marca = 'Ford'
        modelo = 'Ka'
        precio = '1232456'
        numero_de_contrato = '112345678'
        argumentos = self.creador_de_comandos.argumentos_cargar_venta(
            marca=marca, modelo=modelo, precio=precio,
            numero_de_contrato=numero_de_contrato,
            fecha_de_realizacion_string=fecha_de_realizacion_string)
        comando = Comando.nuevo_para(nombre=CargarVentaComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.vendido)
        self._assert_prospecto_tiene_venta_con_datos(
            self.prospecto, marca=marca, modelo=modelo, precio=precio,
            numero_de_contrato=numero_de_contrato, fecha_string=fecha_de_realizacion_string)

    def test_se_vende_prospecto_y_remotamente_carga_venta_debe_ingorar_venta_remota(self, on_commit_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self._vender_prospecto(self.prospecto, self.vendedor, sesion)
        fecha_de_realizacion_string = "2017-01-23"
        marca = 'Ford'
        modelo = 'Ka'
        precio = '1232456'
        numero_de_contrato = '112345678'
        argumentos = self.creador_de_comandos.argumentos_cargar_venta(
            marca=marca, modelo=modelo, precio=precio,
            numero_de_contrato=numero_de_contrato,
            fecha_de_realizacion_string=fecha_de_realizacion_string)
        comando = Comando.nuevo_para(nombre=CargarVentaComando.nombre(), argumentos=argumentos,
                                     tipo_sincronizable=self.tipo_sincronizable)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[comando],
            modelo_fue_modificado=modelo_fue_modificado)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.vendido)
        self.assertEqual(prospecto.ventas.count(), 1)

    def _registrar_como_modificado(self, prospecto, sesion):
        with mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None):
            self.sincronizador.prospecto_modificado(prospecto)
            sesion = reload_model(sesion)
            self.assertEqual(sesion.sincronizaciones_de_prospectos().count(), 1)
            return sesion

    def _comando_agregar_llamado_realizado(self, fecha_inicio_string='2017-01-23-11:51:35', duracion=345):
        comando_json = self.creador_de_comandos.comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string, duracion=duracion)
        comando = Comando.nuevo_desde(comando_json, tipo_sincronizable=self.tipo_sincronizable)
        return comando

    def _comando_nuevo_comentario(self, fecha_string="2017-03-20-16:49:30", texto='a comment', es_automatico=False):
        argumentos = self.creador_de_comandos.argumentos_nuevo_comentario(
            fecha=fecha_string, texto=texto, es_automatico=es_automatico)
        comando = Comando.nuevo_para(nombre=AgregaComentarioComando.nombre(),
                                     argumentos=argumentos, tipo_sincronizable=self.tipo_sincronizable)
        return comando

    def _vender_prospecto(self, prospecto, vendedor, sesion):
        with mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None):
            gestor = GestorDeProspecto.nuevo_para(vendedor)
            venta = gestor.cargar_venta(
                prospecto=prospecto, marca='Ford', modelo='fiesta',
                fecha_de_realizacion=timezone.now(), precio=7500320, numero_de_contrato=1313)

            sesion = reload_model(sesion)
            self.assertEqual(sesion.sincronizaciones_de_prospectos().count(), 1)
            return venta
