import mock
from django.test import override_settings
from freezegun import freeze_time

from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable


@freeze_time("2012-09-10 13:21:34")
@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class NotificacionesTest(SincronizadorCoreTest):
    def setUp(self):
        super(NotificacionesTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self.prospecto_dos = self.fixture['p_2']
        self.supervisor = self.fixture['sup_1']
        self.vendedor = self.prospecto.vendedor
        self.tipo_sincronizable = TipoProspectoSincronizable()

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_asignacion_de_prospecto_a_vendedor_habilitado_debe_ser_enviada_notificacion(self, pusher_trigger_mock):
        vendedor = self.prospecto.obtener_vendedor()
        self._iniciar_sesion_para(vendedor=self.vendedor, fcm_token='1234')
        self.sincronizador.vendedor_asignado_a_prospectos([self.prospecto], vendedor=vendedor)
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_asignacion_de_prospecto_no_nuevo_no_debe_enviarse_notificacion_fcm(self, pusher_trigger_mock):
        vendedor = self.prospecto.obtener_vendedor()
        self._iniciar_sesion_para(vendedor=self.vendedor, fcm_token='1234')
        self._pasar_prospecto_a_en_proceso(self.prospecto, vendedor)
        self.sincronizador.vendedor_asignado_a_prospectos([self.prospecto], vendedor=vendedor)
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_notificacion_prospecto_modificado_debe_enviar_pedido_de_sincronizacion_a_vendedor_habilitado(
            self, pusher_trigger_mock):
        vendedor = self.prospecto.obtener_vendedor()
        self._iniciar_sesion_para(vendedor=self.vendedor)
        self.sincronizador.prospecto_modificado(self.prospecto)
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_notificacion_remocion_a_vendedor_no_habilitado_no_debe_ser_enviada(
            self, pusher_trigger_mock):
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto, vendedor=self.vendedor)
        self.assertFalse(pusher_trigger_mock.called)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_notificacion_remocion_a_vendedor_habilitado_debe_ser_enviada(self, pusher_trigger_mock):
        vendedor = self.prospecto.obtener_vendedor()
        self._iniciar_sesion_para(vendedor=self.vendedor)
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto, vendedor=vendedor)
        mensaje_esperado = self._comando_pedido_de_sincronizacion_de_prospectos()
        self._assert_envio_de_mensaje(mensaje_esperado, pusher_trigger_mock, vendedor)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_notificacion_remocion_a_vendedor_habilitado_borra_todas_las_versiones_de_sincronizacion_asociadas(
            self, pusher_trigger_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self.sincronizador.agregar_sincronizacion_para_prospectos(vendedor=self.vendedor, prospecto=self.prospecto)
        self.sincronizador.agregar_sincronizacion_para_prospectos(vendedor=self.vendedor, prospecto=self.prospecto_dos)

        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[],
                                modelos_modificados=[self.prospecto, self.prospecto_dos])
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto, vendedor=self.vendedor)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[self.prospecto.id],
                                modelos_modificados=[self.prospecto_dos])
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto_dos, vendedor=self.vendedor)
        self._assert_sesion_con(sesion, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[self.prospecto.id, self.prospecto_dos.id],
                                modelos_modificados=[])

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_notificacion_remocion_a_vendedor_no_habilitado_no_debe_ser_enviada(
            self, pusher_trigger_mock):
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto, vendedor=self.vendedor)
        self.assertFalse(pusher_trigger_mock.called)

    @override_settings(API_MOBILE_DIAS_EXPIRACION_DE_SESION=2)
    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_asignacion_de_prospecto_a_vendedor_con_sesion_expirada_no_debe_ser_enviada_notificacion(
            self, pusher_trigger_mock):
        vendedor = self.prospecto.obtener_vendedor()
        self._crear_sesion_expirada(self.vendedor)
        self.sincronizador.vendedor_asignado_a_prospectos([self.prospecto], vendedor=vendedor)
        self.assertFalse(pusher_trigger_mock.called)
