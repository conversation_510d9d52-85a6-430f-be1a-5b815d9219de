import logging

import jsonschema
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from rest_framework.authtoken.models import Token

from conversaciones.administracion_de_conversaciones import AdministradorDeConversaciones
from conversaciones.models import Conversacion
from core.notificador.receptor import ReceptorDeNotificaciones
from mobileapi import tasks
from mobileapi.errors import ErrorDeAutenticacion, VersionDeAPIInvalida, EvaluacionDeComandoException, \
    ParametroRequerido, ConversacionInexistente, ProspectoNoEsDelVendedor, WhatsappDeshabilitado, \
    SincronizacionLockeada, SesionExpirada
from core.locker.mem_locker import Locker
from core.locker.errors import ResourceLockedError
from mobileapi.mergeador import MergeadorDeConflictos
from mobileapi.models import SesionAppMobile, Sincronizacion
from mobileapi.notificador import NotificadorDeSincronizaciones
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable, TipoConversacionSincronizable
from prospectos.models import Prospecto
from testing.test_utils import reload_model
from users.models import User

logger = logging.getLogger(__name__)


class Sincronizador(ReceptorDeNotificaciones):
    def __init__(self):
        super(Sincronizador, self).__init__()
        self._notificador = NotificadorDeSincronizaciones()

    def login(self, user, version_api, fcm_token):
        self._validar_version(version_api)
        self._validar_usuario_habilitado(user)
        token = self._reiniciar_token_para(user)
        sesion = self._nueva_sesion_para(user.vendedor, version_api)
        sesion.cambiar_version(version_api)
        sesion.cambiar_fcm_token(fcm_token)
        return token.key

    def version_api_actual(self):
        return SesionAppMobile.version_api_actual()

    def nombre_de_canal_para(self, vendedor):
        return self._notificador.nombre_de_canal_para(vendedor)

    def sesiones_de(self, vendedores):
        return SesionAppMobile.para_vendedores(vendedores)

    def sesion_de(self, vendedor):
        try:
            return SesionAppMobile.para_vendedor(vendedor)
        except SesionAppMobile.DoesNotExist:
            return None

    def lista_inicial_de_modelos_para(self, vendedor, tipo_sincronizable):
        sesion = self.sesion_de(vendedor)
        self._validar_sesion(sesion)
        sesion.reiniciar_colas_de_sincronizacion(tipo_sincronizable=tipo_sincronizable)
        modelos = tipo_sincronizable.obtener_modelos_iniciales_para(self, vendedor)
        return modelos

    def modificar_fcm(self, vendedor, fcm_token):
        self._validar_fcm_token(fcm_token)
        sesion = self.sesion_de(vendedor)
        self._validar_sesion(sesion)
        sesion.cambiar_fcm_token(fcm_token)

    def sincronizar_lote(self, pedido, vendedor):
        locker = Locker.new_for_group(group_name='mobile', expire_time=settings.API_MOBILE_LOCK_SECONDS)
        try:
            sincronizaciones = locker.do_locking(
                resource=[vendedor.pk],
                a_function=self._sincronizar_lote,
                arguments=(pedido, vendedor))
        except ResourceLockedError:
            raise SincronizacionLockeada.nueva()
        return sincronizaciones

    def _sincronizar_lote(self, pedido, vendedor):
        sesion = self.sesion_de(vendedor)
        self._validar_sesion(sesion)
        tipo_sincronizable = pedido.tipo_sincronizable()
        self._validar_expiracion_por_limite_de_modificaciones(sesion, tipo_sincronizable)
        numero_de_secuencia = pedido.numero_de_secuencia()
        sesion.validar_numero_de_secuencia(pedido)
        if numero_de_secuencia == tipo_sincronizable.ultimo_numero_de_secuencia_exitoso_para(sesion):
            sincronizaciones = self._sincronizacion_pedido_ya_procesado(pedido, sesion)
        else:
            sincronizaciones = self._sincronizar_lote_de_modelos(pedido, sesion)

        resultado = self._resultado_para(sincronizaciones, sesion, pedido)
        return resultado

    def vendedor_asignado_a_prospectos(self, prospectos, vendedor):
        if self._debe_ser_notificado(vendedor):
            # logger.debug(
            #     'Pedir sincronizacion por asignaciones a: %s - prospectos: %s' % (vendedor.pk, len(prospectos)))
            # logger.debug('Notificacion prospectos (cantidad=%d) asignado a: %s' % (len(prospectos), vendedor.pk))
            self._agregar_sincronizaciones_para_prospectos(
                vendedor=vendedor, prospectos=prospectos, accion=Sincronizacion.ASIGNACION)
            self._notificador.enviar_pedido_de_sincronizacion_de_prospectos_para(vendedor)
            # self._notificar_asignacion_prospecto_via_fcm(prospectos, vendedor)

    def vendedor_asignado_a_conversaciones(self, conversaciones, vendedor):
        if self._debe_ser_notificado(vendedor):
            # logger.debug(
            #     'Pedir sincronizacion por asignaciones a: %s - conversaciones: %s' % (vendedor.pk, len(conversaciones)))
            # logger.debug(
            #     'Notificacion conversaciones (cantidad=%d) asignado a: %s' % (len(conversaciones), vendedor.pk))
            self._agregar_sincronizaciones_para_conversaciones(
                vendedor=vendedor, conversaciones=conversaciones, accion=Sincronizacion.ASIGNACION)
            self._notificador.enviar_pedido_de_sincronizacion_de_conversaciones_para(vendedor)

    def prospecto_modificado(self, prospecto):
        """
            Envia: { 'operation': 'ready_sync_prospects', 'arguments': {} }
        """
        prospecto_recargado = reload_model(prospecto)
        vendedor = prospecto_recargado.vendedor
        if vendedor and self._debe_ser_notificado(vendedor):
            self.agregar_sincronizacion_para_prospectos(vendedor=vendedor, prospecto=prospecto_recargado)
            self._notificador.enviar_pedido_de_sincronizacion_de_prospectos_para(vendedor)

    def vendedor_removido_de(self, prospecto, vendedor):
        if self._debe_ser_notificado(vendedor=vendedor):
            prospecto_recargado = reload_model(prospecto)
            self._remover_sincronizaciones_para(vendedor=vendedor, modelo=prospecto_recargado)
            # logger.debug('Notificar remocion del prospecto a: %s' % vendedor.pk)
            self._agregar_sincronizaciones_para_prospectos(vendedor=vendedor, prospectos=[prospecto_recargado],
                                                           accion=Sincronizacion.REMOCION)
            self._notificador.enviar_pedido_de_sincronizacion_de_prospectos_para(vendedor)

    def conversacion_modificada(self, conversacion):
        conversacion_recargada = reload_model(conversacion)
        prospecto = conversacion_recargada.prospecto
        if prospecto:
            vendedor = prospecto.vendedor
            if vendedor and self._debe_ser_notificado(vendedor=vendedor):
                self.agregar_sincronizacion_para_conversaciones(vendedor=vendedor,
                                                                conversacion=conversacion_recargada)
                self._notificador.enviar_pedido_de_sincronizacion_de_conversaciones_para(vendedor)

    def agregar_sincronizacion_para_prospectos(self, vendedor, prospecto):
        return self._agregar_sincronizacion_para(vendedor, prospecto, TipoProspectoSincronizable())

    def agregar_sincronizacion_para_conversaciones(self, vendedor, conversacion):
        return self._agregar_sincronizacion_para(vendedor, conversacion, TipoConversacionSincronizable())

    def _agregar_sincronizacion_para(self, vendedor, modelo, tipo_sincronizable):
        sesion = self._nueva_sesion_para(
            vendedor=vendedor, version_api=SesionAppMobile.version_api_actual())
        return sesion.agregar_sincronizacion_para(id_model=modelo.id, tipo_sincronizable=tipo_sincronizable)

    def prospectos_iniciales_para(self, vendedor):
        prospectos = Prospecto.objects.nuevos_ordenados_por_fecha_de(vendedor=vendedor)
        cantidad = prospectos.count()
        limite = settings.API_MOBILE_LIMITE_LISTA_INICIAL_DE_PROSPECTOS
        if cantidad > limite:
            return prospectos[0:limite]
        else:
            resto = self._prospectos_priorizando_llamados_programados_recientes(limite - cantidad, vendedor)
            prospectos = prospectos | resto
        return Prospecto.objects.optimizar_consulta(prospectos.order_by('fecha'))

    def conversaciones_iniciales_para(self, vendedor):
        no_leidas = Conversacion.objects.conversaciones_de_vendedor(vendedor=vendedor).no_leidas()
        conversaciones = no_leidas.exclude(tipo=Conversacion.TIPO_CHAT)
        cantidad = conversaciones.count()
        limite = settings.API_MOBILE_LIMITE_LISTA_INICIAL_DE_CONVERSACIONES
        if cantidad > limite:
            return conversaciones[0:limite]
        else:
            leidas = Conversacion.objects.all().de_vendedor(vendedor=vendedor).leidas().exclude(
                tipo=Conversacion.TIPO_CHAT)
            resto = leidas[:limite - cantidad]
            conversaciones = list(conversaciones) + list(resto)
            conversaciones_ids = [conversacion.id for conversacion in conversaciones]
            conversaciones = Conversacion.objects.filter(id__in=conversaciones_ids)

        return conversaciones.ordenar_por_fecha_de_ultima_respuesta()

    def _prospectos_priorizando_llamados_programados_recientes(self, cantidad, vendedor):
        prospectos_ids = self._ids_de_prospectos_con_llamadas_programados_recientes(cantidad, vendedor)
        resto = len(prospectos_ids)
        if resto < cantidad:
            ids_restantes = self._ids_de_prospectos_con_llamados_vencidos_o_sin_llamados(
                cantidad - resto, vendedor, prospectos_ids)
            prospectos_ids += ids_restantes
        prospectos = Prospecto.objects.filter(id__in=prospectos_ids)
        return prospectos

    def _ids_de_prospectos_con_llamadas_programados_recientes(self, cantidad, vendedor):
        prospectos = Prospecto.objects.con_llamados_programados_recientes_para(vendedor=vendedor)
        prospectos_ids = list(prospectos.values_list('id', flat=True)[:cantidad])
        return prospectos_ids

    def _ids_de_prospectos_con_llamados_vencidos_o_sin_llamados(self, cantidad, vendedor, ids_de_prospectos):
        prospectos = Prospecto.objects.en_proceso_ordenados_por_fecha_de(vendedor=vendedor)
        prospectos = prospectos.exclude(id__in=ids_de_prospectos)
        ids = list(prospectos.values_list('id', flat=True)[:cantidad])
        return ids

    def _reiniciar_token_para(self, user):
        Token.objects.filter(user=user).delete()
        token = Token.objects.create(user=user)
        return token

    def _resultado_para(self, sincronizaciones, sesion, pedido):
        resultado = {
            "sync_sequence_number": pedido.tipo_sincronizable().ultimo_numero_de_secuencia_exitoso_para(sesion),
            "result": sincronizaciones
        }
        return resultado

    @transaction.atomic
    def _sincronizar_lote_de_modelos(self, pedido, sesion):
        modificaciones = self._sincronizar_modificaciones(pedido, sesion)
        asignaciones = self._sincronizar_asignaciones(sesion, pedido)
        remociones = self._sincronizar_remociones(sesion, pedido)
        self._actualizar_sesion(sesion, pedido)
        extra_data = pedido.tipo_sincronizable().datos_extra_de_la_sincronizacion(pedido=pedido, sesion=sesion)
        return {'assigned': asignaciones, 'removed': remociones, 'modified': modificaciones, 'extra_data': extra_data}

    def _sincronizacion_pedido_ya_procesado(self, pedido, sesion):
        tipo_sincronizable = pedido.tipo_sincronizable()
        modificaciones = self._sincronizar_modificaciones_ya_procesadas(sesion, tipo_sincronizable)
        asignaciones = self._sincronizar_asignaciones_ya_procesadas(sesion, tipo_sincronizable)
        remociones = self._sincronizar_remociones_ya_procesadas(sesion, tipo_sincronizable)
        extra_data = tipo_sincronizable.datos_extra_de_la_sincronizacion(pedido=pedido, sesion=sesion)
        return {'assigned': asignaciones, 'removed': remociones, 'modified': modificaciones, 'extra_data': extra_data}

    def _sincronizar_modificaciones(self, pedido, sesion):
        ids_modelos_modificados_remotamente = pedido.ids_de_modelos_modificados()
        tipo_sincronizable = pedido.tipo_sincronizable()
        modelos = tipo_sincronizable.clase().objects.filter(id__in=ids_modelos_modificados_remotamente)
        result = self._crear_sincronizaciones_de_modelos_no_existentes(ids_modelos=ids_modelos_modificados_remotamente,
                                                                       modelos_encontrados=modelos, sesion=sesion,
                                                                       tipo_sincronizable=tipo_sincronizable)
        modificaciones = self._crear_sincronizaciones_para(modelos=modelos, pedido=pedido, result=result, sesion=sesion,
                                                           tipo_sincronizable=tipo_sincronizable)
        modificaciones_locales = self._sincronizar_prospectos_modificados_localmente(
            sesion, ids_modelos_modificados_remotamente, tipo_sincronizable)
        modificaciones.extend(modificaciones_locales)
        return modificaciones

    def _actualizar_sesion(self, sesion, pedido):
        tipo_sincronizable = pedido.tipo_sincronizable()
        numero_de_sincronizaciones_viejas = tipo_sincronizable.ultimo_numero_de_secuencia_exitoso_para(sesion)
        tipo_sincronizable.incrementar_ultimo_numero_de_secuencia_exitoso_para(sesion)
        sesion.remover_sincronizaciones_de_version(numero_de_sincronizaciones_viejas)

    def _crear_sincronizaciones_de_modelos_no_existentes(self, ids_modelos, modelos_encontrados, sesion,
                                                         tipo_sincronizable):
        ids_modelos_encontrados = modelos_encontrados.values_list('id', flat=True)
        ids_modelos_no_encontrados = set(ids_modelos) - set(ids_modelos_encontrados)
        result = self._crear_sincronizaciones_rechazadas_para(ids_modelos_no_encontrados=ids_modelos_no_encontrados,
                                                              sesion=sesion, tipo_sincronizable=tipo_sincronizable)
        return result

    def _crear_sincronizaciones_rechazadas_para(self, ids_modelos_no_encontrados, sesion, tipo_sincronizable):
        result = []
        sincronizaciones = sesion.crear_sincronizaciones_rechazadas_para(ids_modelos_no_encontrados, tipo_sincronizable)
        for sincronizacion in sincronizaciones:
            id_model = sincronizacion.id_model()
            logger.error('No se pudo encontrar el modelo con el id (=%s)recibido.' % id_model,
                         exc_info=1)
            result_data = tipo_sincronizable.serializar_sincronizacion_fallida_para(id_model, sincronizacion.mensaje())
            result.append(result_data)
        return result

    def _crear_sincronizaciones_para(self, modelos, pedido, result, sesion, tipo_sincronizable):
        for modelo in modelos:
            try:
                comandos = pedido.lista_de_comandos_para(modelo)
                self._mergear(modelo=modelo, comandos=comandos, sesion=sesion, tipo_sincronizable=tipo_sincronizable)
            except (EvaluacionDeComandoException, jsonschema.ValidationError) as exc:
                sesion.agregar_sincronizacion_fallida_para(id_model=modelo.id, tipo_sincronizable=tipo_sincronizable,
                                                           mensaje=str(exc))
                result_data = tipo_sincronizable.serializar_sincronizacion_fallida_para(modelo.id, str(exc))
            else:
                sesion.agregar_sincronizacion_exitosa_para(id_model=modelo.id, tipo_sincronizable=tipo_sincronizable)
                result_data = tipo_sincronizable.serializar_sincronizacion_exitosa(modelo)
            result.append(result_data)
        return result

    def _sincronizar_prospectos_modificados_localmente(self, sesion, ids_modelos_modificados_remotamente,
                                                       tipo_sincronizable):
        result = []
        modificados_localmente = sesion.modelos_modificados(tipo_sincronizable).exclude(
            id__in=ids_modelos_modificados_remotamente)
        for modelo in modificados_localmente:
            sesion.agregar_sincronizacion_exitosa_para(id_model=modelo.id, tipo_sincronizable=tipo_sincronizable)
            result_data = tipo_sincronizable.serializar_sincronizacion_exitosa(modelo)
            result.append(result_data)
        return result

    def _mergear(self, modelo, comandos, sesion, tipo_sincronizable):
        mergeador = MergeadorDeConflictos.nuevo()
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=modelo.id, tipo_sincronizable=tipo_sincronizable)
        mergeador.resolver_conflictos(modelo=modelo, comandos=comandos, modelo_fue_modificado=modelo_fue_modificado)

    def _debe_ser_notificado(self, vendedor):
        if not self._es_vendedor_habilitado(vendedor):
            return False
        else:
            sesion = self.sesion_de(vendedor)
            return sesion and sesion.esta_vigente()

    def _es_vendedor_habilitado(self, vendedor):
        return vendedor.esta_activo() \
               and vendedor.tiene_cargo_vendedor() \
               and vendedor.configuracion_de_servicios().app_habilitada()

    def _validar_usuario_habilitado(self, user):
        if not user.is_vendedor() or not self._es_vendedor_habilitado(user.vendedor):
            raise ErrorDeAutenticacion.nueva()

    def _user_from_token(self, token_key):
        try:
            user = User.objects.get(auth_token__key=token_key)
        except User.DoesNotExist:
            raise ErrorDeAutenticacion.nueva()
        return user

    def _validar_suscripcion_a_canal(self, user, channel_name):
        self._validar_usuario_habilitado(user)
        canal_del_vendedor = self.nombre_de_canal_para(user.vendedor)
        if canal_del_vendedor != channel_name:
            raise ErrorDeAutenticacion.nueva()

    def _nueva_sesion_para(self, vendedor, version_api):
        return SesionAppMobile.obtener_o_crear(vendedor, version_api)

    def _validar_version(self, version_api):
        if not SesionAppMobile.es_version_valida(version_api):
            raise VersionDeAPIInvalida.nueva()

    def _validar_fcm_token(self, fcm_token):
        if not fcm_token:
            raise ParametroRequerido.nueva(parametro='fcm_token')

    def _validar_sesion(self, sesion):
        if sesion is None:
            raise ErrorDeAutenticacion.nueva()
        if not sesion.tiene_version_valida():
            raise VersionDeAPIInvalida.nueva()

    def _validar_expiracion_por_limite_de_modificaciones(self, sesion, tipo_sincronizable):
        if sesion.alcanzo_limite_de_modificaciones_para(tipo_sincronizable):
            raise SesionExpirada.nueva()

    def _agregar_sincronizaciones_para_prospectos(self, vendedor, prospectos, accion):
        sesion = self._nueva_sesion_para(
            vendedor=vendedor, version_api=SesionAppMobile.version_api_actual())
        for prospecto in prospectos:
            sesion.agregar_sincronizacion_para(id_model=prospecto.id, tipo_sincronizable=TipoProspectoSincronizable(),
                                               accion=accion)

    def _agregar_sincronizaciones_para_conversaciones(self, vendedor, conversaciones, accion):
        sesion = self._nueva_sesion_para(
            vendedor=vendedor, version_api=SesionAppMobile.version_api_actual())
        for conversacion in conversaciones:
            sesion.agregar_sincronizacion_para(id_model=conversacion.id, accion=accion,
                                               tipo_sincronizable=TipoConversacionSincronizable())

    def _remover_sincronizaciones_para(self, vendedor, modelo):
        sesion = self._nueva_sesion_para(
            vendedor=vendedor, version_api=SesionAppMobile.version_api_actual())
        sesion.remover_sincronizaciones_para(id_model=modelo.id, tipo=Sincronizacion.TIPO_PROSPECTOS)

    def _sincronizar_asignaciones(self, sesion, pedido):
        tipo_sincronizable = pedido.tipo_sincronizable()
        vendedor = sesion.vendedor()
        if vendedor.obtener_concesionaria().esta_horario_laboral(fecha=timezone.now()):
            adapter = tipo_sincronizable.adapter()
            result = [adapter.adapt_this(each) for each in sesion.modelos_asignados(tipo_sincronizable)]
            sesion.agregar_sincronizaciones_exitosas_para_asignados(tipo_sincronizable)
        else:
            result = []
        return result

    def _sincronizar_asignaciones_ya_procesadas(self, sesion, tipo_sincronizable):
        adapter = tipo_sincronizable.adapter()
        result = [adapter.adapt_this(each) for each in sesion.modelos_asignados_ya_sincronizados(tipo_sincronizable)]
        return result

    def _sincronizar_remociones(self, sesion, pedido):
        tipo_sincronizable = pedido.tipo_sincronizable()
        ids_modelos_removidos = list(sesion.ids_modelos_removidos(tipo_sincronizable))
        sesion.agregar_sincronizaciones_exitosas_para_removidos(tipo_sincronizable)
        return ids_modelos_removidos

    def _sincronizar_remociones_ya_procesadas(self, sesion, tipo_sincronizable):
        return sesion.ids_modelos_removidos_ya_sincronizados(tipo_sincronizable)

    def _sincronizar_modificaciones_ya_procesadas(self, sesion, tipo_sincronizable):
        result = [tipo_sincronizable.serializar_sincronizacion_exitosa(modelo) for modelo in
                  sesion.modelos_modificados_ya_sincronizados_exitosamente(tipo_sincronizable)]
        for sincronizacion in sesion.modificaciones_rechazadas(tipo_sincronizable):
            sincronizacion_json = tipo_sincronizable.serializar_sincronizacion_fallida_para(sincronizacion.id_model(),
                                                                                            sincronizacion.mensaje())
            result.append(sincronizacion_json)
        return result

    def _notificar_asignacion_prospecto_via_fcm(self, prospectos, vendedor):
        sesion = self.sesion_de(vendedor)
        registration_id = sesion.fcm_token()
        if registration_id and self._hay_prospectos_nuevos(prospectos):
            tasks.enviar_notification_fcm.delay(registration_id=registration_id)

    def _hay_prospectos_nuevos(self, prospectos):
        # Todo: ver si se puede filtrar por prospectos nuevos (si prospectos es un queryset)
        return any([prospecto.es_nuevo() for prospecto in prospectos])

    def _validar_prospecto_del_vendedor(self, vendedor, prospecto):
        if prospecto.obtener_vendedor() != vendedor:
            raise ProspectoNoEsDelVendedor.nueva()

    def _validar_vendedor_con_whatsapp_habilitado(self, vendedor):
        if not vendedor.habilitado_para_mensajear_por_whatsapp():
            raise WhatsappDeshabilitado.nueva()

    def conversacion_de_whatsapp(self, vendedor, prospecto):
        sesion = self.sesion_de(vendedor)
        self._validar_sesion(sesion)
        self._validar_vendedor_con_whatsapp_habilitado(vendedor)
        self._validar_prospecto_del_vendedor(vendedor, prospecto)
        administrador = AdministradorDeConversaciones(vendedor)
        try:
            conversacion = administrador.obtener_conversacion_de_whatsapp_de(prospecto)
            return conversacion
        except Conversacion.DoesNotExist:
            raise ConversacionInexistente.nueva()
