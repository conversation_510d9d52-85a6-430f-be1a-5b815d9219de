{#Por ahora es una copia de la de chat, pero la idea es que difiera bastante#}
{% load tz %}
{% load chats_utils %}
<div class="cont-popup-chat popup_email">
    {% include "conversacion-titulo.html" with icon_path="img/email-icon.png" icon_description="Conversación de Email"%}
    <div id="chat-activo">
    {% for mensaje in mensajes %}
        {% ifchanged mensaje.fecha.date %}
            <h4>{{ mensaje.fecha|date:"d/m/Y" }}</h4>
        {% endifchanged %}

        <p class="msj-{% if mensaje.proveniente_de_cliente %}entrante{%else%}saliente{%endif%} {%if mensaje.fue_enviado_al_telefono %}mj-completo{% elif mensaje.fue_enviado_por_api %}mj-enviado{%endif%}" id="mensaje-conv-{{mensaje.id}}">
            <span class="msj">{{mensaje|render_mensaje|safe}}</span>
            <img src="{{STATIC_URL}}img/{% if mensaje.proveniente_de_cliente %}entrante{%else%}saliente{%endif%}.png" width="9" height="10" class="pico" />
            {% localtime on %}
                <em class="hora">{{ mensaje.fecha|date:"H:i" }}</em>
            {% endlocaltime %}
        </p>
    {% endfor %}
    </div>
    <div id="cont-tipear">
        <textarea id="nuevo_mensaje"></textarea>
        <button id="enviar-mensaje-wa" onclick="enviar_mensaje_de_email({{ prospecto.pk }});">Enviar</button>
    </div>
</div>
