{% load tz %}
{% load chats_utils %}

{% for mensaje in mensajes %}
    {% ifchanged mensaje.fecha.date %}
        {% if not fecha_ultimo_mensaje or not fecha_ultimo_mensaje.date == mensaje.fecha.date %}
            <h4>{{ mensaje.fecha|date:"d/m/Y" }}</h4>
        {% endif %}
    {% endifchanged %}

    <p class="msj-{% if mensaje.proveniente_de_cliente %}entrante{%else%}saliente{%endif%} {%if mensaje.fue_enviado_al_telefono %}mj-completo{% elif mensaje.fue_enviado_por_api %}mj-enviado{%endif%}" id="mensaje-conv-{{mensaje.id}}">
        <span class="msj">{{mensaje|render_mensaje|safe}}</span>
        <img src="{{STATIC_URL}}img/{% if mensaje.proveniente_de_cliente %}entrante{%else%}saliente{%endif%}.png" width="9" height="10" class="pico" />
        {% localtime off %}
            <em class="hora">{{ mensaje.fecha|date:"H:i" }}</em>
        {% endlocaltime %}
    </p>
{% endfor %}