{% load static from staticfiles %}

{#<div class="cont-popup-chat popup_whatsapp">#}
{#    {% include "conversacion-titulo.html" with icon_path="img/whatsappicon.png" icon_description="Conversación de Whatsapp"%}#}
{#    <div id="chat-activo">#}
{#        {% include "mensajes-whatsapp.html" %}#}
{#    </div>#}
{#    <!-- Fin chat  -->#}
{#    <div id="cont-tipear">#}
{#        <textarea id="nuevo_mensaje"></textarea>#}
{#        <button id="enviar-mensaje-wa" onclick="enviar_mensaje_de_whatsapp({{ prospecto.pk }});">Enviar</button>#}
{#    </div>#}
{##}
{#</div>#}

{% include "conversacion-unificada-element.html" %}

<script>
    var url = "{% url 'enviar-mensaje' %}";
    var ventanaDeConversacion = new ConversacionMultimedioWindows({{ id_prospecto }}, url);
    $(document).ready(function() {
        ventanaDeConversacion.configurar()
    });
</script>
