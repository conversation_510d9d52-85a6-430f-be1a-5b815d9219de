# coding=utf-8
from django.contrib import admin
from django.urls import reverse

from conversaciones.admin_forms import MensajesWhatsappAdminForm
from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp


class VerificacionWhatsappListFilter(admin.SimpleListFilter):
    title = 'Verificación'
    parameter_name = 'verificado'

    def lookups(self, request, model_admin):
        return (
            ('True', 'tiene whatsapp'),
            ('False', 'no tiene whatsapp'),
            ('None', 'no verificado'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'True':
            return self._filtrar(queryset, True)
        if self.value() == 'False':
            return self._filtrar(queryset, False)
        if self.value() == 'None':
            return self._filtrar(queryset, None)

    def _filtrar(self, queryset, filtro):
        filtrados = [mensaje.pk for mensaje in queryset.all() if self._tiene_whatsapp_extra_para(mensaje.telefono, mensaje.prospecto) == filtro]
        return queryset.filter(emisor='V', pk__in=set(filtrados))

    def _tiene_whatsapp_extra_para(self, telefono, prospecto):
        if telefono == prospecto.telefono:
            return prospecto.tiene_whatsapp
        telefonos = prospecto.telefono_extra.filter(telefono=telefono)
        if telefonos:
            telefono_extra = telefonos.first()
            return telefono_extra.tiene_whatsapp
        else:
            return 'Error'


class WhatsappDeProspectosConVendedoresInactivosFilter(admin.SimpleListFilter):
    title = 'Vendedores inactivos'
    parameter_name = 'vendedores-inactivos'

    def lookups(self, request, model_admin):
        return [('True', 'Con vendedores inactivos')]

    def queryset(self, request, queryset):
        if self.value() == 'True':
            return queryset.con_vendedores_inactivos()
        return queryset


class MensajesWhatsappAdmin(admin.ModelAdmin):
    form = MensajesWhatsappAdminForm
    list_display = ('telefono', 'mensaje', 'fecha', 'estado', 'emisor', "vendedor")
    date_hierarchy = 'fecha'
    list_filter = ('emisor', 'estado', VerificacionWhatsappListFilter, WhatsappDeProspectosConVendedoresInactivosFilter)

    def has_add_permission(self, request):
        return False

    def get_readonly_fields(self, request, obj=None):
        return 'telefono', 'mensaje', 'fecha', 'estado', 'emisor', 'eliminado'

    def vendedor(self, instance):
        responsable = instance.responsable()

        if responsable is None:
            return "---"
        else:
            url = reverse("admin:vendedores_vendedor_change", args=(responsable.id,))
            atributos = self._atributos_para_vendedor(responsable)
            return '<a href="%s" %s>%s</a>' % (url, atributos, responsable.full_name())

    vendedor.allow_tags = True
    vendedor.short_description = "Vendedor"

    def _atributos_para_vendedor(self, responsable):
        if responsable.esta_activo():
            atributos = 'style="color: green" title="Vendedor activo"'
        else:
            atributos = 'style="color: red" title="Vendedor inactivo"'
        return atributos


admin.site.register(MensajesWhatsapp, MensajesWhatsappAdmin)
