from datetime import timedelta
from django.utils import timezone
from django.db import models

from conversaciones.managers.enlace_con_meta_query_set import EnlaceConMetaQuerySet

class EnlaceConMeta(models.Model):
    _operador = models.ForeignKey('whatsapp.Operador', on_delete=models.CASCADE)
    _telefono_destinatario = models.CharField(max_length=15)
    _prospecto = models.ForeignKey('prospectos.Prospecto', on_delete=models.CASCADE)
    _fecha_creacion = models.DateTimeField(auto_now_add=True)
    _fecha_actualizacion = models.DateTimeField(auto_now=True)
    objects = EnlaceConMetaQuerySet.as_manager()

    @classmethod
    def nuevo(cls, operador, telefono_destinatario, prospecto):
        enlace = cls(_operador=operador, _telefono_destinatario=telefono_destinatario, _prospecto=prospecto)
        enlace.save()
        return enlace

    def obtener_operador(self):
        return self._operador

    def obtener_prospecto(self):
        return self._prospecto

    def tiene_fecha_ultima_actualizacion(self, fecha_ultima_actualizacion):
        return self._fecha_actualizacion == fecha_ultima_actualizacion

    def esta_vigente(self, dias_maximos):
        if self._existe_algun_enlace_con_fecha_de_actualizacion_posterior():
            return False
        elif self._su_ultima_actualizacion_es_anterior_a_una_cantidad_de(dias_maximos):
            return False
        else:
            return not (self._prospecto.finalizado or self._prospecto.vendido or not self._prospecto.tiene_vendedor())

    def _su_ultima_actualizacion_es_anterior_a_una_cantidad_de(self, dias_maximos):
        return (timezone.now() - self._fecha_actualizacion) > timedelta(days=dias_maximos)

    def _existe_algun_enlace_con_fecha_de_actualizacion_posterior(self):
        return EnlaceConMeta.objects.con_actualizacion_posterior_a(self._fecha_actualizacion).exists()
       