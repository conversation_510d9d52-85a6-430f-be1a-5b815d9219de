from django.db import models


class EnlaceConMetaQuerySet(models.QuerySet):

    def con(self, operador, telefono_destinatario, prospecto):
        return self.filter(_operador=operador, _telefono_destinatario=telefono_destinatario, _prospecto=prospecto)

    def entre_operador_destinatarios(self, operador, telefonos_destinatarios):
        return self.filter(_operador=operador, _telefono_destinatario__in=telefonos_destinatarios)

    def excluir_con_vendedor(self, vendedor):
        return self.exclude(_prospecto__vendedor=vendedor)

    def con_actualizacion_posterior_a(self, fecha_y_hora):
        return self.filter(_fecha_actualizacion__gt=fecha_y_hora)

    def con_prospecto(self, prospecto):
        return self.filter(_prospecto=prospecto)

    def desde_operadores(self, lista_de_operadores):
        return self.filter(_operador__in=lista_de_operadores)
