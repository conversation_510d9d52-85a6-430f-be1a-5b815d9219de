from django.db import models


class EnlaceConMetaQuerySet(models.QuerySet):

    def con(self, operador, telefono_destinatario, prospecto):
        return self.con_operador(
            operador=operador).con_prospecto(
            prospecto=prospecto).con_algun_telefono_destinatario(
            telefonos_destinatarios=[telefono_destinatario])

    def con_operador(self, operador):
        return self.filter(_operador=operador)

    def excluir_con_vendedor(self, vendedor):
        return self.exclude(_prospecto__vendedor=vendedor)

    def con_actualizacion_posterior_a(self, fecha_y_hora):
        return self.filter(_fecha_actualizacion__gt=fecha_y_hora)

    def con_prospecto(self, prospecto):
        return self.filter(_prospecto=prospecto)

    def desde_operadores(self, lista_de_operadores):
        return self.filter(_operador__in=lista_de_operadores)

    def con_algun_telefono_destinatario(self, telefonos_destinatarios):
        return self.filter(_telefono_destinatario__in=telefonos_destinatarios)

    def ordenar_del_mas_recientemente_actualizado_al_mas_antiguo(self):
        return self.order_by('-_fecha_actualizacion')
