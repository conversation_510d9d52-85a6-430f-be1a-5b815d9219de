// SOLO CAMBIO DE ESTADO

{
  "object": "whatsapp_business_account",
  "entry": [
    {
      //Id identificador cuenta whatsapp business
      "id": "***************",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              //Numero humano por el cual salio
              "display_phone_number": "***********",
              //Id identificador numero de telefono en Meta
              "phone_number_id": "***************"
            },
            "statuses": [
              {
                "id": "wamid.********************************************************",
                "status": "sent",
                // fecha para el estado
                "timestamp": "**********",
                "recipient_id": "*************",
                // Informacion para la conversacion
                "conversation": {
                  "id": "f4e6e634e0f94459eae1b0d6e486cd02",
                  // Fecha en la cual la conversacion expira
                  "expiration_timestamp": "**********",
                  "origin": {
                    "type": "utility"
                  }
                },
                "pricing": {
                  "billable": true,
                  "pricing_model": "CBP",
                  "category": "utility"
                }
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}

//https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/components