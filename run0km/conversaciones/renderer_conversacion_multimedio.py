# coding=utf-8
from django.template.loader import render_to_string
from django.urls import reverse

from conversaciones.adapters import ConversacionMultimediaAdapter
from conversaciones.gestor import GestorDeConversaciones


class ConversacionMultimedioRender(object):
    def render_to_string_de_conversacion_element(self, vendedor, prospecto):
        return render_to_string(
            self._template_de_conversacion(),
            self._contexto_para_template_conversacion_unificada(prospecto, vendedor)
        )

    def render_to_string_de_conversacion_e_aviso_element(self, vendedor, conversacion):
        return render_to_string(
            self._template_de_conversacion(),
            self._contexto_para_template_conversacion_e_aviso(conversacion, vendedor)
        )

    # --- metodos privados ---

    def _template_de_conversacion(self):
        return 'conversacion-unificada.html'

    def _contexto_para_template_conversacion_unificada(self, prospecto, vendedor):
        gestor = GestorDeConversaciones.nuevo_para(vendedor)
        conversacion = gestor.conversacion_multimedia_para(prospecto)
        url_a_prospecto = self._url_para_prospecto(prospecto=prospecto)
        nombre = prospecto.obtener_nombre()
        tooltip = 'Click para ir al prospecto %s' % nombre
        mensajes_json = self._mensajes_de(conversacion)
        medio_seleccionado = self._medio_seleccionado(mensajes_json)
        contexto = {
            'id_prospecto': prospecto.id,
            'nombre_de_conversacion': nombre,
            'imagen_url_para_avatar': prospecto.avatar(),
            'texto_para_url_para_avatar': tooltip,
            'url_para_avatar': url_a_prospecto,
            'texto_para_url_de_nombre_de_conversacion': tooltip,
            'url_para_nombre_de_conversacion': url_a_prospecto,
            'whatsapp_chat': self._configuracion_whatsapp_para(gestor, prospecto, medio_seleccionado),
            'sms_chat': self._configuracion_sms_para(gestor, prospecto, medio_seleccionado),
            'mail_chat': self._configuracion_mail_para(gestor, prospecto, medio_seleccionado),
            'e_aviso_chat': self._configuracion_e_aviso_deshabilitada(),
            'mensajes': mensajes_json,
            'mensaje_disparador_de_conversacion': None,
            'datos_de_contacto_del_interesado': None
        }
        self._configurar_medio_por_defecto(contexto)
        self._configurar_si_puede_enviar_mensajes(contexto)
        return contexto

    def _contexto_para_template_conversacion_e_aviso(self, conversacion, vendedor):
        publicacion = conversacion.publicacion()
        url_a_publicacion = publicacion.url()
        nombre = 'Pregunta de {0}'.format(publicacion.nombre_de_lugar_de_publicacion())
        tooltip = 'Click para ir al prospecto %s' % nombre
        mensajes_json = self._mensajes_de(conversacion)
        pregunta = conversacion.mensaje_a_responder()
        from django.conf import settings
        contexto = {
            'id_prospecto': conversacion.id_mensaje_a_responder(),
            'nombre_de_conversacion': nombre,
            'imagen_url_para_avatar': settings.STATIC_URL + 'img/generic-user.png',
            'texto_para_url_para_avatar': tooltip,
            'url_para_avatar': url_a_publicacion,
            'texto_para_url_de_nombre_de_conversacion': tooltip,
            'url_para_nombre_de_conversacion': url_a_publicacion,
            'whatsapp_chat': self._configuracion_nula(),
            'sms_chat': self._configuracion_nula(),
            'mail_chat': self._configuracion_nula(),
            'e_aviso_chat': self._configuracion_e_aviso_habilitada_y_seleccionada(),
            'mensajes': mensajes_json,
            'mensaje_disparador_de_conversacion': MensajeDePublicacion(publicacion),
            'datos_de_contacto_del_interesado': self._datos_de_contacto_del_interesado(conversacion),
            'cantidad_de_respuestas_disponibles': pregunta.cantidad_de_respuestas_disponibles()
        }
        self._configurar_medio_por_defecto(contexto)
        self._configurar_si_puede_enviar_mensajes_para_eavisos(contexto, conversacion, pregunta)
        return contexto

    def _configurar_si_puede_enviar_mensajes_para_eavisos(self, contexto, conversacion, pregunta):
        e_aviso_habilitado = contexto['e_aviso_chat']['esta_habilitado']
        if e_aviso_habilitado:
            se_puede_responder = pregunta.se_puede_responder()
            contexto["puede_enviar_mensajes"] = se_puede_responder
            if se_puede_responder:
                contexto["puede_enviar_mensajes_motivo"] = ""
            else:
                contexto["puede_enviar_mensajes_motivo"] = "No quedan respuestas disponibles para esta pregunta"

    def _datos_de_contacto_del_interesado(self, conversacion):
        datos_de_contacto = conversacion.datos_de_contacto_del_interesado()
        datos_de_contacto_lista = []
        if datos_de_contacto.telefono():
            datos_de_contacto_lista.append('Teléfono: {0}'.format(datos_de_contacto.telefono()))
        if datos_de_contacto.whatsapp():
            datos_de_contacto_lista.append('Whatsapp: {0}'.format(datos_de_contacto.whatsapp()))
        if datos_de_contacto.email():
            datos_de_contacto_lista.append('Email: {0}'.format(datos_de_contacto.email()))
        if datos_de_contacto.nombre():
            datos_de_contacto_lista.append('Nombre: {0}'.format(datos_de_contacto.nombre()))
        if len(datos_de_contacto_lista) > 0:
            return datos_de_contacto_lista
        return None

    def _url_para_prospecto(self, prospecto):
        return reverse(viewname='prospecto', kwargs={'pk': prospecto.pk})

    def _mensajes_de(self, conversacion):
        adapter = ConversacionMultimediaAdapter()
        return adapter.adapt_this(conversacion)

    def _configuracion_whatsapp_para(self, gestor, prospecto, medio_seleccionado):
        if not gestor.puede_enviar_via_whatspp():
            esta_habilitado = False
            motivo = 'No tiene el servicio de whatsapp habilitado'
        elif prospecto.telefono_para_whatsapp() is None:
            esta_habilitado = False
            motivo = 'El prospecto no tiene teléfono con whatsapp'
        else:
            esta_habilitado = True
            motivo = ''

        return {
            'esta_habilitado': esta_habilitado,
            'motivo_deshabilitado': motivo,
            'esta_seleccionado': esta_habilitado and medio_seleccionado == ConversacionMultimediaAdapter.WHATSAPP,
        }

    def _configuracion_sms_para(self, gestor, prospecto, medio_seleccionado):
        if not gestor.puede_enviar_via_sms():
            esta_habilitado = False
            motivo = 'No tiene el servicio de SMS habilitado'
        elif not prospecto.celular():
            esta_habilitado = False
            motivo = 'El prospecto no tiene teléfono movil'
        else:
            esta_habilitado = True
            motivo = ''

        return {
            'esta_habilitado': esta_habilitado,
            'motivo_deshabilitado': motivo,
            'esta_seleccionado': esta_habilitado and medio_seleccionado == ConversacionMultimediaAdapter.SMS,
        }

    def _configuracion_mail_para(self, gestor, prospecto, medio_seleccionado):
        if not gestor.puede_enviar_via_email():
            esta_habilitado = False
            motivo = 'Su supervisor no tiene configurado la cuenta de email'
        elif not prospecto.obtener_email_activo():
            esta_habilitado = False
            motivo = 'El prospecto no tiene email definido.'
        else:
            esta_habilitado = True
            motivo = ''

        return {
            'esta_habilitado': esta_habilitado,
            'motivo_deshabilitado': motivo,
            'esta_seleccionado': esta_habilitado and medio_seleccionado == ConversacionMultimediaAdapter.EMAIL,
        }

    def _medio_seleccionado(self, mensajes_json):
        cantidad = len(mensajes_json)
        if cantidad > 0:
            mensaje = mensajes_json[cantidad - 1]
            return mensaje['medio']
        else:
            return ConversacionMultimediaAdapter.WHATSAPP

    def _configurar_medio_por_defecto(self, contexto):
        """
            Si ningun medio esta seleccionado, busca algun medio habilitado para marcarlo como seleccionado
            por defecto, con el siguiente orden de prioridad: whatsapp, sms, email

            TODO: ver si podemos reutilizar el concepto de Medio, y asi quitar este case.

        """
        whatsapp = contexto['whatsapp_chat']
        sms = contexto['sms_chat']
        email = contexto['mail_chat']

        if not whatsapp['esta_seleccionado'] and not sms['esta_seleccionado'] and not email['esta_seleccionado']:
            if whatsapp['esta_habilitado']:
                whatsapp['esta_seleccionado'] = True
            elif email['esta_habilitado']:
                email['esta_seleccionado'] = True
            elif sms['esta_habilitado']:
                sms['esta_seleccionado'] = True
            else:
                pass

    def _configurar_si_puede_enviar_mensajes(self, contexto):
        whatsapp_habilitado = contexto['whatsapp_chat']['esta_habilitado']
        sms_habilitado = contexto['sms_chat']['esta_habilitado']
        mail_habilitado = contexto['mail_chat']['esta_habilitado']
        e_aviso_habilitado = contexto['e_aviso_chat']['esta_habilitado']
        puede_enviar_mensajes = whatsapp_habilitado or sms_habilitado or mail_habilitado or e_aviso_habilitado
        contexto['puede_enviar_mensajes'] = puede_enviar_mensajes
        if not puede_enviar_mensajes:
            contexto['puede_enviar_mensajes_motivo'] = 'No tiene servicios habilitados para este prospecto'
        else:
            contexto['puede_enviar_mensajes_motivo'] = ''

    def _configuracion_nula(self):
        return {
            'esta_habilitado': False,
            'motivo_deshabilitado': 'No es posible contactar por este medio a este contacto',
            'esta_seleccionado': False,
        }

    def _configuracion_e_aviso_deshabilitada(self):
        return {
            'esta_habilitado': False,
            'motivo_deshabilitado': 'No hay un EAviso asociado a esta charla',
            'esta_seleccionado': False,
        }

    def _configuracion_e_aviso_habilitada_y_seleccionada(self):
        return {
            'esta_habilitado': True,
            'motivo_deshabilitado': '',
            'esta_seleccionado': True,
        }


class MensajeDePublicacion(object):
    def __init__(self, publicacion):
        self._publicacion = publicacion

    def publicacion(self):
        return self._publicacion

