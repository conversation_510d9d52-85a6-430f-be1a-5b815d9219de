from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
from testing.base import BaseFixturedTest
from whatsapp.cover.fake_whatsapp_cover_api import FakeWhatsappCoverApi
from whatsapp.cover.whatsapp_cover_api import WhatsappCoverApi


class WhatsappAPIRequesterTest(BaseFixturedTest):
    # Clase movida, no es de mi autoria
    # Refactor pendiente

    def test_informacion_de_envio(self):
        requester = FakeWhatsappCoverApi()
        p1 = self.fixture['p_1']
        p1.nombre = 'np1'
        p1.save()
        p2 = self.fixture['p_2']
        p2.nombre = 'np2'
        p2.save()
        concesionaria = p1.vendedor.obtener_concesionaria()
        concesionaria.token_whatsapp = 'El Token!'
        concesionaria.save()
        mj1 = MensajesWhatsapp.nuevo_mensaje(prospecto=p1, mensaje='Mensaje 1', telefono='12345')
        mj2 = MensajesWhatsapp.nuevo_mensaje(prospecto=p2, mensaje='Mensaje 2', telefono='54321')
        mjs = MensajesWhatsapp.objects.mensajes_de_concesionaria_para_enviar(concesionaria).order_by('fecha')
        info = requester._informacion_de_pedido_de_envio(concesionaria.token_whatsapp, mjs, [], codigo='1')

        self.assertIn('Group', info)
        self.assertEqual(info['Group'], '')
        self.assertIn('Messages', info)
        self.assertIn('Name', info)
        self.assertIn('ExtCode', info)
        self.assertIn('Token', info)
        self.assertEqual(info['Token'], concesionaria.token_whatsapp)
        self.assertIn('StartDate', info)
        self.assertIn('EndDate', info)
        self.assertIn('StartTime', info)
        self.assertIn('EndTime', info)

        info_p1 = info['Messages'][0]
        self.assertIn('Email', info_p1)
        self.assertEqual(info_p1['Email'], '')
        self.assertIn('ExtCode', info_p1)
        self.assertEqual(info_p1['ExtCode'], WhatsappCoverApi.WHATSAPP_PREFIX + str(mj1.id))
        self.assertIn('Message', info_p1)
        self.assertEqual(info_p1['Message'], mj1.mensaje)
        self.assertIn('Name', info_p1)
        self.assertEqual(info_p1['Name'], p1.nombre)
        self.assertIn('Phone', info_p1)
        self.assertEqual(info_p1['Phone'], mj1.telefono)

        info_p2 = info['Messages'][1]
        self.assertIn('Email', info_p2)
        self.assertEqual(info_p2['Email'], '')
        self.assertIn('ExtCode', info_p2)
        self.assertEqual(info_p2['ExtCode'], WhatsappCoverApi.WHATSAPP_PREFIX + str(mj2.id))
        self.assertIn('Message', info_p2)
        self.assertEqual(info_p2['Message'], mj2.mensaje)
        self.assertIn('Name', info_p2)
        self.assertEqual(info_p2['Name'], p2.nombre)
        self.assertIn('Phone', info_p2)
        self.assertEqual(info_p2['Phone'], mj2.telefono)