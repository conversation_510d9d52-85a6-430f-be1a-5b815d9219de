from django.test import override_settings

from notificaciones.tests.soporte import NotificacionesTestHelper
from occ.sms_estrategias_de_envio import DeliverySMS
from testing.base import BaseFixturedTest


@override_settings(SMSCOVER_SMS_SENDER_CLASS='lib.smscover.SMSMockSender')
class ConversacionMultimedioTest(BaseFixturedTest):

    def setUp(self):
        super().setUp()
        DeliverySMS.reset_instance()
        self.vendedor_uno = self.fixture['vend_1']
        self.vendedor_dos = self.fixture['vend_2']
        self.prospecto_de_vendedor_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, telefono='48764498', es_telefono_movil=True, email='<EMAIL>')
        self.conversacion_de_uno = self.creador_de_contexto.crear_nueva_conversacion_de_whatsapp_para(
            prospecto=self.prospecto_de_vendedor_uno, mensaje='hello one')
        self._notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)

    def _assert_envio_de_email_por_cliente(self, cliente_gmail_mock):
        # TODO: por ahora no valido el contendo del envio
        cliente_gmail_mock.assert_called_once()

