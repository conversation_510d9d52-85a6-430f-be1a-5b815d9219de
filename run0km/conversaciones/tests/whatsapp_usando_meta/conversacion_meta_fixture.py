from datetime import timedelta

from django.utils import timezone

from conversaciones.gestor import GestorDeConversaciones
from conversaciones.medios import MedioWhatsapp
from conversaciones.meta.canal_de_comunicacion_via_meta import CanalDeComunicacionDeWhatsappViaMeta
from conversaciones.meta.notificador_de_conversaciones_meta_mock import NotificadorDeConversacionesMetaMock
from core.date_helper import DatetimeHelper
from notificaciones.tests.soporte import NotificacionesTestHelper
from testing.test_utils import reload_model
from whatsapp.meta.meta_changes_response import MetaChangesResponse
from whatsapp.meta.models.operador import Operador, GrupoOperadores
from whatsapp.meta.services.whatsapp_meta_service_mock import WhatsappMetaServiceMock


class ConversacionMetaFixture(object):
    def __init__(self, fixture, creador_de_contexto):
        self._inicializar_servicios_base(creador_de_contexto, fixture)
        self._inicializar_staff()

        self._inicializar_servicios_de_meta()

        # Operadores
        self._inicializar_configuracion_operadores()

        self._inicializar_prospectos()

    def _inicializar_prospectos(self):
        self._telefono_uno = '48764549'
        self._telefono_dos = '1140583622'
        self._prospecto_de_vendedor_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self._vendedor_uno, telefono=self._telefono_uno, es_telefono_movil=True, email='<EMAIL>')
        self.creador_de_contexto.agregar_campo_extra_a(self._prospecto_de_vendedor_uno, "DNI", "35766008")
        self._prospecto_dos_de_vendedor_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self._vendedor_uno, telefono='48764499', es_telefono_movil=True,
            email='<EMAIL>')
        self.creador_de_contexto.agregar_campo_extra_a(self._prospecto_dos_de_vendedor_uno, "DNI", "44555666")
        self._prospecto_viejo_de_vendedor_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self._vendedor_uno, telefono='48764400', es_telefono_movil=True,
            email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=1, day=1),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=1, day=1))
        self._prospecto_nuevo_de_vendedor_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self._vendedor_uno, telefono='48764400', es_telefono_movil=True, email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=3, day=3),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=3, day=3))
        self._prospecto_de_vendedor_dos = self._crear_prospecto_vendedor_dos()

    def _inicializar_configuracion_operadores(self):
        self._operador = self.crear_operador()
        self.asignar_grupo_de_operadores_a([self._operador], self._vendedor_uno)
        self._gestor_de_conversaciones_del_vendedor_uno = GestorDeConversaciones.nuevo_para(
            rol=self._vendedor_uno, canal_via_meta=self._canal_via_meta, usa_meta=True)

    def _inicializar_staff(self):
        self._supervisor_uno = self.fixture['sup_1']
        self._vendedor_uno = self.fixture['vend_1']
        self._vendedor_dos = self.fixture['vend_2']

    def _inicializar_servicios_de_meta(self):
        self._servicio_de_meta = WhatsappMetaServiceMock.nuevo()
        self._notificador = NotificadorDeConversacionesMetaMock.nuevo()
        self._notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._canal_via_meta = CanalDeComunicacionDeWhatsappViaMeta.nuevo(
            servicio_de_meta=self._servicio_de_meta,
            notificador=self._notificador)
        self.configurar_al_servicio_de_meta_para_acepte_nuevas_conversaciones()
        self.configurar_al_servicio_de_meta_para_acepte_nuevos_mensajes(
            self.id_de_meta_del_mensaje_enviado_por_el_vendedor())
        self.habilitar_uso_de_whatsapp_para_vendedor_uno()
        self.habilitar_uso_de_whatsapp_para_vendedor_dos()

    def _inicializar_servicios_base(self, creador_de_contexto, fixture):
        self.creador_de_contexto = creador_de_contexto
        self._calendario = DatetimeHelper()
        self.fixture = fixture

    def supervisor_uno(self):
        return self._supervisor_uno

    def vendedor_uno(self):
        return self._vendedor_uno

    def vendedor_dos(self):
        return self._vendedor_dos

    def operador(self):
        return self._operador

    def telefono_uno(self):
        return self._telefono_uno

    def telefono_dos(self):
        return self._telefono_dos

    def prospecto_de_vendedor_uno(self):
        return self._prospecto_de_vendedor_uno

    def prospecto_dos_de_vendedor_uno(self):
        return self._prospecto_dos_de_vendedor_uno

    def prospecto_viejo_de_vendedor_uno(self):
        return self._prospecto_viejo_de_vendedor_uno

    def prospecto_nuevo_de_vendedor_uno(self):
        return self._prospecto_nuevo_de_vendedor_uno

    def prospecto_de_vendedor_dos(self):
        return self._prospecto_de_vendedor_dos

    def asignar_grupo_de_operadores_a(self, operadores, vendedor):
        grupo_de_operadores = GrupoOperadores.nuevo_con(nombre='grupo_uno', operadores=operadores)
        vendedor.obtener_concesionaria().agregar_grupo_de_operadores(grupo_de_operador=grupo_de_operadores)

    def crear_operador(self, activo=True, tipo=None):
        operador = Operador.objects.create(
            id_operador="458578827350095",
            nombre="Test CRM Uno",
            activo=activo,
            tipo=tipo or Operador.SALIENTE)
        operador.agregar_template(
            nombre="saludo",
            contenido="Hola! Vi que te interesa obtener información sobre el Fiat. Te puedo hablar ahora?")
        return operador

    def calendario(self):
        return self._calendario

    def servicio_de_meta(self):
        return self._servicio_de_meta

    def notificador(self):
        return self._notificador

    def canal_via_meta(self):
        return self._canal_via_meta

    def notificaciones_helper(self):
        return self._notificaciones_helper

    def enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno(self):
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_de_vendedor_uno(), texto=self.texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

    def enviar_mensaje_whatsapp_a_prospecto_dos_de_vendedor_uno(self):
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_dos_de_vendedor_uno(), texto=self.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

    def canal_via_meta_actualice(self, notificacion_de_cambio_de_estado):
        self.canal_via_meta().actualizar_desde(notificacion_de_cambio_de_estado)

    def texto_enviado_por_el_vendedor(self):
        return 'Hello client!'

    def id_de_meta_del_mensaje_template(self):
        return 'wamid.HBgNNTQ5MTE0MDU4MzYyMxUCABEYEjMwQzk0RDBBRTEwNjdEOTRCNAA='

    def id_de_meta_del_mensaje_enviado_por_el_vendedor(self):
        return "wamid.HBgNNTQ5MTEyMjM2ODUxNRUCABEYEjVGNjZEQjlDMzZFQkE3NDhENwA="

    def id_de_mensaje_en_meta_dos(self):
        return 'wamid.HBgNNTQ5MTE0MDU4MzYyMxUCABEYEkI2MTgwMDE5Mzg0OEQ0NTRFRAA='

    def conversation_id(self):
        return 'f4e6e634e0f94459eae1b0d6e486cdaa'

    def notificacion_de_cambio_de_estado_a(self, id_de_mensaje_en_meta, status, telefono=None,
                                           conversation_id=None,
                                           conversation_expiration_timestamp=None):
        return MetaChangesResponse.new_example_status_change_to(
            id_meta_message=id_de_mensaje_en_meta,
            status=status,
            phone=telefono,
            conversation_id=conversation_id,
            conversation_expiration_timestamp=conversation_expiration_timestamp)

    def respuesta_del_usuario_a_un_mensaje(self, numero_de_telefono_del_remitente, operator_meta=None):
        return MetaChangesResponse.new_from({
            "marca": "Marca Blanca",
            "data": {
                "variable1": "valor1",
                "variable2": "valor2"
            },
            "telefono": numero_de_telefono_del_remitente,
            "operator_name": None,
            "marca_asociada_al_operador": None,
            "campania": "generica-sms",
            "mensaje": self.texto_entrante_de_prospecto(),
            "nombre": "Maximiliano",
            "operator_meta": operator_meta
        })

    def iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion(self):
        conversation_id = 'f4e6e634e0f94459eae1b0d6e486cdaa'
        fecha_de_expiracion = timezone.now().replace(microsecond=0) + timedelta(days=1)
        self.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        id_meta = self.id_de_meta_del_mensaje_template()
        timestamp = fecha_de_expiracion.timestamp()
        notificacion_de_cambio_de_estado = self.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta, status='sent',
            telefono='15551496593', conversation_id=conversation_id,
            conversation_expiration_timestamp=timestamp)
        return notificacion_de_cambio_de_estado

    def crear_gestor_para_vendedor_uno(self):
        return GestorDeConversaciones.nuevo_para(
            rol=self._vendedor_uno, canal_via_meta=self._canal_via_meta, usa_meta=True)

    def crear_gestor_para_vendedor_dos(self):
        return GestorDeConversaciones.nuevo_para(
            rol=self._vendedor_dos, canal_via_meta=self._canal_via_meta, usa_meta=True)

    def _crear_prospecto_vendedor_dos(self):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self._vendedor_dos, telefono='', es_telefono_movil=True, email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=2, day=2),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=4, day=4))
        prospecto.telefono = self._telefono_uno
        prospecto.save()
        return reload_model(prospecto)

    def texto_entrante_de_prospecto(self):
        return 'Hola! Esto es una prueba de mensaje'

    def configurar_al_servicio_de_meta_para_acepte_nuevas_conversaciones(self, id_de_mensaje_en_meta=None):
        self.servicio_de_meta().cuando_inicia_una_conversacion_con_template_hace(lambda: {
            'messaging_product': 'whatsapp', 'contacts': [{'input': '1140583623', 'wa_id': '5491140583623'}],
            'messages': [
                {'id': id_de_mensaje_en_meta or self.id_de_meta_del_mensaje_template(),
                 'message_status': 'accepted'}]
        })

    def configurar_al_servicio_de_meta_para_acepte_nuevos_mensajes(self, id_de_mensaje_en_meta=None):
        self.servicio_de_meta().cuando_se_envia_un_mensaje_hace(lambda: {
            'messaging_product': 'whatsapp', 'contacts': [{'input': '1140583623', 'wa_id': '5491140583623'}],
            'messages': [
                {'id': id_de_mensaje_en_meta or self.id_de_meta_del_mensaje_template()}]
        })

    def habilitar_uso_de_whatsapp_para_vendedor_uno(self):
        NotificacionesTestHelper.nuevo_para(self).habilitar_whatsapp_para(
            self.vendedor_uno())

    def habilitar_uso_de_whatsapp_para_vendedor_dos(self):
        NotificacionesTestHelper.nuevo_para(self).habilitar_whatsapp_para(
            self.vendedor_dos())

    def inicializar_notificaciones_test_helper(self, x):
        return NotificacionesTestHelper.nuevo_para(x)
