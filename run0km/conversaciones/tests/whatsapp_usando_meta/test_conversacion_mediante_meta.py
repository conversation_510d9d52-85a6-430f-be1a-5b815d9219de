from datetime import timed<PERSON><PERSON>

from django.core.exceptions import ValidationError
from django.utils import timezone
from freezegun import freeze_time

from conversaciones.gestor import GestorDeConversaciones
from conversaciones.medios import MedioWhatsapp
from conversaciones.tests.whatsapp_usando_meta.conversacion_meta_fixture import ConversacionMetaFixture
from lib.api_client import ClientConnectionError
from notificaciones.tests.soporte import NotificacionesTestHelper
from testing.base import BaseFixturedTest
from whatsapp.meta.models.message_in_meta import MessageInMeta
from whatsapp.meta.models.operador import Operador, GrupoOperadores


class TestConversacionMedianteMeta(BaseFixturedTest):
    """
    ---22/05/2025---
    Si el operador que llega en la respuesta no existe, debemos manejar el error (ya está implementada la solución, plantear test, hacerlo fallar)
    Si creo una nueva conversación debería tener el operador que me llegó en la notificación DONE
    Al buscar el mensaje incluir el operador DONE
    """

    def setUp(self):
        super().setUp()
        self._conversacion_meta_fixture = ConversacionMetaFixture(self.fixture, self.creador_de_contexto)

    def test_cuando_no_existe_una_conversacion_en_meta_se_inicia_con_el_primer_mensaje(self):
        # Dado - En el SetUp

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.servicio_de_meta().a_iniciado_una_conversacion_con(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            marca=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_marca().nombre(),
            dni=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_valor_campo_extra('DNI', ''),
            id_operador=self._conversacion_meta_fixture.operador().obtener_id_operador(),
            nombre_template=self._conversacion_meta_fixture.operador().template_por_defecto().obtener_nombre(),
            url_notificacion=self._conversacion_meta_fixture.canal_via_meta().obtener_url_de_notificaciones_de_estado())
        )

    def test_al_iniciar_una_conversacion_en_meta_se_crea_un_mensaje_template_con_estado_aceptado(self):
        # Dado - En el SetUp

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertTrue(
            self._conversacion_meta_fixture.canal_via_meta().tiene_un_mensaje_con_el_id_de_meta(
                self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()))
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_template())
        self.assertTrue(mensaje.has_accepted_status())
        self.assertTrue(mensaje.has_template_with_name(
            self._conversacion_meta_fixture.operador().template_por_defecto().obtener_nombre()))

    def test_cuando_falla_el_inicio_de_la_conversacion_de_meta_no_se_crea_conversacion_de_meta(self):
        # TODO Definir si no vamos a crear una conversacion o le asignamos un estado "Erronea o Fallida"
        # Dado
        self._conversacion_meta_fixture.servicio_de_meta().cuando_inicia_una_conversacion_con_template_hace(
            self.simular_respuesta_peticion_erronea)

        # Cuando / Entonces
        self.assertRaisesMessage(
            ValueError,
            "No es posible enviar el mensaje",
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno)
        self.assertFalse(
            self._conversacion_meta_fixture.canal_via_meta().tiene_un_mensaje_con_el_id_de_meta(
                self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()))

    def test_si_el_prospecto_aun_no_acepto_la_conversacion_en_meta_el_mensaje_del_vendedor_queda_en_espera(self):
        # ACLARACION: si un vendedor envia un mensaje template, y el prospecto no responde, los mensajes deben quedar
        # en espera de poder ser enviados
        # Dado - En el SetUp

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.canal_via_meta().tiene_un_mensaje_en_espera_con_texto(
            self._conversacion_meta_fixture.texto_enviado_por_el_vendedor()))

    def test_cuando_se_envia_un_mensaje_template_accepted_se_actualiza_el_estado(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        id_meta = self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta, status='sent',
            telefono='15551496593', conversation_id=self._conversacion_meta_fixture.conversation_id(),
            conversation_expiration_timestamp="1740660300")

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        # Entonces
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_template())
        self.assertTrue(mensaje.has_sent_status())

    def test_al_actualizar_el_estado_de_mensajes_inexistentes_lanza_un_error(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        id_de_meta_inexistente = 'wamid.BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB='
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_de_meta_inexistente, status='sent',
            telefono='15551496593', conversation_id=self._conversacion_meta_fixture.conversation_id(),
            conversation_expiration_timestamp='1740660300.0')

        # Cuando / Entonces
        self.assertRaises(ValueError, self._conversacion_meta_fixture.canal_via_meta().actualizar_desde,
                          notificacion_de_cambio_de_estado)
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_template())
        self.assertTrue(mensaje.has_accepted_status())

    def test_al_recibir_varias_notificaciones_independientes_con_cambio_de_estado_se_actualizan_los_estados_de_los_mensajes(
            self):
        # Dado

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        id_meta = self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta, status='sent',
            telefono='15551496593', conversation_id=self._conversacion_meta_fixture.conversation_id(),
            conversation_expiration_timestamp='1740660300')

        self._conversacion_meta_fixture.configurar_al_servicio_de_meta_para_acepte_nuevas_conversaciones(
            id_de_mensaje_en_meta=self._conversacion_meta_fixture.id_de_mensaje_en_meta_dos())

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_dos_de_vendedor_uno()
        notificacion_de_cambio_de_estado_prospecto_dos = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=self._conversacion_meta_fixture.id_de_mensaje_en_meta_dos(), status='sent',
            telefono=self._conversacion_meta_fixture.telefono_dos(), conversation_id=self._conversacion_meta_fixture.conversation_id(),
            conversation_expiration_timestamp='1740660300')

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado_prospecto_dos)

        # Entonces
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_template())
        self.assertTrue(mensaje.has_sent_status())
        mensaje_dos = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_mensaje_en_meta_dos())
        self.assertTrue(mensaje_dos.has_sent_status())

    def test_al_recibir_mas_de_un_cambio_de_estado_se_queda_con_el_mas_avanzado(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        id_meta_segun_template = self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()
        notificacion_de_cambio_de_estado_delivered = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta_segun_template, status='delivered')
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado_delivered)
        id_meta_segun_conversacion = self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()
        notificacion_de_cambio_de_estado_sent = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta_segun_conversacion, status='sent')

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado_sent)

        # Entonces
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_template())
        self.assertTrue(mensaje.has_delivered_status())

    def test_al_recibir_cambios_de_estados_de_mensajes_se_actualiza_la_conversacion(
            self):
        # La zona horaria es UTC 00:00
        # Dado
        conversation_id = self._conversacion_meta_fixture.conversation_id()
        fecha_de_expiracion = timezone.now().replace(microsecond=0)

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        id_meta = self._conversacion_meta_fixture.id_de_meta_del_mensaje_template()
        timestamp = fecha_de_expiracion.timestamp()
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.notificacion_de_cambio_de_estado_a(
            id_de_mensaje_en_meta=id_meta, status='sent',
            telefono='15551496593', conversation_id=conversation_id,
            conversation_expiration_timestamp=timestamp)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        # Entonces
        conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            operador=self._conversacion_meta_fixture.operador())
        self.assertEqual(timezone.localtime(fecha_de_expiracion),
                         timezone.localtime(conversacion.expiration_datetime()))
        self.assertEqual(conversation_id, conversacion.meta_id())

    def test_al_recibir_una_respuesta_del_destinatario_se_genera_un_mensaje_de_respuesta_de_meta(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Averiguar el formato con el cual llega la respuesta y meterlo acá
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces
        conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
            numero_de_telefono_del_remitente, self._conversacion_meta_fixture.operador())
        self.assertEqual(len(conversacion.message_received()), 1)
        mensaje_recibido = conversacion.message_received()[0]
        self.assertEqual(mensaje_recibido.text(), self._conversacion_meta_fixture.texto_entrante_de_prospecto())
        # Assertar el resto de la información recibida, la fecha del mensaje recibido,
        # manejarlo de la misma forma que la notificación

    def test_al_abrir_la_ventana_de_conversacion_se_envia_al_servicio_de_meta_los_mensajes_pendientes(self):
        # La zona horaria es UTC 00:00
        # Dado
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.servicio_de_meta().a_enviado_un_mensaje_a(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            marca=None,
            dni=None,
            id_operador=self._conversacion_meta_fixture.operador().obtener_id_operador(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            url_notificacion=self._conversacion_meta_fixture.canal_via_meta().obtener_url_de_notificaciones_de_estado())
        )

    def test_al_abrir_la_ventana_de_conversacion_los_mensajes_dejan_de_estar_pendiente(self):
        # La zona horaria es UTC 00:00
        # Dado
        notificacion_de_cambio_de_estado =self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces
        conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
            telefono_destinatario=self._conversacion_meta_fixture.telefono_uno(),
            operador=self._conversacion_meta_fixture.operador())
        self.assertTrue(conversacion.is_open())
        self.assertEqual(len(conversacion.pending_messages()), 0)
        mensaje = self._conversacion_meta_fixture.canal_via_meta().mensaje_con_el_id_de_meta(
            self._conversacion_meta_fixture.id_de_meta_del_mensaje_enviado_por_el_vendedor())
        self.assertTrue(mensaje.has_accepted_status())

    def test_la_ventana_de_conversacion_se_cierra_luego_de_24_horas_de_la_ultima_respuesta(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
            numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

            notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
                numero_de_telefono_del_remitente,
                operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

            # Cuando
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Entonces
            conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
                telefono_destinatario=numero_de_telefono_del_remitente,
                operador=self._conversacion_meta_fixture.operador())
            self.assertFalse(conversacion.is_open())

    def test_cuando_falla_el_envio_de_los_mensajes_pendientes_a_meta_continuan_pendientes(self):
        # La zona horaria es UTC 00:00
        # Dado
        self._conversacion_meta_fixture.servicio_de_meta().cuando_se_envia_un_mensaje_hace(
            self.simular_respuesta_peticion_erronea)
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        # Entonces
        conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            operador=self._conversacion_meta_fixture.operador())
        self.assertEqual(len(conversacion.pending_messages()), 1)

    def test_al_tener_la_ventana_abierta_no_se_vuelve_a_enviar_el_mensaje_template(self):
        # Dado
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertEqual(self._conversacion_meta_fixture.servicio_de_meta().cantidad_conversaciones_iniciadas_con(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            marca=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_marca().nombre(),
            dni=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_valor_campo_extra('DNI', ''),
            id_operador=self._conversacion_meta_fixture.operador().obtener_id_operador(),
            nombre_template=self._conversacion_meta_fixture.operador().template_por_defecto().obtener_nombre(),
            url_notificacion=self._conversacion_meta_fixture.canal_via_meta().obtener_url_de_notificaciones_de_estado()),
            1
        )

    def test_luego_de_24_horas_de_la_ultima_respuesta_si_se_envia_un_mensaje_se_envia_un_template(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
            numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

            notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
                numero_de_telefono_del_remitente,
                operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Cuando
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            # Entonces
            self.assertEqual(self._conversacion_meta_fixture.servicio_de_meta().cantidad_conversaciones_iniciadas_con(
                telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
                marca=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_marca().nombre(),
                dni=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_valor_campo_extra('DNI', ''),
                id_operador=self._conversacion_meta_fixture.operador().obtener_id_operador(),
                nombre_template=self._conversacion_meta_fixture.operador().template_por_defecto().obtener_nombre(),
                url_notificacion=self._conversacion_meta_fixture.canal_via_meta().obtener_url_de_notificaciones_de_estado()),
                2
            )

    def test_luego_de_24_horas_de_la_ultima_respuesta_los_mensajes_enviados_quedan_en_pendiente(self):
        # TODO en realidad este esta cubierto pero por alguna razon quedan en failed los mensajes
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
            numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

            notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
                numero_de_telefono_del_remitente,
                operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Cuando
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            # Entonces
            self.assertTrue(self._conversacion_meta_fixture.canal_via_meta().tiene_un_mensaje_en_espera_con_texto(
                self._conversacion_meta_fixture.texto_enviado_por_el_vendedor()))

    def test_al_tener_la_ventana_abierta_el_mensaje_del_vendedor_no_queda_pendiente_de_que_se_envie(self):
        # Dado
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertFalse(self._conversacion_meta_fixture.canal_via_meta().tiene_un_mensaje_en_espera_con_texto(
            self._conversacion_meta_fixture.texto_enviado_por_el_vendedor()))

    def test_al_tener_un_mensaje_en_espera_no_se_envian_los_mensajes_templates_hasta_que_expire(self):
        # Caso: Una persona escribe muchos mensajes en una conversacion no iniciada, no enviar templates
        # bajo esa misma conversacion.

        # Dado
        notificacion_de_cambio_de_estado = self._conversacion_meta_fixture.iniciar_conversacion_y_obtener_notificacion_con_expiracion_de_la_conversacion()

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_de_cambio_de_estado)

        # Entonces
        self.assertEqual(self._conversacion_meta_fixture.servicio_de_meta().cantidad_conversaciones_iniciadas_con(
            telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
            marca=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_marca().nombre(),
            dni=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().obtener_valor_campo_extra('DNI', ''),
            id_operador=self._conversacion_meta_fixture.operador().obtener_id_operador(),
            nombre_template=self._conversacion_meta_fixture.operador().template_por_defecto().obtener_nombre(),
            url_notificacion=self._conversacion_meta_fixture.canal_via_meta().obtener_url_de_notificaciones_de_estado()),
            1)

        self.assertEqual(self._conversacion_meta_fixture.canal_via_meta().cantidad_de_mensajes_de_espera_con_texto(
            self._conversacion_meta_fixture.texto_enviado_por_el_vendedor()), 4)

    def test_al_recibir_una_de_respuesta_del_prospecto_se_envia_al_vendedor_la_notificacion_de_abrir_chat(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente,
            operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self.assertTrue(
            self._conversacion_meta_fixture.notificador().ha_recibido_una_notificacion_de_nueva_respuesta_para(
                prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno()))

    def test_al_no_tener_una_notificacion_de_estado_de_inicio_de_conversacion_se_utiliza_el_created_at_para_verificar_si_la_conversacion_esta_expirada(
            self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            # Cuando
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Entonces
            conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
                telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
                operador=self._conversacion_meta_fixture.operador())
            self.assertTrue(conversacion.is_expired())

    def test_pasadas_mas_de_24_horas_del_envio_de_template_un_prospecto_envia_un_mensaje_la_conversacion_no_esta_expirada(
            self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            tiempo_freezado.tick(timedelta(hours=25) + timedelta(seconds=1))

            numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()

            notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
                numero_de_telefono_del_remitente,
                operator_meta=self._conversacion_meta_fixture.operador().obtener_id_operador())

            # Cuando
            self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

            # Entonces
            conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
                telefono_destinatario=self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
                operador=self._conversacion_meta_fixture.operador())
            self.assertTrue(conversacion.is_open())
            self.assertFalse(conversacion.is_expired())

    def test_al_recibir_una_respuesta_sin_una_conversacion_existente_crea_una_nueva_con_el_operador_recibido(self):
        # Dado

        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            self._conversacion_meta_fixture.telefono_uno(),
             meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces

        self.assertEqual(self._conversacion_meta_fixture.canal_via_meta().cantidad_de_conversaciones_con_operador(
            self._conversacion_meta_fixture.operador()), 1)

    def test_al_recibir_una_respuesta_del_cliente_la_asignamos_a_la_conversacion_que_contenga_el_telefono_destinatario_y_el_operador(
            self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        operador_dos = Operador.objects.create(
            id_operador="458578827350096",
            nombre="Test CRM Dos",
            activo=True,
            tipo=Operador.SALIENTE)
        operador_dos.agregar_template(
            nombre="saludo",
            contenido="Hola! Vi que te interesa obtener información sobre el Fiat. Te puedo hablar ahora?")
        prospecto_de_vendedor_dos = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=(
                self._conversacion_meta_fixture.vendedor_dos()), telefono='', es_telefono_movil=True,
            email='<EMAIL>')
        prospecto_de_vendedor_dos.telefono = self._conversacion_meta_fixture.telefono_uno()
        prospecto_de_vendedor_dos.save()
        grupo_de_operadores = GrupoOperadores.nuevo_con(nombre='grupo_dos', operadores=[operador_dos])
        self._conversacion_meta_fixture.vendedor_dos().obtener_concesionaria().agregar_grupo_de_operadores(
            grupo_de_operador=grupo_de_operadores)
        self._gestor_de_conversaciones_del_vendedor_dos = GestorDeConversaciones.nuevo_para(
            rol=(
                self._conversacion_meta_fixture.vendedor_dos()),
            canal_via_meta=self._conversacion_meta_fixture.canal_via_meta(), usa_meta=True)
        NotificacionesTestHelper.nuevo_para(self).habilitar_whatsapp_para(
            self._conversacion_meta_fixture.vendedor_dos())
        self._gestor_de_conversaciones_del_vendedor_dos.enviar_a(
            prospecto_de_vendedor_dos, texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            self._conversacion_meta_fixture.telefono_uno(),
             meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta_actualice(notificacion_con_respuesta_del_destinatario)

        # Entonces
        mensaje_uno = MessageInMeta.objects.with_operator(self._conversacion_meta_fixture.operador()).first()
        mensaje_dos = MessageInMeta.objects.with_operator(operador_dos).first()
        conversacion_uno = mensaje_uno.conversation()
        conversacion_dos = mensaje_dos.conversation()
        self.assertEqual(conversacion_uno.message_received().count(), 1)
        self.assertEqual(conversacion_dos.message_received().count(), 0)

    def test_al_recibir_una_respuesta_con_un_operador_inexistente_lanza_error(self):
        # Dado

        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            self._conversacion_meta_fixture.telefono_uno(),  operator_meta='erroneo')

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No existen remitentes para asignar respuesta',
                                 self._conversacion_meta_fixture.canal_via_meta_actualice,
                                 notificacion_con_respuesta_del_destinatario)

    def xxtest_se_actualiza_la_info_del_prospecto_segun_lo_que_venga_en_la_respuesta(self):
        pass

    def xxtest_un_mensaje_recibido_de_un_prospecto_repetido_con_operador_se_asigna_correctamente(self):
        pass

    def xxtest_al_responder_a_un_prospecto_que_respondio_un_mensaje_se_actualiza_la_conversacion_con_info_de_meta(self):
        pass

    def xxtest_un_mensaje_recibido_de_un_prospecto_repetido_con_marca_igual_se_respeta_la_marca(self):
        pass

    def simular_respuesta_peticion_erronea(self):
        raise ClientConnectionError("NotFound - Not Found", None, None)
