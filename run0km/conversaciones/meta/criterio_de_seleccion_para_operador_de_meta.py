from conversaciones.models.enlace_con_meta import EnlaceConMeta
from whatsapp.meta.models.operador import GrupoOperadores


class CriterioDeSeleccionParaOperadorDeMeta(object):

    def seleccionar_para_prospecto(self, prospecto):
        vendedor = prospecto.obtener_vendedor()
        concesionaria = vendedor.obtener_concesionaria()
        grupos_de_operadores = GrupoOperadores.objects.de_concesionaria(concesionaria=concesionaria)
        operadores = grupos_de_operadores.operadores()

        if operadores.exists() is None:
            return None
        else:
            operadores = operadores.activos().excluir_entrantes().ordenar_de_antiguo_a_reciente()
            enlaces = EnlaceConMeta.objects.con_prospecto(prospecto=prospecto).desde_operadores(lista_de_operadores=operadores)
            if not enlaces.exists():
                return operadores.first()
            else:
                return enlaces.first().obtener_operador()