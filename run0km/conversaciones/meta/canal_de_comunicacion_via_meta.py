from django.core.exceptions import ValidationError
from django.utils import timezone

from conversaciones.models import MensajeDeWhatsappConProspecto, Conversacion
from core.date_helper import DatetimeHelper
from lib.api_client import ClientConnectionError
from whatsapp.meta.models.conversation_in_meta import ConversationInMeta
from whatsapp.meta.models.message_in_meta import MessageInMeta
from whatsapp.meta.models.meta_template import MetaTemplate
from whatsapp.meta.models.operador import Operador


class CanalDeComunicacionDeWhatsappViaMeta:

    def __init__(self, servicio_de_meta, logger):
        self._servicio_de_meta = servicio_de_meta
        self._logger = logger
        self._meta_template, _ = MetaTemplate.objects.get_or_create(
            nombre="saludo1",
            contenido="Hola! Vi que te interesa obtener información sobre el Fiat. Te puedo hablar ahora?")
        self._operador, _ = Operador.objects.get_or_create(id_operador="458578827350094",
                                                           defaults={"nombre": "Test CRM", "activo": True})

    @classmethod
    def nuevo(cls, servicio_de_meta, logger=None):
        return cls(servicio_de_meta, logger)


    def enviar_mensaje_a(self, prospecto, texto, telefono_destinatario):
        conversacion = self._conversacion_con(telefono_destinatario)

        # No existe conversacion
        if conversacion is None:
            self._enviar_mensaje_para_empezar_conversacion(prospecto, texto, telefono_destinatario)

        # Existe conversacion y esta la ventana abierta
        elif conversacion.is_open():
            self._enviar_mensaje_a(prospecto, texto, telefono_destinatario)

        # Existe conversacion y ya enviamos template, pero esto no verifica de cuando es la conversacion
        elif conversacion.has_template_sent() and not conversacion.is_expired():
            self._agregar_mensaje_pendiente(conversacion, prospecto, telefono_destinatario, texto)

        else:
            self._enviar_mensaje_para_empezar_conversacion(prospecto, texto, telefono_destinatario)

    def _enviar_mensaje_para_empezar_conversacion(self, prospecto, texto, telefono_destinatario):
        nombre_del_template = self._meta_template.obtener_nombre()
        try:
            respuesta = self._servicio_de_meta.iniciar_conversacion(telefono_destinatario=telefono_destinatario,
                                                                    marca=prospecto.obtener_marca().nombre(),
                                                                    dni=prospecto.obtener_valor_campo_extra('DNI', ''),
                                                                    id_operador=self._operador.obtener_id_operador(),
                                                                    nombre_template=nombre_del_template)
        except ClientConnectionError as error:
            self._log_error(error)
        else:
            id_de_meta = respuesta['messages'][0]['id']
            status_en_meta = respuesta['messages'][0]['message_status']
            conversacion = ConversationInMeta.new(self._operador)
            MessageInMeta.new_template(meta_id=id_de_meta, status=status_en_meta, template_name=nombre_del_template,
                                       phone_number=telefono_destinatario, conversation=conversacion)
            self._agregar_mensaje_pendiente(conversacion, prospecto, telefono_destinatario, texto)

    def _agregar_mensaje_pendiente(self, conversacion, prospecto, telefono_destinatario, texto):
        mensaje_meta = conversacion.add_pending_message(telefono_destinatario, texto)
        self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto, MensajeDeWhatsappConProspecto.VENDEDOR)

    def _enviar_mensaje_a(self, prospecto, texto, telefono_destinatario):
        try:
            respuesta = self._servicio_de_meta.enviar_mensaje(telefono_destinatario=telefono_destinatario,
                                                              marca=prospecto.obtener_marca().nombre(),
                                                              dni=prospecto.obtener_valor_campo_extra('DNI', ''),
                                                              id_operador=self._operador.obtener_id_operador(),
                                                              texto=texto)
        except ClientConnectionError as error:
            self._log_error(error)
        else:
            id_de_meta = respuesta['messages'][0]['id']
            conversacion = ConversationInMeta.new(self._operador)
            mensaje_meta = MessageInMeta.new_sent(meta_id=id_de_meta, phone_number=telefono_destinatario,
                                                  conversation=conversacion, text=texto)

            self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto, MensajeDeWhatsappConProspecto.VENDEDOR)

    def actualizar_desde(self, notificacion_de_meta):
        if not notificacion_de_meta.get('mensaje'):
            mensaje_meta = self._actualizar_estado_desde(notificacion_de_meta)
            self._actualizar_conversacion_desde(mensaje_meta.conversation(), notificacion_de_meta)
        else:
            mensaje_meta = self._actualizar_respuestas_desde(notificacion_de_meta)
        self._enviar_mensajes_pendientes_si_la_ventana_esta_abierta(mensaje_meta.conversation())

    def _crear_nuevo_mensaje_a_prospecto(self, mensaje_meta, prospecto, emisor):
        mensaje = MensajeDeWhatsappConProspecto.nuevo_desde_meta(prospecto=prospecto, mensaje_meta=mensaje_meta,
                                                                 emisor=emisor)
        Conversacion.nuevo_mensaje(mensaje)

    def tiene_un_mensaje_con_el_id_de_meta(self, id_de_mensaje_en_meta):
        return MessageInMeta.objects.with_meta_id(id_de_mensaje_en_meta).exists()

    def mensaje_con_el_id_de_meta(self, id_de_mensaje_en_meta):
        # Investigar si Meta nos garantiza que el ID sea unico. En tal caso usar unique=True en el campo y un get.
        mensaje = MessageInMeta.objects.with_meta_id(id_de_mensaje_en_meta).last()
        if mensaje is None:
            raise ValueError('No se encontró el mensaje con el id de meta {}'.format(id_de_mensaje_en_meta))
        return mensaje

    def mensaje_con_telefono(self, telefono):
        return MessageInMeta.objects.with_phone_number(telefono).last()

    def tiene_un_mensaje_en_espera_con_texto(self, texto):
        return MessageInMeta.objects.pending_messages().with_text(texto).exists()

    def _actualizar_estado_desde(self, notificacion_de_cambio_de_estado):
        id_meta = self._obtener_id_mensaje_meta(notificacion_de_cambio_de_estado)
        status = notificacion_de_cambio_de_estado['status']
        mensaje_meta = self.mensaje_con_el_id_de_meta(id_meta)
        try:
            mensaje_meta.change_status_to(status=status)
        except ValidationError as err:
            pass
        return mensaje_meta

    def _actualizar_conversacion_desde(self, conversacion, notificacion_de_cambio_de_estado):
        notificacion = notificacion_de_cambio_de_estado.get('conversation')
        if notificacion is not None:
            conversacion_meta_id = notificacion['id']
            expiration_datetime = self._fecha_de_expiracion_desde(notificacion)
            conversacion.change_expiration_datetime(expiration_datetime=expiration_datetime)
            conversacion.change_meta_id(meta_id=conversacion_meta_id)

    def _fecha_de_expiracion_desde(self, notificacion):
        expiration_datetime_string = notificacion['expiration_timestamp']
        expiration_timestamp = int(expiration_datetime_string)
        calendario = DatetimeHelper()
        return calendario.from_timestamp(expiration_timestamp, timezone=timezone.utc)

    def _obtener_id_mensaje_meta(self, notificacion_de_cambio_de_estado):
        # id = notificacion_de_cambio_de_estado['entry'][0]['changes'][0]['value']['statuses'][0]['id'] deprecado
        id = notificacion_de_cambio_de_estado['id']
        return id

    def _actualizar_respuestas_desde(self, notificacion_del_endpoint_meta):
        telefono_prospecto = notificacion_del_endpoint_meta.get('telefono')
        prospecto_en_el_crm = self._obtener_prospecto(telefono_prospecto)
        texto_mensaje_prospecto = notificacion_del_endpoint_meta.get('mensaje')
        mensaje_con_telefono_de_prospecto = MessageInMeta.objects.with_phone_number(
            phone_number=prospecto_en_el_crm.telefono).last()
        if mensaje_con_telefono_de_prospecto:
            conversacion_prospecto = mensaje_con_telefono_de_prospecto.conversation()
            mensaje_meta = MessageInMeta.new_received(meta_id=None, status=MessageInMeta.RECEIVED,
                                                      phone_number=telefono_prospecto,
                                                      conversation=conversacion_prospecto, text=texto_mensaje_prospecto)
        else:
            operador_meta = notificacion_del_endpoint_meta.get('AtUrl')
            conversacion_prospecto = ConversationInMeta.new(operator=operador_meta)
            mensaje_meta = MessageInMeta.new_received(meta_id=None, status=MessageInMeta.RECEIVED,
                                                      phone_number=telefono_prospecto,
                                                      conversation=conversacion_prospecto, text=texto_mensaje_prospecto)
        self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto_en_el_crm, MensajeDeWhatsappConProspecto.CLIENTE)
        return mensaje_meta

    def _obtener_prospecto(self, telefono_prospecto):
        from prospectos.models import Prospecto
        return Prospecto.objects.con_algun_telefono_extra(telefonos=self._variantes_de_telefono(telefono_prospecto)).last()

    def _variantes_de_telefono(self, telefono_prospecto):
        telefono_sin_caracteristica_de_pais = telefono_prospecto.replace('549', '')
        return [telefono_prospecto, telefono_sin_caracteristica_de_pais]

    def _enviar_mensajes_pendientes_si_la_ventana_esta_abierta(self, conversacion_meta):
        if conversacion_meta.is_open():
            self._enviar_mensajes_pendientes_de_la_conversacion(conversacion_meta)

    def _enviar_mensajes_pendientes_de_la_conversacion(self, conversacion_meta):
        mensajes_a_enviar = conversacion_meta.pending_messages()
        for mensaje_a_enviar in mensajes_a_enviar:
            self._enviar_mensaje_pendiente(mensaje_a_enviar)

    def _enviar_mensaje_pendiente(self, mensaje_a_enviar):
        try:
            respuesta = self._servicio_de_meta.enviar_mensaje(
                telefono_destinatario=mensaje_a_enviar.phone_number(),
                texto=mensaje_a_enviar.text(), marca=None, dni=None,
                id_operador=mensaje_a_enviar.conversation().operator().obtener_id_operador())
        except ClientConnectionError as error:
            self._log_error(error)
        else:
            id_de_meta = respuesta['messages'][0]['id']
            mensaje_a_enviar.change_status_to(mensaje_a_enviar.ACCEPTED)
            mensaje_a_enviar.change_meta_id(id_de_meta)

    def _conversacion_con(self, telefono_destinatario):
        message = MessageInMeta.objects.with_phone_number(
                phone_number=telefono_destinatario).last()

        return message.conversation() if message else None

    def _log_error(self, error):
        if self._logger is not None:
            self._logger.error(error)
