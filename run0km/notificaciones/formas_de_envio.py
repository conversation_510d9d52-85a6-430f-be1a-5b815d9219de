# coding=utf-8
from notificaciones.errores import FormaDeEnvioDeNotificacionExcepcion
from notificaciones.servicios_adapter import EnviadorDe<PERSON>hatsapp, EnviadorDeEmail


class FormaDeEnvioDeNotificacion(object):
    def __init__(self, emisor_de_mensaje=None):
        super(FormaDeEnvioDeNotificacion, self).__init__()
        self._emisor_de_mensaje = emisor_de_mensaje

    def emisor_de_mensaje(self):
        return self._emisor_de_mensaje or self.__class__.emisor_de_mensaje_por_defecto()

    def configuracion_para(self, contenido):
        return {}

    def enviar(self, notificacion):
        contenido = notificacion.contenido_para(forma_de_envio=self)
        mensajes = contenido.mensajes()
        configuracion = self.configuracion_para(contenido)
        try:
            self.emisor_de_mensaje().send(messages=mensajes, configuration=configuracion)
        except Exception as exc:
            raise FormaDeEnvioDeNotificacionExcepcion.envio_fallido(str(exc))

    def __hash__(self):
        return hash(self.__class__)

    def __eq__(self, other):
        return self.__class__ == other.__class__

    def mensaje_builder(self):
        return self.emisor_de_mensaje().mensaje_builder()

    @classmethod
    def emisor_de_mensaje_por_defecto(cls):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def default(cls):
        return cls()

    @classmethod
    def usando(cls, emisor_de_mensaje):
        # TODO: queda pendiente validar que el emisor es valido para la forma de envio
        return cls(emisor_de_mensaje)

    @classmethod
    def de_nombre(cls, nombre):
        subclases = cls.__subclasses__()
        for each in subclases:
            if each.tiene_nombre(nombre):
                return each
        raise ValueError('Tipo incorrecto: %s' % nombre)

    @classmethod
    def tiene_nombre(cls, nombre):
        return cls.nombre() == nombre

    @classmethod
    def nombre(cls):
        raise NotImplementedError('Subclass responsibility')


class FormaDeEnvioEmail(FormaDeEnvioDeNotificacion):

    @classmethod
    def emisor_de_mensaje_por_defecto(cls):
        return EnviadorDeEmail()

    @classmethod
    def nombre(cls):
        return 'email'


class FormaDeEnvioSMS(FormaDeEnvioDeNotificacion):

    @classmethod
    def emisor_de_mensaje_por_defecto(cls):
        # TODO: quitar esta dependencia
        from occ.occ_notificaciones.notificaciones_adapter import EnviadorDeSMS
        return EnviadorDeSMS.cliente_por_defecto()

    @classmethod
    def nombre(cls):
        return 'sms'

    @classmethod
    def sanitizar_texto(cls, texto):
        from occ.occ_notificaciones.notificaciones_adapter import EnviadorDeSMS
        return EnviadorDeSMS.sanitizar_texto(texto)


class FormaDeEnvioWhatsapp(FormaDeEnvioDeNotificacion):

    @classmethod
    def emisor_de_mensaje_por_defecto(cls):
        return EnviadorDeWhatsapp()

    def configuracion_para(self, contenido):
        return {
            'token': contenido.token()
        }

    @classmethod
    def nombre(cls):
        return 'whatsapp'
