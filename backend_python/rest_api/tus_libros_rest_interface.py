import uuid

from model.month_of_year import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_api.http_response import HttpResponse


class TusLibrosRestInterface:
    @classmethod
    def for_system(cls, system):
        return cls(system)

    def __init__(self, system):
        self._system = system
        self._users = {}

    def create_cart(self, an_http_request):
        user_id = an_http_request.fields()['userId']
        password = an_http_request.fields()['password']

        def response_body():
            cart_id = self._system.create_cart_for(user_id, password)
            self._users[user_id] = cart_id
            return f'0|{cart_id}'

        return self._answer(lambda: response_body())

    def add_to_cart(self, an_http_request):
        book_isbn = an_http_request.fields()['bookIsbn']
        book_quantity = int(an_http_request.fields()['bookQuantity'])

        def response_body():
            cart_id = self._cart_id_from(an_http_request)
            self._system.add_to_cart(cart_id, book_isbn, book_quantity)
            return f'0|OK'

        return self._answer(lambda: response_body())

    def list_cart(self, an_http_request):

        def response_body():
            cart_id = self._cart_id_from(an_http_request)
            cart_contents = self._system.list_cart(cart_id)
            return f'0|{self._bag_as_string(cart_contents)}'

        return self._answer(lambda: response_body())

    def checkout_cart(self, an_http_request):
        ccn = an_http_request.fields()['ccn']
        cced = an_http_request.fields()['cced']
        cco = an_http_request.fields()['cco']

        expiration_date = MonthOfYear(int(cced[:2]), int(cced[2:]))

        def response_body():
            cart_id = self._cart_id_from(an_http_request)
            transaction_id = self._system.checkout_cart(cart_id, ccn, expiration_date, cco)
            return f'0|{transaction_id}'

        return self._answer(lambda: response_body())

    def list_purchases(self, an_http_request):
        user_id = an_http_request.fields()['userId']
        password = an_http_request.fields()['password']

        def response_body():
            purchases_info = self._system.list_purchases(user_id, password)
            return f'0|{self._purchases_info_as_string(purchases_info)}'

        return self._answer(lambda: response_body())

    'private'

    def _answer(self, a_block):
        try:
            response_body = a_block()
        except RuntimeError as exception:
            return HttpResponse.ok_response_with(f'1|{str(exception)}')

        return HttpResponse.ok_response_with(response_body)

    def _cart_id_from(self, an_http_request):
        try:
            user_id = an_http_request.fields()['userId']
            return self._users[user_id]
        except KeyError:
            raise RuntimeError("Invalid UUID")

    'private - serialization to string'

    def _bag_as_string(self, a_bag):
        string_result = ''
        bag_as_set = set(a_bag)

        for item_number, item in enumerate(bag_as_set):
            string_result += f'{item}|{a_bag.count(item)}'
            if item_number < len(bag_as_set) - 1:
                string_result += '|'

        return string_result

    def _purchases_info_as_string(self, purchase_info):
        sold_items = purchase_info[0]
        total = purchase_info[1]

        return f'{self._bag_as_string(sold_items)}|{total}'
