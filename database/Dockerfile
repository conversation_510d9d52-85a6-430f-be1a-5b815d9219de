FROM mysql:8.0

COPY 01_deliveryrun-structure.sql.tar.gz /
RUN tar -xvf /01_deliveryrun-structure.sql.tar.gz -C /docker-entrypoint-initdb.d/
RUN rm /01_deliveryrun-structure.sql.tar.gz

COPY 02_db-data.sql.tar.gz /
RUN tar -xvf /02_db-data.sql.tar.gz -C /docker-entrypoint-initdb.d/
RUN rm /02_db-data.sql.tar.gz


# Check max_allowed_packet for production
CMD ["--character-set-server=utf8mb4", "--collation-server=utf8mb4_unicode_ci", "--max_allowed_packet=268435456"]
