import json
from flask import Flask, request
from datetime import datetime

app = Flask(__name__)


@app.route('/', methods=['POST'])
def receive_post_request():
    content = request.get_json()  # Obtiene el contenido JSON de la solicitud

    # Genera el nombre del archivo utilizando la hora actual
    now = datetime.now()
    filename = now.strftime("%Y-%m-%d_%H-%M-%S-%f") + ".txt"

    # Guarda el contenido en el archivo
    with open(filename, 'w') as file:
        file.write(json.dumps(content, indent=4))

    return 'Solicitud recibida correctamente'


if __name__ == '__main__':
    app.run()
