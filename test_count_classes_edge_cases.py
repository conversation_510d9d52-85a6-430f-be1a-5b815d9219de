#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil
from unittest.mock import patch, mock_open

from count_classes import count_classes_in_file, scan_directory

class TestCountClassesEdgeCases(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper method to create test files with specific content"""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_nested_classes(self):
        """Test counting nested classes"""
        content = """
class OuterClass:
    class InnerClass:
        pass
    
    def method(self):
        class MethodClass:
            pass
"""
        file_path = self.create_test_file('nested.py', content)
        # The current implementation should find all 3 classes
        self.assertEqual(count_classes_in_file(file_path), 3)
    
    def test_class_in_conditional(self):
        """Test classes defined inside conditional statements"""
        content = """
if True:
    class ConditionalClass:
        pass

def function():
    if False:
        class AnotherConditionalClass:
            pass
"""
        file_path = self.create_test_file('conditional.py', content)
        self.assertEqual(count_classes_in_file(file_path), 2)
    
    def test_class_with_unusual_spacing(self):
        """Test classes with unusual spacing"""
        content = """
class    SpacedClass   :
    pass

class\tTabClass\t:
    pass

class NoSpaceClass:pass
"""
        file_path = self.create_test_file('spacing.py', content)
        # Current regex might not catch all of these
        result = count_classes_in_file(file_path)
        self.assertEqual(result, 3)
    
    def test_file_with_syntax_error(self):
        """Test a file with Python syntax errors"""
        content = """
class ValidClass:
    pass

class InvalidClass
    def broken_method(self):
        pass
"""
        file_path = self.create_test_file('syntax_error.py', content)
        # Should still find the valid class
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_file_with_unicode(self):
        """Test a file with Unicode characters"""
        content = """
class NormalClass:
    pass

class UnicodeClass_áéíóú:
    pass
"""
        file_path = self.create_test_file('unicode.py', content)
        self.assertEqual(count_classes_in_file(file_path), 2)
    
    def test_file_read_error(self):
        """Test handling of file read errors"""
        with patch('builtins.open', side_effect=IOError("Mocked error")):
            with patch('builtins.print') as mock_print:
                result = count_classes_in_file("nonexistent.py")
                self.assertEqual(result, 0)
                mock_print.assert_called_once()
    
    def test_scan_directory_permission_error(self):
        """Test handling of permission errors during directory scan"""
        with patch('os.walk', side_effect=PermissionError("Permission denied")):
            total, file_counts = scan_directory("/some/path")
            self.assertEqual(total, 0)
            self.assertEqual(len(file_counts), 0)
    
    def test_class_with_metaclass(self):
        """Test counting classes with metaclass specifications"""
        content = """
class Meta(type):
    pass

class ClassWithMeta(metaclass=Meta):
    pass

class OldStyleMeta:
    __metaclass__ = Meta
"""
        file_path = self.create_test_file('metaclass.py', content)
        self.assertEqual(count_classes_in_file(file_path), 3)

if __name__ == '__main__':
    unittest.main()
